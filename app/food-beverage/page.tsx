import { redirect } from 'next/navigation';
import { CustomerTemplate } from '@/components/Templates/CustomerTemplate';
import { Locale } from '@/i18n-config';
import { EFoodBeverageRoute } from '@/lib/types/enum/food-beverage';
import {
  FoodBeverageContainer,
  TFoodBeverageTabs,
} from '@/components/Organisms/FoodBeverage/FoodBeverageContainer/FoodBeverageContainer';
import { EResourceAccess } from '@/lib/types/enum/role';

const FoodBeveragePage = ({
  params,
  searchParams: { tab },
}: {
  params: {
    lang: Locale;
  };
  searchParams: { tab: TFoodBeverageTabs };
}) => {
  if (!tab) redirect(`${EFoodBeverageRoute.HOME}?tab=${EResourceAccess['FNB_FOOD']}`);
  return <CustomerTemplate title="F&B" body={<FoodBeverageContainer />} />;
};

export default FoodBeveragePage;
