import { redirect } from 'next/navigation';
import { DesktopCheckout } from '@/components/Molecules/DesktopCheckout/DesktopCheckout';
import { Locale } from '@/i18n-config';

const CheckoutPage = ({
  params,
  searchParams: { appointmentId, callbackUrl },
}: {
  params: {
    lang: Locale;
  };
  searchParams: { appointmentId: string; callbackUrl: string };
}) => {
  if (!appointmentId) redirect(`/`);

  return <DesktopCheckout callbackUrl={callbackUrl} appointmentId={appointmentId} isOpen />;
};

export default CheckoutPage;
