import { redirect } from 'next/navigation';
import { CustomerTemplate } from '@/components/Templates/CustomerTemplate';
import { Locale } from '@/i18n-config';
import { AdminContainer, TAdminTabs } from '@/components/Organisms/Admin/AdminContainer/AdminContainer';
import { EAdminRoute } from '@/lib/types/enum/admin';
import { EResourceAccess } from '@/lib/types/enum/role';

const AdminPage = ({
  params,
  searchParams: { tab },
}: {
  params: {
    lang: Locale;
  };
  searchParams: { tab: TAdminTabs };
}) => {
  if (!tab) redirect(`${EAdminRoute.HOME}?tab=${EResourceAccess['ADMIN_USER']}`);
  return <CustomerTemplate title="ADMIN" body={<AdminContainer />} />;
};

export default AdminPage;
