import { Metadata } from 'next';

import GlobalLayoutWrapper from '@/components/Templates/GlobalLayoutWrapper/GlobalLayoutWrapper';
import { Locale, i18n } from '@/i18n-config';
import { onSeverSession } from '@/lib/data/auth';
import ThemeRegistry from '@/theme/ThemeRegistry';
import { CustomProvider } from '../lib/provider/CustomProvider';

export async function generateStaticParams() {
  return i18n.locales.map(locale => ({ lang: locale }));
}

export const metadata: Metadata = {
  openGraph: {
    images: ['/assets/images/logo.svg'],
  },
};

const RootLayout = async ({
  children,
  params: { lang },
}: {
  children: React.ReactNode;
  params: {
    lang: Locale;
  };
}) => {
  const ssrSession = await onSeverSession();

  return (
    <html lang={lang}>
      <ThemeRegistry>
        <body style={{ minWidth: '100vw', minHeight: '100vh' }}>
          <CustomProvider session={ssrSession}>
            <GlobalLayoutWrapper>{children}</GlobalLayoutWrapper>
          </CustomProvider>
        </body>
      </ThemeRegistry>
    </html>
  );
};

export default RootLayout;
