import { KitchenContainer } from '@/components/Organisms/Kitchen/KitchenContainer/KitchenContainer';
import { CustomerTemplate } from '@/components/Templates/CustomerTemplate';
import { Locale } from '@/i18n-config';

const KitchenPage = ({
  params: { lang },
}: {
  params: {
    lang: Locale;
  };
}) => <CustomerTemplate title="KITCHEN / BAR" body={<KitchenContainer />} />;

export default KitchenPage;
