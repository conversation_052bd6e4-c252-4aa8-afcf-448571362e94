import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@/lib/clients/client';

export async function GET(req: NextRequest) {
  const { url } = req;

  const CustomerClient = new Client({ url });

  const response = await CustomerClient.GET();
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data }, { status: response.status });

  return NextResponse.json(data);
}
