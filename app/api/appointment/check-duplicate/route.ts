import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@/lib/clients/client';
import { onSeverSession } from '@/lib/data/auth';

export async function POST(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });

  const { url } = req;
  const body = await req.json();

  const currentSession = await onSeverSession();
  const branchId = currentSession?.user?.branchChosen?.id || '';

  const AppointmentClient = new Client({ url, token: token.accessToken });
  const response = await AppointmentClient.POST(JSON.stringify({ ...body, branch: { id: branchId } }));
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data?.message || data }, { status: response.status });
  return NextResponse.json(data);
}
