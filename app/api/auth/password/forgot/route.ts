import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@/lib/clients/client';

export async function POST(req: NextRequest) {
  const { url } = req;
  const body = await req.json();

  const AuthClient = new Client({ url });
  const response = await AuthClient.POST(JSON.stringify(body));
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data?.message || data }, { status: response.status });
  return NextResponse.json(data);
}
