import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@/lib/clients/client';

export async function POST(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });

  const { url, headers } = req;
  const body = await req.json();
  const starttime = headers.get('starttime');

  const CustomerClient = new Client({ url, token: token.accessToken, header: starttime ? { starttime } : {} });
  const response = await CustomerClient.POST(JSON.stringify(body));
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data?.message || data }, { status: response.status });
  return NextResponse.json(data);
}
