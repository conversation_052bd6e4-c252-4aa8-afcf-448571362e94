import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@/lib/clients/client';

export async function POST(req: NextRequest) {
  const { url, headers } = req;
  const body = await req.json();

  const Order = new Client({ url, header: headers });

  const response = await Order.POST(JSON.stringify(body));
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data }, { status: response.status });

  return NextResponse.json(data);
}
