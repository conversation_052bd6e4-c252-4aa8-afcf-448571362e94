import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@/lib/clients/client';

export async function GET(req: NextRequest) {
  // const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  // if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });

  const { url } = req;

  const ProductCategory = new Client({ url });

  const response = await ProductCategory.GET();
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data }, { status: response.status });

  return NextResponse.json(data);
}

export async function POST(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });

  const { url } = req;
  const body = await req.json();

  const ProductCategory = new Client({ url, token: token.accessToken });
  const response = await ProductCategory.POST(JSON.stringify(body));
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data?.message || data }, { status: response.status });
  return NextResponse.json(data);
}
