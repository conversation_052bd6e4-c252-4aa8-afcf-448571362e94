import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@/lib/clients/client';
import { onSeverSession } from '@/lib/data/auth';
import { EInvoiceAPI } from '@/lib/types/enum/invoice';

export async function GET(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });

  const { url } = req;

  const InvoiceClient = new Client({ url, token: token.accessToken });

  const response = await InvoiceClient.GET();
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data }, { status: response.status });

  return NextResponse.json(data);
}

export async function POST(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });

  const { url, headers } = req;
  const body = await req.json();
  const starttime = headers.get('starttime');
  const currentSession = await onSeverSession();
  const branchId = currentSession?.user?.branchChosen?.id || '';
  const InvoiceClient = new Client({ url, token: token.accessToken, header: starttime ? { starttime } : {} });
  const response = await InvoiceClient.POST(JSON.stringify({ ...body, branch: { id: branchId } }));
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data?.message || data }, { status: response.status });

  const InvoiceDetailClient = new Client({
    url: `${EInvoiceAPI.API_SWR_PATH}/${data?.id}?join=appointment&join=customer`,
    token: token.accessToken,
  });
  const detailInvoiceResponse = await InvoiceDetailClient.GET();
  const detailInvoiceData = await detailInvoiceResponse.json();
  return NextResponse.json(detailInvoiceData);
}
