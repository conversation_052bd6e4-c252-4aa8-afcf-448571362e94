import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@/lib/clients/client';

export async function GET(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });

  const { url } = req;

  const CouponClient = new Client({ url, token: token.accessToken });

  const response = await CouponClient.GET();
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data }, { status: response.status });

  return NextResponse.json(data);
}

export async function PATCH(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });

  const { url } = req;
  const body = await req.json();

  const CouponClient = new Client({ url, token: token.accessToken });

  const response = await CouponClient.PATCH(JSON.stringify(body));
  const data = await response.json();
  if (!response.ok) return NextResponse.json({ error: data }, { status: response.status });

  return NextResponse.json(data);
}

export async function DELETE(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });

  const { url } = req;

  const CouponClient = new Client({ url, token: token.accessToken });
  const response = await CouponClient.DELETE();
  if (!response.ok) {
    const data = await response.json();
    return NextResponse.json({ error: data }, { status: response.status });
  }

  return NextResponse.json('Deleted !');
}
