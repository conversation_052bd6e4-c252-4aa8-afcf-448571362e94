import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@/lib/clients/client';
import { ECreditType } from '@/lib/types/enum/credit';

export async function POST(req: NextRequest) {
  try {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
    if (!token?.accessToken) return NextResponse.json({ error: 'missing accessToken' }, { status: 400 });
    const { url } = req;
    const body = await req.json();

    const { type } = body;
    const MembershipClient = new Client({ url, token: token.accessToken });
    const response = await MembershipClient.POST(JSON.stringify(type === ECreditType.NEW_CREDIT ? body : {}));

    const data = await response.json();
    if (!response.ok) return NextResponse.json({ error: data?.message || data }, { status: response.status });
    return NextResponse.json(data);
  } catch (e) {
    return NextResponse.json({ error: 'Something wrong!' }, { status: 400 });
  }
}
