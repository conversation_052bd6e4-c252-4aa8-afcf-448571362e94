import { redirect } from 'next/navigation';
import { EmployeeContainer, TEmployeeTabs } from '@/components/Organisms/Employee/EmployeeContainer/EmployeeContainer';
import { CustomerTemplate } from '@/components/Templates/CustomerTemplate';
import { Locale } from '@/i18n-config';
import { EEmployeeRoute } from '@/lib/types/enum/employee';
import { EResourceAccess } from '@/lib/types/enum/role';

const StaffPage = ({
  params,
  searchParams: { tab },
}: {
  params: {
    lang: Locale;
  };
  searchParams: { tab: TEmployeeTabs };
}) => {
  if (!tab) redirect(`${EEmployeeRoute.HOME}?tab=${EResourceAccess['WORKING_HOURS']}`);
  return <CustomerTemplate title="THERAPIST" body={<EmployeeContainer />} />;
};

export default StaffPage;
