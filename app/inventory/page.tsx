import { redirect } from 'next/navigation';
import {
  InventoryContainer,
  TInventoryTabs,
} from '@/components/Organisms/Inventory/InventoryContainer/InventoryContainer';
import { CustomerTemplate } from '@/components/Templates/CustomerTemplate';
import { Locale } from '@/i18n-config';
import { EInventoryRoute } from '@/lib/types/enum/inventory';
import { EResourceAccess } from '@/lib/types/enum/role';

const InventoryPage = ({
  params,
  searchParams: { tab },
}: {
  params: {
    lang: Locale;
  };
  searchParams: { tab: TInventoryTabs };
}) => {
  if (!tab) redirect(`${EInventoryRoute.HOME}?tab=${EResourceAccess['ISSUE_COUPON']}`);
  return <CustomerTemplate title="COUPON" body={<InventoryContainer />} />;
};

export default InventoryPage;
