import { redirect } from 'next/navigation';
import { CustomerTemplate } from '@/components/Templates/CustomerTemplate';
import { Locale } from '@/i18n-config';
import { SaleContainer, TSaleTabs } from '@/components/Organisms/Sales/SaleContainer/SaleContainer';
import { ESaleRoute } from '@/lib/types/enum/sale';

const SalePage = ({
  params,
  searchParams: { tab },
}: {
  params: {
    lang: Locale;
  };
  searchParams: { tab: TSaleTabs };
}) => {
  if (!tab) redirect(`${ESaleRoute.HOME}?tab=daily-sale`);
  return <CustomerTemplate title="SALES" body={<SaleContainer />} />;
};

export default SalePage;
