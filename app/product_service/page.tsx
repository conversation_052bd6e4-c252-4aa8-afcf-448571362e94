import { redirect } from 'next/navigation';
import {
  ProductAndServiceContainer,
  TProductTabs,
} from '@/components/Organisms/ProductAndService/ProductAndServiceContainer';
import { CustomerTemplate } from '@/components/Templates/CustomerTemplate';
import { Locale } from '@/i18n-config';
import { EProductAndServiceRoutes } from '@/lib/types/enum/product_service';
import { EResourceAccess } from '@/lib/types/enum/role';

const ProductServicePage = ({
  params,
  searchParams: { tab },
}: {
  params: {
    lang: Locale;
  };
  searchParams: { tab: TProductTabs };
}) => {
  if (!tab) redirect(`${EProductAndServiceRoutes.HOME}?tab=${EResourceAccess['PRODUCT']}`);
  return <CustomerTemplate title="PRODUCT & SERVICES" body={<ProductAndServiceContainer />} />;
};

export default ProductServicePage;
