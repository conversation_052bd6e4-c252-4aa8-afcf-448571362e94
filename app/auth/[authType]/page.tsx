import { Stack } from '@mui/material';
import { Auth } from '@/components/Organisms/Auth';
import { TFormTypes } from '@/components/Organisms/Auth/Auth.types';
import { Locale } from '@/i18n-config';
import { customPalettes } from '@/theme/customPalettes';

const LoginPage = ({
  params: { lang, authType },
}: {
  params: {
    lang: Locale;
    authType: TFormTypes;
  };
}) => (
  <>
    <Stack bgcolor={customPalettes.neutrals?.N20.main} width="100vw" height="100vh">
      <Auth authType={authType} />
    </Stack>
  </>
);

export default LoginPage;
