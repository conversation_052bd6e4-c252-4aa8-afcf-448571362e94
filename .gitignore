# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

#storybook-buid
storybook-static
build-storybook*

# dependencies
/node_modules
/.pnp
.pnp.js

#IDE files
.idea
.vscode

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
._*
.env
.env.local
.env.production

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

package-lock.json
# yarn.lock
yarn-error.lock



# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store

# Task files
# tasks.json
# tasks/

.clinerules
.cursor
.github
.roo
.taskmaster
.trae
.windsurf
.roomodes
AGENTS.md
CLAUDE.md