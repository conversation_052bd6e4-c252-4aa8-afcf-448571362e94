version: 2.1

jobs:
  build_prod:
    docker:
      - image: cimg/node:18.17.1-browsers
    resource_class: large
    steps:
      - run:
          shell: /bin/sh
          command: |
            sudo apt update
            sudo apt install git -y
            git --version
      - run:
          name: Install rsync
          command: |
            sudo apt-get install rsync
      - checkout
      - restore_cache:
          keys:
            - yarn-packages-{{ checksum "yarn.lock" }}
      - run:
          name: Checkout production branch
          command: |
            git checkout production     
            git pull origin production
      - run:
          name: Install dependencies
          command: |
            yarn
      - save_cache:
          paths:
            - node_modules
          key: yarn-packages-{{ checksum "yarn.lock" }}
      - run:
          name: Create .env file
          command: |
            echo "API_URL=${API_URL}" >> .env
            echo "NEXTAUTH_SECRET=${NEXTAUTH_SECRET}" >> .env
            echo "BASE_URL=${BASE_URL}" >> .env
            echo "NEXTAUTH_URL=${NEXTAUTH_URL}" >> .env
      - run:
          name: Build
          command: |
            yarn build:standalone
      - add_ssh_keys
      - run:
          name: Add the server to known hosts
          command: |
            ssh-keyscan -H ${SERVER_PROD} >> ~/.ssh/known_hosts
      - run:
          name: Upload files to stand alone files to server
          command: |
            rsync -avce ssh --delete  --exclude 'ecosystem.config.js' ./.next/standalone/. ubuntu@${SERVER_PROD}:~/botg-webapp
      - run:
          name: Run migration and restart server
          command: |
            ssh ubuntu@${SERVER_PROD} "source ~/.nvm/nvm.sh && cd ~/botg-webapp && ls -la && source ~/.bashrc && pm2 restart onsen-fe"

workflows:
  build-and-deploy-prod:
    jobs:
      - build_prod:
          filters:
            branches:
              only: production # only deploy on the main branch
