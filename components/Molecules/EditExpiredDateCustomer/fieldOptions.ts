import { Theme } from '@mui/material';
import { TRHFCalendarProps, TRHFTextFieldProps } from '@/components/Molecules/RHFItems/RHFItems.types';

type TTextfields = 'credit';
type TCalendar = 'validity' | 'passportExpiredDate';

export type TKeyOfForm = TTextfields | TCalendar;

type TRHFTextfieldOption = Record<TTextfields, TRHFTextFieldProps>;
type TRHFCalendarOption = Record<TCalendar, TRHFCalendarProps>;

export type TFieldOptions = TRHFTextfieldOption | TRHFCalendarOption;

export const fieldOptions: TFieldOptions = {
  credit: {
    type: 'textfield',
    containerProps: {
      flex: '20%',
    },
    attributes: {
      name: 'credit',
      label: 'Available Credit',
      disabled: true,
      InputProps: {
        sx: (theme: Theme) => ({
          input: {
            background: theme.palette.neutrals.N100.main,
          },
        }),
      },
    },
  },
  validity: {
    type: 'calendar',
    containerProps: {
      flex: '40%',
    },
    attributes: {
      name: 'validity',
      label: '\u00A0',
    },
  },
  passportExpiredDate: {
    type: 'calendar',
    attributes: {
      name: 'passportExpiredDate',
      label: 'Passport Expired Date',
    },
  },
};
