'use client';

import { Theme } from '@mui/material';
import { dialogClasses } from '@mui/material/Dialog';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import Modal from '@/components/Atoms/Modal';
import RHFItem from '@/components/Molecules/RHFItems/RHFItems';
import { FORMAT_TIME_FULL } from '@/lib/constants/dateTime';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { useMutationCustomerUpdate } from '@/lib/hooks/mutation/customer';
import { useMutationCredit } from '@/lib/hooks/mutation/membership';
import {
  StyledButton,
  StyledButtonWrapper,
  StyledContainer,
  StyledFormContainer,
} from './EditExpiredDateCustomer.styled';
import { TEditExpiredDateCustomerForm, TFormValues } from './EditExpiredDateCustomer.types';
import { TFieldOptions, fieldOptions } from './fieldOptions';

dayjs.extend(utc);
dayjs.extend(timezone);

export const EditExpiredDateCustomer: React.FC<TEditExpiredDateCustomerForm> = ({
  defaultValues,
  onClose,
  isOpen,
  customerId,
  credit,
  mutateCustomerId,
  mutateCustomerSummary,
}) => {
  const { showError, showSuccess } = useAlert();
  const { onUpdateCustomer, isMutating: isMutatingCustomer } = useMutationCustomerUpdate();
  const { onMutationCredit, isMutating: isMutatingCredit } = useMutationCredit(credit?.id);

  const methods = useForm<TFormValues>({
    defaultValues,
  });

  const isLoading = isMutatingCredit || isMutatingCustomer || methods.formState.isSubmitting;

  const handleSubmit = async (values: TFormValues) => {
    const passportExpiredDate = values?.passportExpiredDate
      ? dayjs.utc(values?.passportExpiredDate).format(FORMAT_TIME_FULL)
      : '';
    const validity = values?.validity ? dayjs.utc(values?.validity).format(FORMAT_TIME_FULL) : '';

    if (methods.getFieldState('validity').isDirty && validity) {
      await onMutationCredit({
        id: credit?.id,
        expiryDate: validity,
      });
    }

    if (methods.getFieldState('passportExpiredDate').isDirty && passportExpiredDate) {
      const response = await onUpdateCustomer({
        id: customerId,
        expiryDate: passportExpiredDate,
      });

      if (response.status === 'error') {
        return showError({ title: String(response?.message?.error) });
      }
      showSuccess({ title: 'Updated !' });
    }
    mutateCustomerId();
    mutateCustomerSummary();
    onClose();
  };

  useEffect(() => {
    methods.reset(defaultValues);
    return () => methods.reset({});
  }, [defaultValues]);

  const additionalAttributes = {
    validity: {
      disabled: !defaultValues?.validity,
    },
    passportExpiredDate: {
      disabled: !defaultValues?.passportExpiredDate,
    },
  };

  return (
    <Modal
      isOpen={isOpen}
      handleClose={onClose}
      title="EDIT EXPIRED DATE"
      titleProps={{
        justifyContent: 'center',
      }}
      dialogProps={{
        sx: (theme: Theme) => ({
          [`& .${dialogClasses.paper}`]: {
            maxWidth: theme.spacing(500 / 8),
            height: 'fit-content',
            padding: theme.spacing(3),
            maxHeight: 'calc(100% - 48px)',
            backgroundColor: theme.palette.neutrals.N100.main,
            borderRadius: theme.spacing(1.25),
            [theme.breakpoints.down('md')]: {
              maxWidth: 'auto',
              padding: theme.spacing(2),
            },
          },
        }),
      }}
    >
      <StyledContainer>
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(handleSubmit)}>
            <Stack>
              <StyledFormContainer>
                {(Object.keys(fieldOptions) as Array<keyof TFieldOptions>)?.map(fieldName => {
                  const { type, attributes, containerProps } = fieldOptions[fieldName];
                  return (
                    <RHFItem
                      key={fieldName}
                      type={type}
                      attributes={{
                        ...(attributes as any),
                        ...((additionalAttributes?.[fieldName] as any) || {}),
                      }}
                      containerProps={containerProps}
                    />
                  );
                })}
              </StyledFormContainer>

              <StyledButtonWrapper>
                <StyledButton
                  variant="outlined"
                  label="Cancel"
                  size="large"
                  isLoading={isLoading}
                  loadingText="Cancel"
                  onClick={onClose}
                />
                <StyledButton
                  variant="contained"
                  type="submit"
                  label="Save"
                  size="large"
                  isLoading={isLoading}
                  loadingText="Saving"
                />
              </StyledButtonWrapper>
            </Stack>
          </form>
        </FormProvider>
      </StyledContainer>
    </Modal>
  );
};
