import { KeyedMutator } from 'swr';
import { TCustomer, TCustomerCredit, TCustomerSummary } from '@/lib/types/entities/customer';

export type TFormValues = {
  credit?: number;
  validity?: string;
  passportExpiredDate?: string;
};

export type TEditExpiredDateCustomerForm = {
  defaultValues?: TFormValues;
  isOpen: boolean;
  isLoading?: boolean;
  onClose: () => void;
  customerId: string;
  credit?: TCustomerCredit;
  mutateCustomerId: KeyedMutator<TCustomer>;
  mutateCustomerSummary: KeyedMutator<TCustomerSummary>;
};
