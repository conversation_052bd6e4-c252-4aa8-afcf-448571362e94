import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Button from '@/components/Atoms/Button';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: theme.spacing(64),
  [theme.breakpoints.down('md')]: {
    width: 'auto',
  },
}));

export const StyledFormContainer = styled(Stack)(({ theme }) => ({
  gap: theme.spacing(1.5),
  overflow: 'auto',
  width: '100%',
  margin: '0 auto',
  flexDirection: 'row',
  flexWrap: 'wrap',
  '.Mui-disabled': {
    backgroundColor: theme.palette.neutrals.N100.main,
  },
}));

export const StyledButtonWrapper = styled(Stack)(({ theme }) => ({
  width: '100%',
  justifyContent: 'center',
  alignItems: 'center',
  flexDirection: 'row',
  flexWrap: 'wrap',
  gap: theme.spacing(1.5),
  overflow: 'auto',
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  maxWidth: theme.spacing(23.75),
}));
