import { dialogClasses } from '@mui/material/Dialog';
import Stack from '@mui/material/Stack';
import { Theme } from '@mui/material/styles';
import Modal from '@/components/Atoms/Modal';
import { StyledButton } from '@/components/Organisms/RFID/RFIDForm/RFIDForm.styled';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { useMutationCustomerCheckOut } from '@/lib/hooks/mutation/customer';
import { TCustomer } from '@/lib/types/entities/customer';
import { TApiError } from '@/lib/utils/transformResponse';
import CustomerInfoCashier from '../CustomerInfoCashier';
import { TCustomerInfoRow } from '../CustomerInfoCashier/CustomerInfoCashier';
import { StyledAvatar } from './CustomerModal.styled';

type TCustomerModal = {
  customer?: TCustomer;
  rfid?: string;
  lockerNumber: number;
  isOpen: boolean;
  handleClose: () => void;
  checkinTime: string;
  hasPermissionCheckout: boolean;
  hideFields: TCustomerInfoRow[];
};

export const CustomerModal: React.FC<TCustomerModal> = ({
  rfid,
  customer,
  checkinTime,
  isOpen,
  handleClose,
  lockerNumber,
  hasPermissionCheckout,
  hideFields,
}) => {
  const { showError, showSuccess } = useAlert();
  const { onCustomerCheckOut, isMutating } = useMutationCustomerCheckOut(rfid);

  const onCheckout = async () => {
    if (!hasPermissionCheckout) return;
    const response = await onCustomerCheckOut({ rfid });
    if (response.status === 'error') {
      if (typeof response?.message?.error === 'string') {
        return showError({ title: response?.message?.error || 'Error' });
      }
      if (Array.isArray(response.message.error)) {
        return showError({ title: 'Error' });
      }
      // It's a TApiError
      const apiError = response.message.error as TApiError;
      return showError({ title: apiError?.message || 'Error' });
    }
    showSuccess({ title: 'Checkout success!' });
    handleClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      handleClose={handleClose}
      title=""
      dialogProps={{
        sx: (theme: Theme) => ({
          [`& .${dialogClasses.paper}`]: {
            maxWidth: theme.spacing(126.5),
            height: 'fit-content',
            padding: theme.spacing(3),
            maxHeight: 'calc(100% - 48px)',
            backgroundColor: theme.palette.neutrals.N100.main,
            borderRadius: theme.spacing(1.25),
            [theme.breakpoints.down('md')]: {
              maxWidth: 'auto',
              padding: theme.spacing(2),
            },
          },
        }),
      }}
    >
      <Stack justifyContent="center" alignItems="center" gap={3}>
        <Stack flexDirection="row" gap={2.25} minWidth="480px">
          <StyledAvatar src={customer?.avatar?.url}>{customer?.firstName?.[0]}</StyledAvatar>

          <CustomerInfoCashier
            checkinTime={checkinTime}
            customer={customer}
            showRfid
            rfid={rfid}
            lockerNumber={lockerNumber}
            hideFields={hideFields}
          />
        </Stack>
        <StyledButton
          variant="contained"
          label="Checkout"
          size="large"
          isLoading={isMutating}
          loadingText="Saving"
          disabled={!hasPermissionCheckout}
          onClick={onCheckout}
        />
      </Stack>
    </Modal>
  );
};
