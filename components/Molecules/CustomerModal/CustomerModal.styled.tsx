import styled from '@emotion/styled';
import Avatar, { avatarClasses } from '@mui/material/Avatar';

export const StyledAvatar = styled(Avatar)(({ theme }) => ({
  [`&.${avatarClasses.root}`]: {
    width: theme.spacing(8),
    height: theme.spacing(8),
    textTransform: 'uppercase',
    color: theme.palette.common.white,
    backgroundColor: theme.palette.neutrals.N50.main,
    ...theme.typography['heading-xlarge-700'],
  },
}));
