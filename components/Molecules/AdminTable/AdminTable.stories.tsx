import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import AdminTable from '.';
import { IAdminColumn } from './AdminTable.types';
import { TAdmin } from '@/lib/types/entities/admin';

export default {
  title: 'Molecules/AdminTable',
  component: AdminTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof AdminTable>;

const SAMPLE_ROWS: TAdmin[] = [];

const SAMPLE_COLUMNS: IAdminColumn[] = [
  {
    name: 'username',
    label: 'NAME',
  },
  {
    name: 'branch',
    label: 'BRANCH',
  },
  {
    name: 'email',
    label: 'EMAIL',
  },
  {
    name: 'status',
    label: 'STATUS',
  },
  {
    name: 'actions',
    label: ' ',
  },
];

export const Default: Story = {
  args: {
    loading: false,
    rows: SAMPLE_ROWS,
    columns: SAMPLE_COLUMNS,
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <AdminTable {...args} />
    </Box>
  ),
};
