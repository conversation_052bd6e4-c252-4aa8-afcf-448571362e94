import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { useMemo } from 'react';
import Typography from '@/components/Atoms/Typography';
import { FORMAT_DD_MM_YYYY_SLASH } from '@/lib/constants/dateTime';
import { useSetting } from '@/lib/hooks/queries/setting';
import { TCustomer } from '@/lib/types/entities/customer';
import { getCredits } from '@/lib/utils/credit';
import { round2decimal } from '@/lib/utils/string';
import { StyledCustomerName, StyledTitle } from './CustomerInfoCashier.styled';

dayjs.extend(utc);
dayjs.extend(timezone);

export type TCustomerInfoRow = 'gender' | 'passport' | 'credit';

export type TCustomerInfoCashier = {
  customer?: TCustomer;
  rfid?: string;
  checkinTime?: string;
  lockerNumber: number;
  showRfid?: boolean;
  hideFields?: TCustomerInfoRow[];
};

export const CustomerInfoCashier: React.FC<TCustomerInfoCashier> = ({
  customer,
  showRfid = false,
  rfid,
  checkinTime,
  lockerNumber,
  hideFields = [],
}) => {
  const name = `${customer?.firstName || ''} ${customer?.lastName || ''}`;

  const { data: genderSetting } = useSetting(['gender']);

  const gender = genderSetting?.gender?.find(g => g?.id === customer?.gender?.id);

  const { oldCredit, newCredit: credit } = useMemo(() => getCredits(customer?.credits), [customer]);

  return (
    <Stack gap="4px" flex={1}>
      <StyledCustomerName variant="heading-xmedium-700">{name}</StyledCustomerName>

      {gender?.name && !hideFields?.includes('gender') && (
        <Stack flexDirection="row" gap={1.5}>
          <StyledTitle width="100px" variant="heading-small-700">
            Gender
          </StyledTitle>
          <StyledTitle variant="body-large-400">{`${gender?.name || ''}`}</StyledTitle>
        </Stack>
      )}

      {checkinTime && (
        <Stack flexDirection="row" gap={1.5}>
          <StyledTitle width="100px" variant="heading-small-700">
            Checkin
          </StyledTitle>
          <StyledTitle variant="body-large-400">{`${checkinTime || ''}`}</StyledTitle>
        </Stack>
      )}

      <Stack flexDirection="row" gap={1.5}>
        <StyledTitle width="100px" variant="heading-small-700">
          RFID number
        </StyledTitle>
        <StyledTitle variant="body-large-400">{`${lockerNumber}`}</StyledTitle>
      </Stack>

      {customer?.expiryDate && !hideFields?.includes('passport') && (
        <Stack flexDirection="row" gap={1.5}>
          <StyledTitle width="100px" variant="heading-small-700">
            Passport
          </StyledTitle>
          <StyledTitle variant="body-large-400">{`${dayjs
            .utc(customer?.expiryDate)
            .format(FORMAT_DD_MM_YYYY_SLASH)}`}</StyledTitle>
        </Stack>
      )}

      {showRfid && (
        <Stack flexDirection="row" gap={1.5}>
          <StyledTitle width="100px" variant="heading-small-700">
            RFID
          </StyledTitle>
          <StyledTitle variant="body-large-400">{`${rfid}`}</StyledTitle>
        </Stack>
      )}

      {credit?.isShow && !hideFields?.includes('credit') && (
        <Stack flexDirection="row" gap={1.5} justifyContent="flex-start" alignItems="center">
          <StyledTitle width="100px" variant="heading-small-700">
            Credit
          </StyledTitle>

          <StyledTitle
            variant="body-large-400"
            width="60px"
            sx={{
              wordBreak: 'break-word',
              whiteSpace: 'pre-wrap',
            }}
          >{`${round2decimal(credit?.balance || 0)}`}</StyledTitle>
          <StyledTitle
            variant="body-large-400"
            width="100px"
            sx={{
              wordBreak: 'break-word',
              whiteSpace: 'pre-wrap',
            }}
          >
            {dayjs.utc(credit?.rawExpiredDate).format(FORMAT_DD_MM_YYYY_SLASH)}
          </StyledTitle>
          <Typography
            variant="body-medium-400"
            color="white"
            sx={{ background: credit?.isExpired ? 'red' : '#3BA716', borderRadius: '10px', padding: '4px 10px' }}
          >
            {credit?.isExpired ? 'Expired' : 'Valid'}
          </Typography>
        </Stack>
      )}

      {oldCredit?.isShow && (
        <Stack flexDirection="row" gap={1.5} justifyContent="flex-start" alignItems="center">
          <StyledTitle width="100px" variant="heading-small-700">
            Old Credit
          </StyledTitle>
          <StyledTitle
            variant="body-large-400"
            width="60px"
            sx={{
              wordBreak: 'break-word',
              whiteSpace: 'pre-wrap',
            }}
          >{`${round2decimal(oldCredit?.balance || 0)}`}</StyledTitle>
          <StyledTitle
            variant="body-large-400"
            width="100px"
            sx={{
              wordBreak: 'break-word',
              whiteSpace: 'pre-wrap',
            }}
          >
            {dayjs.utc(oldCredit?.rawExpiredDate).format(FORMAT_DD_MM_YYYY_SLASH)}
          </StyledTitle>
          <Typography
            variant="body-medium-400"
            color="white"
            sx={{ background: oldCredit?.isExpired ? 'red' : '#3BA716', borderRadius: '10px', padding: '4px 10px' }}
          >
            {oldCredit?.isExpired ? 'Expired' : 'Valid'}
          </Typography>
        </Stack>
      )}

      {customer?.remark && (
        <Stack flexDirection="row" gap={1.5}>
          <StyledTitle variant="heading-small-700" minWidth="100px">
            Remark
          </StyledTitle>
          <StyledTitle
            variant="body-large-400"
            maxHeight="84px"
            maxWidth="300px"
            sx={{
              wordBreak: 'break-word',
              '&::-webkit-scrollbar': {
                background: '#DCE5F2',
                borderRadius: '5px',
                width: '5px',
              },
              '&:: -webkit-scrollbar-thumb': {
                background: '#99A3B4',
                borderRadius: '5px',
              },
            }}
            overflow="auto"
          >{`${customer?.remark || ''}`}</StyledTitle>
        </Stack>
      )}
    </Stack>
  );
};
