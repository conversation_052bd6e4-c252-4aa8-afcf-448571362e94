import Stack from '@mui/material/Stack';
import React, { createRef, useMemo, useState } from 'react';
import Cropper, { ReactCropperElement } from 'react-cropper';
import { Theme, dialogClasses } from '@mui/material';
import { StyledButton, StyledButtonWrapper } from './ImageCropperModal.styled';
import Modal from '@/components/Atoms/Modal';
import 'cropperjs/dist/cropper.css';
import { dataURLToFile } from '@/lib/utils/file';

export type TImageCropperModalProps = {
  image?: File;
  fileName: string;
  isOpen: boolean;
  isLoading: boolean;
  onFileUpLoad: (file?: File) => Promise<void>;
  onClose: () => void;
};

export const ImageCropperModal: React.FC<TImageCropperModalProps> = ({
  image,
  isOpen,
  isLoading,
  fileName,
  onFileUpLoad,
  onClose,
}) => {
  const [cropData, setCropData] = useState('');

  const cropperRef = createRef<ReactCropperElement>();

  const onCrop = () => {
    if (typeof cropperRef.current?.cropper !== 'undefined') {
      setCropData(cropperRef.current?.cropper.getCroppedCanvas().toDataURL());
    }
  };
  const covertedImage = useMemo(() => (image ? URL.createObjectURL(image) : ''), [image]);
  return (
    <Modal
      isOpen={isOpen}
      handleClose={onClose}
      title="UPLOAD IMAGE"
      dialogProps={{
        sx: (theme: Theme) => ({
          [`& .${dialogClasses.paper}`]: {
            maxWidth: theme.spacing(500 / 8),
            height: 'fit-content',
            padding: theme.spacing(3),
            maxHeight: 'calc(100% - 48px)',
            backgroundColor: theme.palette.neutrals.N100.main,
            borderRadius: theme.spacing(1.25),
            [theme.breakpoints.down('md')]: {
              maxWidth: 'auto',
              padding: theme.spacing(2),
            },
          },
        }),
      }}
    >
      <Stack>
        <Cropper
          ref={cropperRef}
          style={{ height: 400, width: '100%' }}
          zoomTo={0.5}
          initialAspectRatio={16 / 9}
          aspectRatio={16 / 9}
          src={covertedImage}
          preview=".img-preview"
          viewMode={1}
          minCropBoxHeight={10}
          minCropBoxWidth={10}
          background={false}
          responsive
          autoCropArea={1}
          checkOrientation={false} // https://github.com/fengyuanchen/cropperjs/issues/671
          guides
          crop={onCrop}
        />
        <StyledButtonWrapper>
          <StyledButton variant="outlined" label="Cancel" size="large" loadingText="Cancel" onClick={onClose} />
          <StyledButton
            variant="contained"
            type="submit"
            label="Upload"
            size="large"
            loadingText="Uploading"
            isLoading={isLoading}
            onClick={() => onFileUpLoad(cropData ? dataURLToFile(cropData, fileName) : undefined)}
          />
        </StyledButtonWrapper>
      </Stack>
    </Modal>
  );
};
