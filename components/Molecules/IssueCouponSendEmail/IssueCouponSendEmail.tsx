import Stack from '@mui/material/Stack';
import React, { useEffect, useState } from 'react';
import { z } from 'zod';
import { useMutationSendOneCode } from '@/lib/hooks/mutation/issue-coupon';
import Button from '@/components/Atoms/Button';
import { StyledTextField } from './IssueCouponSendEmail.styled';
import { TApiError } from '@/lib/utils/transformResponse';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { useMe } from '@/lib/hooks/queries/me';
import { hasPermission } from '@/lib/utils/role';
import { EPermission, EResourceAccess } from '@/lib/types/enum/role';

type TIssueCouponSendEmail = {
  code: string;
  disable?: boolean;
  onRefetchCouponCodes: () => void;
  sentEmail?: string;
};

const validateEmail = z.object({ email: z.string().email('This is not a valid email.') });

export const IssueCouponSendEmail: React.FC<TIssueCouponSendEmail> = ({
  code,
  disable,
  sentEmail = '',
  onRefetchCouponCodes,
}) => {
  const [email, setEmail] = useState<string>(sentEmail);
  const [isDisableButton, setDisableButton] = useState<boolean>(false);
  const { showError, showSuccess } = useAlert();
  const { onSendOneCode, isMutating } = useMutationSendOneCode();
  const { data: meData } = useMe();
  const resource = meData?.resource || [];

  useEffect(() => {
    setEmail(sentEmail);
  }, [sentEmail]);

  const onSend = async () => {
    if (code) {
      const response = await onSendOneCode({ email, couponCode: code });
      if (response.status === 'error') {
        if (typeof response?.message?.error === 'string') {
          return showError({ title: response?.message?.error || 'Send code failed' });
        }
        if (Array.isArray(response.message.error)) {
          return showError({ title: 'Send code failed' });
        }
        // It's a TApiError
        const apiError = response.message.error as TApiError;
        return showError({ title: apiError?.message || 'Send code failed' });
      }
      onRefetchCouponCodes();
      showSuccess({ title: 'Send code successfully' });
    }
  };

  useEffect(() => {
    const validated = validateEmail.safeParse({ email });
    setDisableButton(!validated.success);
  }, [email]);

  return (
    <Stack width="100%" flexDirection="row" flexWrap="nowrap" alignItems="center" gap={1}>
      <StyledTextField
        sx={{ minWidth: '200px' }}
        disabled={disable}
        value={email}
        onChange={e => setEmail(String(e?.target?.value))}
      />
      {hasPermission(resource, EResourceAccess['ISSUE_COUPON'], EPermission['SEND_MAIL']) && (
        <Button
          loadingText={sentEmail ? 'Resending...' : 'Sending...'}
          isLoading={isMutating}
          label={sentEmail ? 'Resend' : 'Send'}
          variant="contained"
          sx={{ width: '150px !important' }}
          onClick={() => onSend()}
          disabled={disable || isDisableButton}
        />
      )}
    </Stack>
  );
};
