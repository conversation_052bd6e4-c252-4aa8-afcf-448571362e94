import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import AppointmentsTable from '.';

export default {
  title: 'Molecules/AppointmentsTable',
  component: AppointmentsTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof AppointmentsTable>;

export const Default: Story = {
  args: {
    rows: [],
    columns: [],
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <AppointmentsTable {...args} />
    </Box>
  ),
};
