import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { TimeField } from '@/components/Atoms/TimeField/TimeField';
import { TTimeFieldProps } from '@/components/Atoms/TimeField/TimeField.types';

export type TRHFTimeField = {
  name: string;
} & Partial<TTimeFieldProps>;

export const RHFTimeField: React.FC<TRHFTimeField> = ({ name, ...resetProps }) => {
  const { control } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { ref, value, onChange, ...restField } }) => (
        <TimeField value={value} onChange={onChange} {...restField} {...resetProps} />
      )}
    />
  );
};
