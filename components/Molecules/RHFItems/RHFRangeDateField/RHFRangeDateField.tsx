import { FC } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Stack from '@mui/material/Stack';
import Icon from '@/components/Atoms/Icon';
import { StyledFormHelperText } from '../RHFItems.styled';
import RangeDateField, { TRangeDateFieldProps } from '@/components/Atoms/RangeDateField';
import { TSelectOption } from '../RHFSelect/RHFSelect';

export type TRHFRangeDateField = {
  name: string;
  startDateFieldName: string;
  endDateFieldName: string;
  rangeTypeFieldName: string;
  showHelperText?: boolean;
} & Partial<TRangeDateFieldProps>;

export const RHFRangeDateField: FC<TRHFRangeDateField> = ({ name, showHelperText = true, ...restProps }) => {
  const {
    control,
    formState: { errors },
    setValue,
    getValues,
    watch,
  } = useFormContext();
  const helperText = errors[name] ? (errors[name]?.message as unknown as string) : '';
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange } }) => {
        const handleChangeValue = ({ field, value }: { field: string; value: string | number }) => {
          setValue(field, value);
        };
        const transfromValue = {
          [restProps.startDateFieldName]: watch(restProps.startDateFieldName),
          [restProps.endDateFieldName]: watch(restProps.endDateFieldName),
          [restProps.rangeTypeFieldName]: watch(restProps.rangeTypeFieldName),
        };
        return (
          <Stack sx={{ width: '100%', height: '100%' }}>
            <RangeDateField
              value={transfromValue}
              onChange={handleChangeValue}
              error={!!errors?.[name]}
              {...restProps}
            />
            {showHelperText && (
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                gap={0.5}
                mt={0.5}
                minHeight="20px"
              >
                {helperText && (
                  <>
                    <Icon variant="warning" size={1.5} />
                    <StyledFormHelperText>{helperText}</StyledFormHelperText>
                  </>
                )}
              </Stack>
            )}
          </Stack>
        );
      }}
    />
  );
};
