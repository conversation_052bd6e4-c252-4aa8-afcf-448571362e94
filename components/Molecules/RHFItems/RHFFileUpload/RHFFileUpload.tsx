import { FC } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { MediaUpload, TMediaUpload } from '@/components/Atoms/MediaUpload/MediaUpload';

export type TRHFUploadFile = {
  name: string;
} & Omit<TMediaUpload, 'getFileUploadRes'>;

export const RHFFileUpload: FC<TRHFUploadFile> = ({ name, variant = 'avatar', ...restProps }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange } }) => {
        const handleChange = (data: { id: string; url?: string | undefined }) => {
          onChange(data?.url ? data : null);
        };

        return (
          <MediaUpload
            variant={variant}
            getFileUploadRes={handleChange}
            avatarProps={{ src: value?.url }}
            value={value}
            {...restProps}
          />
        );
      }}
    />
  );
};
