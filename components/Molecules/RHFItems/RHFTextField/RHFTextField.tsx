import { FormControl, FormControlProps, IconButton, Stack } from '@mui/material';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { TTextFieldProps } from '@/components/Atoms/TextField/TextField.types';
import { TextField } from '@/components/Atoms/TextField/TextField';
import { TOptions } from '../RHFItems.types';
import Icon from '@/components/Atoms/Icon';
import { StyledFormHelperText } from '../RHFItems.styled';
import { BoldCloseIcon } from '@/components/Atoms/IconsComponent';
import { customPalettes } from '@/theme/customPalettes';

export type TRHFTextField = {
  name: string;
  options?: TOptions;
  formControlProps?: FormControlProps;
  showHelperText?: boolean;
  exceptThisSymbols?: string[];
  showCloseIcon?: boolean;
} & Partial<TTextFieldProps>;

export const RHFTextField: React.FC<TRHFTextField> = ({
  name,
  type,
  formControlProps,
  showHelperText = true,
  showCloseIcon = false,
  ...restProps
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const helperText = errors[name] ? (errors[name]?.message as unknown as string) : '';
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { ref, value, onChange, ...restField } }) => (
        <FormControl {...formControlProps}>
          <TextField
            variant="outlined"
            sx={{ mb: 0 }}
            value={value}
            type={type}
            onChange={e => {
              if (type === 'number') {
                onChange(Number(e.target.value));
                return;
              }
              onChange(e.target.value);
            }}
            {...restField}
            {...restProps}
            InputProps={{
              ...restProps.InputProps,
              endAdornment: showCloseIcon ? (
                <IconButton onClick={() => onChange('')} sx={{ color: customPalettes?.neutrals?.N50?.main }}>
                  <BoldCloseIcon style={{ width: '18px', height: '18px' }} />
                </IconButton>
              ) : (
                restProps.InputProps?.endAdornment
              ),
            }}
          />
          {showHelperText && (
            <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
              {helperText && (
                <>
                  <Icon variant="warning" size={1.5} />
                  <StyledFormHelperText>{helperText}</StyledFormHelperText>
                </>
              )}
            </Stack>
          )}
        </FormControl>
      )}
    />
  );
};
