import { FC } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Stack from '@mui/material/Stack';
import { TTimeFieldProps } from '@/components/Atoms/TimeField/TimeField.types';
import RangeTimeField from '@/components/Atoms/RangeTimeField';
import Icon from '@/components/Atoms/Icon';
import { StyledFormHelperText } from '../RHFItems.styled';

export type TRHFRangeTimeField = {
  name: string;
  startTimeFieldName: string;
  endTimeFieldName: string;
  showHelperText?: boolean;
} & Partial<TTimeFieldProps>;

export const RHFRangeTimeField: FC<TRHFRangeTimeField> = ({ name, showHelperText = true, ...restProps }) => {
  const {
    control,
    formState: { errors },
    setValue,
    getValues,
  } = useFormContext();
  const helperText = errors[name] ? (errors[name]?.message as unknown as string) : '';
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange } }) => {
        const handleChangeValue = ({ field, value }: { field: string; value: string | number }) => {
          if (field === restProps.startTimeFieldName) {
            setValue(field, value);
          } else {
            onChange(value);
          }
        };
        const transfromValue = {
          [restProps.startTimeFieldName]: getValues(restProps.startTimeFieldName),
          [restProps.endTimeFieldName]: getValues(restProps.endTimeFieldName),
        };
        return (
          <Stack sx={{ height: '100%' }}>
            <RangeTimeField
              {...restProps}
              value={transfromValue}
              onChange={handleChangeValue}
              error={!!errors?.[name]}
            />
            {showHelperText && (
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                gap={0.5}
                mt={0.5}
                minHeight="20px"
              >
                {helperText && (
                  <>
                    <Icon variant="warning" size={1.5} />
                    <StyledFormHelperText>{helperText}</StyledFormHelperText>
                  </>
                )}
              </Stack>
            )}
          </Stack>
        );
      }}
    />
  );
};
