import { Icon<PERSON><PERSON>on, Stack } from '@mui/material';
import { FC } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Icon from '@/components/Atoms/Icon';
import PhoneInput, { TPhoneInputProps } from '@/components/Atoms/PhoneInput';
import { parseDialCodeAndPhoneNumber } from '@/lib/utils/phoneNumber';
import { StyledAddPhoneText, StyledMutipleContainer } from './RHFPhoneInput.styled';

export type TRHFPhoneInput = {
  name: string;
  isMutiple?: boolean;
} & TPhoneInputProps;

export const RHFPhoneInput: FC<TRHFPhoneInput> = ({ isMutiple = false, name, ...restProps }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange } }) => {
        // Mutiple phone pick
        if (isMutiple) {
          const currenValue: string[] = Array.isArray(value) ? value : [value];

          const onMutipleChanged = (newValue: string, index: number) => {
            currenValue[index] = newValue;
            onChange(currenValue);
          };

          const addNewInput = () => {
            onChange([...currenValue, '+65']);
          };

          const onDeleteInput = (index: number) => {
            const newVal = currenValue.filter((v, indx) => index !== indx);
            onChange([...newVal]);
          };

          return (
            <StyledMutipleContainer>
              {currenValue?.map((v, index) => {
                const parsedValue = parseDialCodeAndPhoneNumber(typeof v === 'string' ? v : '+65');

                return (
                  <Stack flexDirection="row">
                    <PhoneInput
                      {...restProps}
                      phoneVariant="base-autocomplete"
                      formControlProps={{ sx: { flex: '90%' } }}
                      value={{
                        dialCode: parsedValue?.dialCode || '',
                        phoneNumber: parsedValue?.phoneNumber || '',
                      }}
                      label={`${restProps.label} ${index + 1}`}
                      onChange={value => onMutipleChanged(`${value.dialCode}${value.phoneNumber}`, index)}
                      error={!!errors[name]}
                      helperText={errors[name] ? (errors[name]?.message as unknown as string) : ''}
                    />
                    {index > 0 && (
                      <IconButton
                        onClick={() => onDeleteInput(index)}
                        sx={{
                          '&.MuiButtonBase-root:hover': {
                            bgcolor: 'transparent',
                          },
                        }}
                      >
                        <Icon variant="delete" />
                      </IconButton>
                    )}
                  </Stack>
                );
              })}
              <Stack onClick={addNewInput} flexDirection="row" gap={1}>
                <Icon variant="plus-green" />
                <StyledAddPhoneText variant="heading-medium-700">Add phone</StyledAddPhoneText>
              </Stack>
            </StyledMutipleContainer>
          );
        }

        const parsedValue = parseDialCodeAndPhoneNumber(typeof value === 'string' ? value : '+65');

        return (
          <PhoneInput
            {...restProps}
            value={{
              dialCode: parsedValue?.dialCode || '',
              phoneNumber: parsedValue?.phoneNumber || '',
            }}
            onChange={value => onChange(`${value.dialCode}${value.phoneNumber}`)}
            error={!!errors[name]}
            helperText={errors[name] ? (errors[name]?.message as unknown as string) : ''}
          />
        );
      }}
    />
  );
};
