import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { TTextFieldProps } from '@/components/Atoms/TextField/TextField.types';
import { TextField } from '@/components/Atoms/TextField/TextField';
import Icon from '@/components/Atoms/Icon';

export type TRHFPassword = {
  name: string;
} & Partial<TTextFieldProps>;

export const RHFPassword: React.FC<TRHFPassword> = ({ name, ...restProps }) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const handleClickShowPassword = () => setShowPassword(show => !show);

  const handleMouseDownPassword = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
  };
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { ref, value, ...restField } }) => (
        <TextField
          variant="outlined"
          sx={{ mb: 0 }}
          error={!!errors[name]}
          value={value}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={handleClickShowPassword}
                  onMouseDown={handleMouseDownPassword}
                  edge="end"
                >
                  <Icon variant={showPassword ? 'eye-close' : 'eye'} />
                </IconButton>
              </InputAdornment>
            ),
          }}
          helperText={errors[name] ? (errors[name]?.message as unknown as string) : ' '}
          {...restField}
          {...restProps}
          type={showPassword ? 'text' : 'password'}
        />
      )}
    />
  );
};
