import { FC } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { isEmpty } from 'lodash';
import { TreeViewSingle } from '@/components/Atoms/TreeViewSingle/TreeViewSingle';
import { TTreeViewOption, TTreeViewSingle } from '@/components/Atoms/TreeViewSingle/TreeViewSingle.types';
import Typography from '@/components/Atoms/Typography';
import { findOptionById } from '@/lib/utils/array';
import { StyledWrapper } from './RHFTreeViewSelect.styled';

export type TRHFTreeViewSelect = {
  name: string;
  label?: string;
  disabled?: boolean;
  handleOnChange?: (option: TTreeViewOption) => void;
} & Partial<TTreeViewSingle>;

export const RHFTreeViewSelect: FC<TRHFTreeViewSelect> = ({
  label = '',
  name,
  options,
  disabled,
  handleOnChange,
  ...restProps
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field: { ref, value, onChange, ...restField } }) => (
          <>
            <TreeViewSingle
              label={label}
              options={options}
              selected={value}
              helperText={errors[name] ? (errors[name]?.message as unknown as string) : ''}
              onSelectedChange={(id: string) => {
                const option = findOptionById(options?.children || [], id);
                if (!option || !isEmpty(option?.children) || !isEmpty(option?.items)) return;
                onChange({
                  id: option?.id,
                  name: option?.name,
                });
                if (handleOnChange) handleOnChange(option);
              }}
              {...restField}
              {...restProps}
              disabled={disabled}
            >
              <StyledWrapper disabled={disabled}>
                <Typography variant="body-xlarge-400" textAlign="left" textTransform="none">
                  {value?.name}
                </Typography>
                <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M5.75 7.875L11 13.125L16.25 7.875"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </StyledWrapper>
            </TreeViewSingle>
          </>
        )}
      />
    </>
  );
};
