import { SxProps, Theme } from '@mui/material';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Autocomplete, { TAutocompleteProps } from '@/components/Atoms/Autocomplete';
import { TTextFieldProps } from '@/components/Atoms/TextField/TextField.types';
import TreeViewAutoComplete from '@/components/Atoms/TreeViewAutoComplete';
import Tag from '@/components/Atoms/TreeViewAutoComplete/Tag';
import { StyledContainer } from './RHFAutocomplete.styled';

export type AutocompleteVariant = 'normal' | 'tree_view_modal';
export type TOption = {
  id: string;
  name: string | any;
  disabled?: boolean;
  parent?: {
    id: string;
    name: string;
  };
  children?: Array<TOption & { parent?: { id: string } }>;
  items?: TOption[];
  isError?: boolean;
} & { [key: string]: any };

export type TRHFAutocomplete = {
  label?: string;
  name: string;
  options?: TOption[];
  textfieldProps?: TTextFieldProps;
  showValue?: boolean;
  defaultValue?: TOption[];
  sxLabel?: SxProps<Theme>;
  sxContainer?: SxProps<Theme>;
  isHideDeleteWorker?: boolean;
  variant?: AutocompleteVariant;
  searchTerm?: string;
  selectTerm?: string;
  showValueType?: 'default' | 'extends';
  showHelperText?: boolean;
} & TAutocompleteProps;

export const RHFAutocomplete: React.FC<TRHFAutocomplete> = ({
  name,
  variant,
  showValueType = 'default',
  showHelperText = true,
  ...restProps
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field: { ref, onChange, value, ...restField } }) => {
          if (variant === 'tree_view_modal') {
            const onDeleteValue = (id: string) => {
              const newValue = value?.filter((v: any) => v?.id !== id);
              onChange(newValue);
            };

            return (
              <>
                <TreeViewAutoComplete
                  ref={ref}
                  value={value}
                  showSelectedValue={showValueType !== 'extends'}
                  // onChange={onChange}
                  defaultValue={value}
                  handleOnChange={onChange}
                  error={!!errors[name]}
                  helperText={errors[name] ? (errors[name]?.message as unknown as string) : ''}
                  {...restField}
                  {...restProps}
                />
                {showValueType === 'extends' && value?.length > 0 && (
                  <StyledContainer className="RHFAutocomplete-value-extends">
                    {value?.map((val: any) => (
                      <Tag
                        disabled={restProps?.disabled}
                        key={val?.id}
                        label={val?.name}
                        onDelete={() => {
                          if (restProps?.disabled || val?.disabled) return;
                          onDeleteValue(val?.id || '');
                        }}
                      />
                    ))}
                  </StyledContainer>
                )}
              </>
            );
          }
          let helperText = '';

          if (!showHelperText) {
            helperText = '';
          } else if (errors[name]) {
            helperText = errors[name]?.message as unknown as string;
          } else {
            helperText = ' ';
          }

          return (
            <>
              <Autocomplete
                ref={ref}
                value={value}
                // onChange={onChange}
                defaultValue={value}
                handleOnChange={onChange}
                error={!!errors[name]}
                helperText={helperText}
                {...restField}
                {...restProps}
              />
            </>
          );
        }}
      />
    </>
  );
};
