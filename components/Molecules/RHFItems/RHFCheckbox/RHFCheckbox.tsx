import FormControl, { FormControlProps } from '@mui/material/FormControl';
import Stack from '@mui/material/Stack';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Grid, { GridProps } from '@mui/material/Grid';
import Icon from '@/components/Atoms/Icon';
import { StyledFormHelperText } from '../RHFItems.styled';
import Typography, { ITypographyProps } from '@/components/Atoms/Typography';
import { customPalettes } from '@/theme/customPalettes';
import Checkbox, { ICheckboxProps } from '@/components/Atoms/Checkbox';

export interface TCheckboxOption {
  label: string;
  value: string;
}
export type TRHFCheckbox = {
  name: string;
  label?: string;
  options?: TCheckboxOption[];
  showHelperText?: boolean;
  handleOnChange?: (checked: boolean) => void;
  formControlProps?: FormControlProps;
  gridItemProps?: GridProps;
  isGroupCheckbox?: boolean;
  checkboxLabel?: string;
  labelProps?: ITypographyProps;
} & ICheckboxProps;

export const RHFCheckbox: React.FC<TRHFCheckbox> = ({
  isGroupCheckbox = true,
  name,
  label,
  formControlProps,
  options,
  showHelperText,
  handleOnChange,
  gridItemProps,
  checkboxLabel,
  labelProps = {},
  ...restProps
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const helperText = errors[name] ? (errors[name]?.message as unknown as string) : '';
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { ref, value, onChange } }) => (
        <FormControl {...formControlProps}>
          {label && (
            <Typography variant="heading-medium-700" color={customPalettes?.neutrals?.N50.main} {...labelProps}>
              {label}
            </Typography>
          )}
          {isGroupCheckbox ? (
            <Grid container spacing={1.5} width="100%">
              {options?.map((option: TCheckboxOption) => (
                <Grid item xs={6} sm={4} md={3} key={option.value} {...gridItemProps}>
                  <Checkbox
                    {...restProps}
                    checked={value === option.value}
                    label={option.label}
                    onChange={(checked: boolean) => {
                      onChange(checked ? option.value : '');
                      if (handleOnChange) handleOnChange(checked);
                    }}
                  />
                </Grid>
              ))}
            </Grid>
          ) : (
            <Checkbox
              {...restProps}
              checked={value}
              label={checkboxLabel}
              onChange={(checked: boolean) => {
                onChange(checked);
                if (handleOnChange) handleOnChange(checked);
              }}
            />
          )}
          {showHelperText && (
            <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
              {helperText && (
                <>
                  <Icon variant="warning" size={1.5} />
                  <StyledFormHelperText>{helperText}</StyledFormHelperText>
                </>
              )}
            </Stack>
          )}
        </FormControl>
      )}
    />
  );
};
