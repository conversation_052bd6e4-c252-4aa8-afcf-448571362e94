import styled from '@emotion/styled';
import FormHelperText, { formHelperTextClasses } from '@mui/material/FormHelperText';
import Stack from '@mui/material/Stack';
import Typography from '@/components/Atoms/Typography';

export const ContainerWrapper = styled(Stack)(({ theme }) => ({
  height: 'fit-content',
  width: '100%',
  overflow: 'hidden',
}));

export const StyledFormHelperText = styled(FormHelperText)(({ theme }) => ({
  [`&.${formHelperTextClasses.root}`]: {
    padding: 0,
    margin: 0,
    color: theme.palette.neutrals.N110.main,
    ...theme.typography['body-small-400'],
  },
}));

export const StyledLabel = styled(Typography)<{ $isRequired?: boolean }>(({ theme, $isRequired }) => ({
  marginBottom: theme.spacing(1),
  color: theme.palette.neutrals.N50.main,
  ...($isRequired
    ? {
        ':before': {
          content: `"*"`,
          color: 'red',
        },
      }
    : {}),
}));
