import { Box, Divider } from '@mui/material';
import Stack, { StackProps } from '@mui/material/Stack';
import { isEmpty, union } from 'lodash';
import React, { useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Checkbox from '@/components/Atoms/Checkbox';
import CheckboxGroup from '@/components/Atoms/CheckboxGroup';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import { TCustomAccessResource } from '@/lib/types/entities/resource';
import { customPalettes } from '@/theme/customPalettes';
import { StyledFormHelperText } from '../RHFItems.styled';
import { StyledContainer, StyledLabel } from './RHFResourceAccess.styled';

export type TRHFResourceAccess = {
  name: string;
  label?: string;
  resources: TCustomAccessResource[];
  containerProps?: StackProps;
  fieldWrapperProps?: StackProps;
  disabled?: boolean;
};

export const RHFResourceAccess: React.FC<TRHFResourceAccess> = ({
  name,
  label,
  resources,
  containerProps,
  fieldWrapperProps,
  disabled,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const [resourceIndex, setResourceIndex] = useState<number>(0);
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { ref, value, onChange } }) => {
        const transformValue = Array.isArray(value) ? value : [];
        const activeModule = resources?.[resourceIndex];
        const isSelectedAll = activeModule?.groupPermission?.every(item => value.includes(item));
        return (
          <StyledContainer {...containerProps}>
            {label && <StyledLabel variant="heading-xlarge-700">{label}</StyledLabel>}
            <Stack gap={3} {...fieldWrapperProps}>
              <Stack direction="row" gap={2.25}>
                {resources?.map((resource, index) => (
                  <Typography
                    variant="body-large-400"
                    sx={theme => ({
                      padding: theme.spacing(1, 1.25),
                      borderRadius: theme.spacing(1.25),
                      color: resourceIndex === index ? theme.palette.common.white : theme.palette.neutrals.N50.main,
                      backgroundColor: resourceIndex === index ? theme.palette.neutrals.N50.main : 'transparent',
                      cursor: 'pointer',
                    })}
                    onClick={() => setResourceIndex(index)}
                  >
                    {resource?.name}
                  </Typography>
                ))}
              </Stack>
              <Checkbox
                disabled={disabled}
                checked={isSelectedAll}
                label="Select all"
                onChange={(checked: boolean) => {
                  if (checked) {
                    const newValue = union(transformValue, activeModule?.groupPermission || []);
                    onChange(newValue);
                  } else {
                    const newValue = transformValue?.filter(
                      (item: string) => !activeModule?.groupPermission?.includes(item)
                    );
                    onChange(newValue);
                  }
                }}
              />
              <Divider
                sx={theme => ({
                  backgroundColor: theme.palette.neutrals.N50.main,
                })}
              />
              {isEmpty(activeModule?.children) && (
                <Stack direction="row" gap={1}>
                  <Typography
                    variant="heading-medium-700"
                    textTransform="uppercase"
                    color={customPalettes?.neutrals?.N50?.main}
                    flex="1"
                  >
                    {activeModule?.name}
                  </Typography>
                  <Box flex="9">
                    <CheckboxGroup
                      variant="normal"
                      onChange={newValues => {
                        onChange(newValues);
                      }}
                      options={activeModule?.permissionOptions || []}
                      values={transformValue}
                    />
                  </Box>
                </Stack>
              )}
              {activeModule?.children?.map(subModule => {
                const listPermission = subModule?.permissionOptions || [];
                return (
                  <Stack direction="row" gap={1} key={subModule?.code}>
                    <Typography
                      variant="heading-medium-700"
                      textTransform="uppercase"
                      color={customPalettes?.neutrals?.N50?.main}
                      width="250px"
                      whiteSpace="pre-wrap"
                      sx={{ wordBreak: 'break-word' }}
                    >
                      {subModule?.name}
                    </Typography>
                    <Box flex="9">
                      <CheckboxGroup
                        variant="normal"
                        onChange={newValues => {
                          onChange(newValues);
                        }}
                        options={listPermission}
                        values={transformValue}
                      />
                    </Box>
                  </Stack>
                );
              })}
            </Stack>
            {(errors?.[name] as any)?.root?.message && (
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                gap={0.5}
                mt={0.5}
                minHeight="20px"
              >
                {(errors?.[name] as any)?.root?.message && (
                  <>
                    <Icon variant="warning" size={1.5} />
                    <StyledFormHelperText>{(errors?.[name] as any)?.root?.message}</StyledFormHelperText>
                  </>
                )}
              </Stack>
            )}
          </StyledContainer>
        );
      }}
    />
  );
};
