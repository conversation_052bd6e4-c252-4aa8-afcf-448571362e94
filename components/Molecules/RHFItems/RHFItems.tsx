import { FormDivider, TFormDivider } from '@/components/Atoms/FormDivider/FormDivider';
import RHFAppointmentServices from './RHFAppointmentServices';
import { TRHFAppointmentServices } from './RHFAppointmentServices/RHFAppointmentServices';
import { RHFAutocomplete, TRHFAutocomplete } from './RHFAutocomplete/RHFAutocomplete';
import RHFCheckbox from './RHFCheckbox';
import { TRHFCheckbox } from './RHFCheckbox/RHFCheckbox';
import RHFColorPicker from './RHFColorPicker';
import { TRHFColorPicker } from './RHFColorPicker/RHFColorPicker';
import RHFDateTimePicker from './RHFDateTimePicker';
import { TRHFDateTimePicker } from './RHFDateTimePicker/RHFDateTimePicker';
import RHFFileUpload from './RHFFileUpload';
import { TRHFUploadFile } from './RHFFileUpload/RHFFileUpload';
import { ContainerWrapper } from './RHFItems.styled';
import { TRHFProps } from './RHFItems.types';
import RHFPassword from './RHFPassword';
import { TRHFPassword } from './RHFPassword/RHFPassword';
import RHFPhoneInput from './RHFPhoneInput';
import { TRHFPhoneInput } from './RHFPhoneInput/RHFPhoneInput';
import { RHFRadio, TRHFRadio } from './RHFRadio/RHFRadio';
import RHFRangeDateField from './RHFRangeDateField';
import { TRHFRangeDateField } from './RHFRangeDateField/RHFRangeDateField';
import RHFRangeTimeField from './RHFRangeTimeField';
import { TRHFRangeTimeField } from './RHFRangeTimeField/RHFRangeTimeField';
import { RHFResourceAccess, TRHFResourceAccess } from './RHFResourceAccess/RHFResourceAccess';
import RHFSelect from './RHFSelect';
import { TRHFSelect } from './RHFSelect/RHFSelect';
import RHFSelectAndTextfield from './RHFSelectAndTextfield';
import { TRHFSelectAndTextfield } from './RHFSelectAndTextfield/RHFSelectAndTextfield';
import RHFTextField from './RHFTextField';
import { TRHFTextField } from './RHFTextField/RHFTextField';
import RHFTimeField from './RHFTimeField';
import { TRHFTimeField } from './RHFTimeField/RHFTimeField';
import RHFTimePicker from './RHFTimePicker';
import { TRHFTimePicker } from './RHFTimePicker/RHFTimePicker';
import RHFTreeViewMultipleSelect from './RHFTreeViewMultipleSelect';
import { TRHFTreeViewMultipleSelect } from './RHFTreeViewMultipleSelect/RHFTreeViewMultipleSelect';
import RHFTreeViewSelect from './RHFTreeViewSelect';
import { TRHFTreeViewSelect } from './RHFTreeViewSelect/RHFTreeViewSelect';
import RHFWorkingHours from './RHFWorkingHours';
import { TRHFWorkingHours } from './RHFWorkingHours/RHFWorkingHours';
import RHFSummarizeBy from './RHFSummarizeBy';
import { TRHFSummarizeBy } from './RHFSummarizeBy/RHFSummarizeBy';

const RHFItem: React.FC<TRHFProps> = ({ containerProps, type, attributes }) => {
  const Items: Record<TRHFProps['type'], any> = {
    password: <RHFPassword {...(attributes as TRHFPassword)} />,
    textfield: <RHFTextField {...(attributes as TRHFTextField)} />,
    calendar: <RHFDateTimePicker {...(attributes as TRHFDateTimePicker)} />,
    select: <RHFSelect {...(attributes as TRHFSelect)} />,
    divider: <FormDivider {...(attributes as TFormDivider)} />,
    fileUpload: <RHFFileUpload {...(attributes as TRHFUploadFile)} />,
    phone: <RHFPhoneInput {...(attributes as TRHFPhoneInput)} />,
    radio: <RHFRadio {...(attributes as TRHFRadio)} />,
    colorPicker: <RHFColorPicker {...(attributes as TRHFColorPicker)} />,
    autocomplete: <RHFAutocomplete {...(attributes as TRHFAutocomplete)} />,
    workingHours: <RHFWorkingHours {...(attributes as TRHFWorkingHours)} />,
    selectAndTextfield: <RHFSelectAndTextfield {...(attributes as TRHFSelectAndTextfield)} />,
    timepicker: <RHFTimePicker {...(attributes as TRHFTimePicker)} />,
    treeViewSelect: <RHFTreeViewSelect {...(attributes as TRHFTreeViewSelect)} />,
    appointmentServices: <RHFAppointmentServices {...(attributes as TRHFAppointmentServices)} />,
    timefield: <RHFTimeField {...(attributes as TRHFTimeField)} />,
    upload: <RHFFileUpload {...(attributes as TRHFUploadFile)} />,
    checkbox: <RHFCheckbox {...(attributes as TRHFCheckbox)} />,
    resourceAccess: <RHFResourceAccess {...(attributes as TRHFResourceAccess)} />,
    rangeTime: <RHFRangeTimeField {...(attributes as TRHFRangeTimeField)} />,
    rangeDate: <RHFRangeDateField {...(attributes as TRHFRangeDateField)} />,
    treeViewMultipleSelect: <RHFTreeViewMultipleSelect {...(attributes as TRHFTreeViewMultipleSelect)} />,
    summarizeBy: <RHFSummarizeBy {...(attributes as TRHFSummarizeBy)} />,
  };
  return <ContainerWrapper {...containerProps}>{Items[type]}</ContainerWrapper>;
};

export default RHFItem;
