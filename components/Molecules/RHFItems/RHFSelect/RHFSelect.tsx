import { FormControlProps, MenuProps, SelectProps } from '@mui/material';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Select, { TSelectProps } from '@/components/Atoms/Select';
import { IconVariantTypes } from '@/components/Atoms/Icon';

export type TSelectOption = {
  id: string;
  name: string | any;
  parentId?: string;
  icon?: IconVariantTypes;
  parent?: {
    id: string;
    name: string;
  };
  children?: Array<TSelectOption & { parent?: { id: string } }>;
  items?: TSelectOption[];
  renderLabel?: () => React.ReactNode;
  isError?: boolean;
};

export type TRHFSelect = {
  name: string;
  label?: string;
  options?: TSelectOption[];
  formControlProps?: FormControlProps;
  selectProps?: SelectProps;
  menuProps?: Omit<MenuProps, 'open'>;
  showCheckbox?: boolean;
  showHelperText?: boolean;
  isGrouping?: boolean;
  isTransformValue?: boolean;
  isCustomOnChange?: boolean;
  // transform response to {id:string}
  // customFilterValue?: (neuValue: string[]) => TSelectOption[];
} & TSelectProps;

export const RHFSelect: React.FC<TRHFSelect> = ({
  name,
  options,
  isTransformValue = true,
  isCustomOnChange,
  ...restProps
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field: { ref, value, onChange, ...reseField } }) => {
          const handleChangeValue = (valueInput?: string | string[]) => {
            if (isCustomOnChange) {
              return onChange(valueInput);
            }
            if (!isTransformValue) {
              return onChange(valueInput);
            }
            if (Array.isArray(valueInput)) {
              const newValue = options?.filter(o => valueInput.includes(o?.id));
              onChange(newValue);
            } else {
              const newValue = options?.find(o => o.id === valueInput);
              onChange(newValue);
            }
          };

          let transformValue = value;
          if (isTransformValue) {
            if (Array.isArray(transformValue)) {
              transformValue = transformValue?.map(o => o?.id || o);
            } else {
              transformValue = transformValue?.id || transformValue || '';
            }
          }
          return (
            <Select
              {...reseField}
              handleOnChange={handleChangeValue}
              {...restProps}
              options={options}
              error={!!errors[name]}
              value={transformValue}
              helperText={errors[name] ? (errors[name]?.message as unknown as string) : ''}
            />
          );
        }}
      />
    </>
  );
};
