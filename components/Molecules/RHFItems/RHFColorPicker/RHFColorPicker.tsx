import Stack from '@mui/material/Stack';
import { FC } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Typography from '@/components/Atoms/Typography';
import { CustomColorPicker, IColorPicker } from '@/components/Atoms/CustomColorPicker/CustomColorPicker';

export type TRHFColorPicker = {
  name: string;
  label?: string;
} & Partial<IColorPicker>;

export const RHFColorPicker: FC<TRHFColorPicker> = ({ name, ...restProps }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field: { ref, value, ...restField } }) => (
          <Stack sx={{ height: '100%' }}>
            <CustomColorPicker value={value} onChange={restField.onChange} {...restProps} />
            {errors[name] && (
              <Typography fontSize="12px" sx={theme => ({ '&&': { color: theme.palette.red[200] } })}>
                {errors[name]?.message as unknown as string}
              </Typography>
            )}
          </Stack>
        )}
      />
    </>
  );
};
