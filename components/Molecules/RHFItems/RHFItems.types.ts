import { StackProps } from '@mui/material';
import { TFormDivider } from '@/components/Atoms/FormDivider/FormDivider';
import { TRHFAppointmentServices } from './RHFAppointmentServices/RHFAppointmentServices';
import { TRHFAutocomplete } from './RHFAutocomplete/RHFAutocomplete';
import { TRHFCheckbox } from './RHFCheckbox/RHFCheckbox';
import { TRHFColorPicker } from './RHFColorPicker/RHFColorPicker';
import { TRHFSelectAndTextfield } from './RHFSelectAndTextfield/RHFSelectAndTextfield';
import { TRHFDateTimePicker } from './RHFDateTimePicker/RHFDateTimePicker';
import { TRHFUploadFile } from './RHFFileUpload/RHFFileUpload';
import { TRHFPassword } from './RHFPassword/RHFPassword';
import { TRHFPhoneInput } from './RHFPhoneInput/RHFPhoneInput';
import { TRHFRadio } from './RHFRadio/RHFRadio';
import { TRHFSelect } from './RHFSelect/RHFSelect';
import { TRHFTextField } from './RHFTextField/RHFTextField';
import { TRHFTimeField } from './RHFTimeField/RHFTimeField';
import { TRHFTimePicker } from './RHFTimePicker/RHFTimePicker';
import { TRHFTreeViewSelect } from './RHFTreeViewSelect/RHFTreeViewSelect';
import { TRHFWorkingHours } from './RHFWorkingHours/RHFWorkingHours';
import { TRHFResourceAccess } from './RHFResourceAccess/RHFResourceAccess';
import { TRHFRangeTimeField } from './RHFRangeTimeField/RHFRangeTimeField';
import { TRHFRangeDateField } from './RHFRangeDateField/RHFRangeDateField';
import { TRHFTreeViewMultipleSelect } from './RHFTreeViewMultipleSelect/RHFTreeViewMultipleSelect';
import { TRHFSummarizeBy } from './RHFSummarizeBy/RHFSummarizeBy';

export type TOption = { id: string | number; name: string | any };

export type TOptions<T = TOption> = T extends TOption ? TOption[] : T[];

export type OptionTypes =
  | 'colorPicker'
  | 'password'
  | 'textfield'
  | 'calendar'
  | 'select'
  | 'divider'
  | 'fileUpload'
  | 'phone'
  | 'radio'
  | 'autocomplete'
  | 'workingHours'
  | 'selectAndTextfield'
  | 'timepicker'
  | 'treeViewSelect'
  | 'appointmentServices'
  | 'timefield'
  | 'upload'
  | 'checkbox'
  | 'resourceAccess'
  | 'rangeTime'
  | 'rangeDate'
  | 'treeViewMultipleSelect'
  | 'summarizeBy';

export type TRHFTextFieldProps = { type: 'textfield'; attributes: TRHFTextField; containerProps?: StackProps };
export type TRHFPasswordProps = { type: 'password'; attributes: TRHFPassword; containerProps?: StackProps };
export type TRHFCalendarProps = { type: 'calendar'; attributes: TRHFDateTimePicker; containerProps?: StackProps };
export type TRHFSelectProps = { type: 'select'; attributes: TRHFSelect; containerProps?: StackProps };
export type TRHFDividerProps = { type: 'divider'; attributes: TFormDivider; containerProps?: StackProps };
export type TRHFPhoneProps = { type: 'phone'; attributes: TRHFPhoneInput; containerProps?: StackProps };
export type TRHFUploadProps = { type: 'upload'; attributes: TRHFUploadFile; containerProps?: StackProps };
export type TRHFRadioProps = {
  type: 'radio';
  attributes: TRHFRadio;
  containerProps?: StackProps;
};
export type TRHFColorPickerProps = { type: 'colorPicker'; attributes: TRHFColorPicker; containerProps?: StackProps };
export type TRHFAutocompleteProps = { type: 'autocomplete'; attributes: TRHFAutocomplete; containerProps?: StackProps };
export type TRHFWorkingHoursProps = { type: 'workingHours'; attributes: TRHFWorkingHours; containerProps?: StackProps };
export type TRHFSelectAndTextfieldProps = {
  type: 'selectAndTextfield';
  attributes: TRHFSelectAndTextfield;
  containerProps?: StackProps;
};
export type TRHFTimepickerProps = { type: 'timepicker'; attributes: TRHFTimePicker; containerProps?: StackProps };
export type TRHFTreeViewSelectProps = {
  type: 'treeViewSelect';
  attributes: TRHFTreeViewSelect;
  containerProps?: StackProps;
};
export type TRHFTreeViewMultipleSelectProps = {
  type: 'treeViewMultipleSelect';
  attributes: TRHFTreeViewMultipleSelect;
  containerProps?: StackProps;
};
export type TRHFTimeFieldProps = { type: 'timefield'; attributes: TRHFTimeField; containerProps?: StackProps };
export type TRHFAppointmentServiceProps = {
  type: 'appointmentServices';
  attributes: TRHFAppointmentServices;
  containerProps?: StackProps;
};
export type TRHFCheckProps = {
  type: 'checkbox';
  attributes: TRHFCheckbox;
  containerProps?: StackProps;
};

export type TRHFResourceAccessProps = {
  type: 'resourceAccess';
  attributes: TRHFResourceAccess;
  containerProps?: StackProps;
};

export type TRHFRangeTimeProps = {
  type: 'rangeTime';
  attributes: TRHFRangeTimeField;
  containerProps?: StackProps;
};

export type TRHFRangeDateProps = {
  type: 'rangeDate';
  attributes: TRHFRangeDateField;
  containerProps?: StackProps;
};

export type TRHFSummarizeByProps = {
  type: 'summarizeBy';
  attributes: TRHFSummarizeBy;
  containerProps?: StackProps;
};

export type TRHFProps = {
  type: OptionTypes;
  containerProps?: StackProps;
  attributes:
    | TRHFTextField
    | TRHFPassword
    | TRHFDateTimePicker
    | TRHFSelect
    | TFormDivider
    | TRHFPhoneInput
    | TRHFRadio
    | TRHFColorPicker
    | TRHFAutocomplete
    | TRHFWorkingHours
    | TRHFSelectAndTextfield
    | TRHFTimePicker
    | TRHFAppointmentServices
    | TRHFTimeField
    | TRHFUploadProps
    | TRHFResourceAccess
    | TRHFRangeDateField
    | TRHFTreeViewMultipleSelect
    | TRHFSummarizeBy;
};
