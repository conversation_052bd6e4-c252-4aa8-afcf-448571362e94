import { FC, useMemo, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import FormControl, { FormControlProps } from '@mui/material/FormControl';
import Popover from '@mui/material/Popover';
import { isEmpty } from 'lodash';
import Stack from '@mui/material/Stack';
import Icon from '@/components/Atoms/Icon';
import { StyledFormHelperText } from '../RHFItems.styled';
import { StyledWrapper } from './RHFTreeViewMultipleSelect.styled';
import Typography from '@/components/Atoms/Typography';
import { TreeViewMultiple } from '@/components/Atoms/TreeViewMultiple/TreeViewMultiple';
import {
  ETreeViewMultipleVariants,
  TTreeViewMultiple,
} from '@/components/Atoms/TreeViewMultiple/TreeViewMultiple.types';
import { StyledLabel } from '@/components/Atoms/TextField/TextField.styled';
import { StyledButton } from '@/components/Atoms/TreeViewSingle/TreeViewSingle.styled';
import { convertToSelectOptions } from '@/lib/utils/array';
import { TSelectOption } from '../RHFSelect/RHFSelect';

export type TRHFTreeViewMultipleSelect = {
  name: string;
  label?: string;
  disabled?: boolean;
  formControlProps?: FormControlProps;
  showHelperText?: boolean;
} & Partial<TTreeViewMultiple>;

export const RHFTreeViewMultipleSelect: FC<TRHFTreeViewMultipleSelect> = ({
  label = '',
  name,
  options,
  disabled,
  formControlProps,
  showHelperText,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) {
      event.preventDefault();
      return;
    }
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const defaultExpaned = useMemo(
    () =>
      convertToSelectOptions(options?.[0]?.children || [])
        ?.filter(option => !isEmpty(option?.items) || !isEmpty(option.children))
        ?.map(option => option?.id) || [],
    [JSON.stringify(options)]
  );
  const helperText = errors[name] ? (errors[name]?.message as unknown as string) : '';
  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field: { ref, value, onChange, ...restField } }) => (
          <FormControl {...formControlProps} variant="filled" error={!!helperText}>
            {label && <StyledLabel variant="heading-medium-700">{label}</StyledLabel>}
            <StyledButton onClick={handleClick} disableRipple disabled={disabled}>
              <StyledWrapper disabled={disabled}>
                <Typography
                  component="p"
                  width="100%"
                  overflow="clip"
                  variant="body-xlarge-400"
                  textTransform="none"
                  whiteSpace="nowrap"
                  textOverflow="ellipsis"
                  textAlign="left"
                >
                  {value?.map((item: TSelectOption) => (typeof item === 'string' ? item : item?.name))?.join(', ')}
                </Typography>
                <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M5.75 7.875L11 13.125L16.25 7.875"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </StyledWrapper>
            </StyledButton>
            <Popover
              open={open}
              anchorEl={anchorEl}
              onClose={handleClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
              }}
              sx={theme => ({
                marginTop: theme.spacing(0.625),
              })}
            >
              <TreeViewMultiple
                wrapperProps={{
                  sx: {
                    maxHeight: '50vh',
                    overflowY: 'auto',
                  },
                }}
                options={options || []}
                selected={Array.isArray(value) ? value.map(item => (typeof item === 'string' ? item : item?.id)) : []}
                onSelectedChange={value =>
                  onChange(
                    convertToSelectOptions(options || [])
                      ?.filter(option => value.includes(option?.id))
                      ?.map(option => ({
                        id: option.id,
                        name: option.name,
                      })) || []
                  )
                }
                variant={ETreeViewMultipleVariants['SMALL']}
              />
            </Popover>
            {showHelperText && (
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                gap={0.5}
                mt={0.5}
                minHeight="20px"
              >
                {helperText && (
                  <>
                    <Icon variant="warning" size={1.5} />
                    <StyledFormHelperText>{helperText}</StyledFormHelperText>
                  </>
                )}
              </Stack>
            )}
          </FormControl>
        )}
      />
    </>
  );
};
