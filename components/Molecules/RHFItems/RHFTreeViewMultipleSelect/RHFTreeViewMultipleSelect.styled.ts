import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import { alpha } from '@mui/material/styles';

export const StyledWrapper = styled(Stack)<{ disabled?: boolean }>(({ theme, disabled }) => ({
  width: 'fit-content',
  minWidth: '100%',
  justifyContent: 'space-between',
  alignItems: 'center',
  flexDirection: 'row',
  flexWrap: 'nowrap',
  backgroundColor: theme.palette.common.white,
  borderWidth: theme.spacing(1 / 16),
  borderStyle: 'solid',
  borderColor: disabled ? alpha(theme.palette.common.black, 0.26) : theme.palette.neutrals.N180.main,
  padding: theme.spacing(1.25),
  borderRadius: theme.spacing(1.25),
  color: disabled ? alpha(theme.palette.common.black, 0.26) : theme.palette.neutrals.N50.main,
  '&:hover': {
    borderColor: theme.palette.neutrals.N50.main,
  },
}));
