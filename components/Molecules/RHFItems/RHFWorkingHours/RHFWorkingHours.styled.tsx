import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  overflowX: 'hidden',
  gap: theme.spacing(1),
}));

export const StyledDeleteItemButton = styled(IconButton)(({ theme }) => ({
  width: theme.spacing(2),
  height: theme.spacing(3),
  justifyContent: 'center',
  alignItems: 'center',
}));

export const StyledAddItemButton = styled(IconButton)(({ theme }) => ({
  width: theme.spacing(3),
  height: theme.spacing(3),
  justifyContent: 'center',
  alignItems: 'center',
  backgroundColor: theme.palette.neutrals.N50.main,
}));
