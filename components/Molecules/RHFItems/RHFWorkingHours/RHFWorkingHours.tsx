import Stack, { StackProps } from '@mui/material/Stack';
import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import Grid from '@mui/material/Grid';
import dayjs from 'dayjs';
import Typography from '@/components/Atoms/Typography';
import Icon from '@/components/Atoms/Icon';
import { StyledAddItemButton, StyledDeleteItemButton, StyledContainer } from './RHFWorkingHours.styled';
import { StyledFormHelperText } from '../RHFItems.styled';
import RHFTimePicker from '../RHFTimePicker';

export type TRHFWorkingHours = {
  name: string;
  containerProps?: StackProps;
  fieldWrapperProps?: StackProps;
  disabled?: boolean;
};

export const RHFWorkingHours: React.FC<TRHFWorkingHours> = ({
  name,
  containerProps,
  disabled = false,
  fieldWrapperProps,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const { fields, append, remove } = useFieldArray({
    control, // control props comes from useForm (optional: if you are using FormContext)
    name, // unique name for your Field Array
  });
  return (
    <Stack>
      <StyledContainer {...containerProps}>
        <Grid container columnSpacing={{ xs: 2, sm: 2.25 }} alignItems="flex-end">
          <Grid item xs={5.5} sm={5.5}>
            <Typography variant="heading-medium-700" component="h4">
              Shift start
            </Typography>
          </Grid>
          <Grid item xs={5.5} sm={5.5}>
            <Typography variant="heading-medium-700" component="h4">
              Shift end
            </Typography>
          </Grid>
          <Grid item xs={1} sm={1}>
            <Stack flexDirection="row" justifyContent="flex-end">
              <StyledAddItemButton
                disabled={disabled}
                onClick={() => {
                  if (disabled) return;
                  append({
                    startTime: dayjs().startOf('day').add(9, 'hours').toISOString(),
                    endTime: dayjs().startOf('day').add(10, 'hours').toISOString(),
                  });
                }}
                disableRipple
              >
                <Icon variant="bold_plus" size={2.25} />
              </StyledAddItemButton>
            </Stack>
          </Grid>
        </Grid>
        <Stack gap={1} {...fieldWrapperProps}>
          {fields.map((item, index) => (
            <>
              <Grid key={`working-hours-${item.id}`} container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2.25 }}>
                <Grid item xs={5.5} sm={5.5}>
                  {/* <RHFSelect
                    name={`${name}.${index}.startTime`}
                    disabled={disabled}
                    options={HOURS_IN_DAY}
                    formControlProps={{
                      sx: {
                        width: '100%',
                      },
                    }}
                    selectProps={{
                      sx: {
                        width: '100%',
                      },
                    }}
                    showHelperText={false}
                  /> */}
                  <RHFTimePicker
                    name={`${name}.${index}.startTime`}
                    disabled={disabled}
                    formControlProps={{
                      sx: {
                        width: '100%',
                      },
                    }}
                    showHelperText={false}
                    timeSteps={{ minutes: 15 }}
                    minTime={dayjs().startOf('day').add(9, 'hours')}
                  />
                </Grid>
                <Grid item xs={5.5} sm={5.5}>
                  {/* <RHFSelect
                    name={`${name}.${index}.endTime`}
                    options={HOURS_IN_DAY}
                    formControlProps={{
                      sx: {
                        width: '100%',
                      },
                    }}
                    selectProps={{
                      sx: {
                        width: '100%',
                      },
                    }}
                    showHelperText={false}
                  /> */}
                  <RHFTimePicker
                    name={`${name}.${index}.endTime`}
                    disabled={disabled}
                    formControlProps={{
                      sx: {
                        width: '100%',
                      },
                    }}
                    showHelperText={false}
                    timeSteps={{ minutes: 15 }}
                    minTime={dayjs().startOf('day').add(9, 'hours')}
                  />
                </Grid>
                <Grid item xs={1} sm={1}>
                  {index !== 0 && (
                    <Stack direction="row" justifyContent="flex-end" alignItems="center" height="100%">
                      <StyledDeleteItemButton
                        disabled={disabled}
                        onClick={() => {
                          if (disabled) return;
                          remove(index);
                        }}
                      >
                        <Icon variant="delete" size={3} />
                      </StyledDeleteItemButton>
                    </Stack>
                  )}
                </Grid>
              </Grid>
              {(errors?.[name] as any)?.[index]?.message && (
                <Stack
                  direction="row"
                  justifyContent="flex-start"
                  alignItems="center"
                  gap={0.5}
                  mt={0.5}
                  minHeight="20px"
                >
                  {(errors?.[name] as any)?.[index]?.message && (
                    <>
                      <Icon variant="warning" size={1.5} />
                      <StyledFormHelperText>{(errors?.[name] as any)?.[index]?.message}</StyledFormHelperText>
                    </>
                  )}
                </Stack>
              )}
            </>
          ))}
        </Stack>
      </StyledContainer>
      {(errors?.[name] as any)?.root?.message && (
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
          {(errors?.[name] as any)?.root?.message && (
            <>
              <Icon variant="warning" size={1.5} />
              <StyledFormHelperText>{(errors?.[name] as any)?.root?.message}</StyledFormHelperText>
            </>
          )}
        </Stack>
      )}
    </Stack>
  );
};
