import FormControl, { FormControlProps } from '@mui/material/FormControl';
import Stack from '@mui/material/Stack';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Grid from '@mui/material/Grid';
import Icon from '@/components/Atoms/Icon';
import { StyledFormHelperText } from '../RHFItems.styled';
import Typography from '@/components/Atoms/Typography';
import { customPalettes } from '@/theme/customPalettes';
import Checkbox, { ICheckboxProps } from '@/components/Atoms/Checkbox';

export interface TRadioOption {
  label: string;
  value: string;
  disabled?: boolean;
}
export type TRHFRadio = {
  name: string;
  label?: string;
  options?: TRadioOption[];
  formControlProps?: FormControlProps;
} & ICheckboxProps;

export const RHFRadio: React.FC<TRHFRadio> = ({ name, label, formControlProps, options, ...restProps }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const helperText = errors[name] ? (errors[name]?.message as unknown as string) : '';
  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field: { ref, value, onChange, ...reseField } }) => (
          <FormControl {...formControlProps}>
            <Typography variant="heading-medium-700" color={customPalettes?.neutrals?.N50.main}>
              {label}
            </Typography>
            <Grid container spacing={1.5}>
              {options?.map((option: TRadioOption) => (
                <Grid item xs={6} sm={4} md={3} key={option.value}>
                  <Checkbox
                    checked={value === option.value}
                    disabled={option?.disabled}
                    label={option.label}
                    onChange={(checked: boolean) => {
                      if (checked) {
                        onChange(option.value);
                      }
                    }}
                  />
                </Grid>
              ))}
            </Grid>
            <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
              {helperText && (
                <>
                  <Icon variant="warning" size={1.5} />
                  <StyledFormHelperText>{helperText}</StyledFormHelperText>
                </>
              )}
            </Stack>
          </FormControl>
        )}
      />
    </>
  );
};
