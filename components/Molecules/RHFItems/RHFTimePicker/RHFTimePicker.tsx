import Stack from '@mui/material/Stack';
import { FC, ReactNode } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { TTimePicker, TimePicker } from '@/components/Atoms/TimePicker/TimePicker';
import Icon from '@/components/Atoms/Icon';
import { StyledFormHelperText } from '../RHFItems.styled';

export type TRHFTimePicker = {
  name: string;
  label?: string | ReactNode;
  showHelperText?: boolean;
} & Partial<TTimePicker>;

export const RHFTimePicker: FC<TRHFTimePicker> = ({ name, showHelperText = true, ...restProps }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const helperText = errors[name] ? (errors[name]?.message as unknown as string) : '';

  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field: { ref, value, ...restField } }) => (
          <Stack sx={{ height: '100%' }}>
            <TimePicker {...restProps} value={value} onChange={restField.onChange} />
            {showHelperText && (
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                gap={0.5}
                mt={0.5}
                minHeight="20px"
              >
                {helperText && (
                  <>
                    <Icon variant="warning" size={1.5} />
                    <StyledFormHelperText>{helperText}</StyledFormHelperText>
                  </>
                )}
              </Stack>
            )}
          </Stack>
        )}
      />
    </>
  );
};
