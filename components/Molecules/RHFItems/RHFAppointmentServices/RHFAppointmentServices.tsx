import Stack, { StackProps } from '@mui/material/Stack';
import React, { Fragment, useEffect, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import Grid from '@mui/material/Grid';
import { Box, Theme } from '@mui/material';
import { isEmpty } from 'lodash';
import RHFSelect from '../RHFSelect';
import Icon from '@/components/Atoms/Icon';
import { StyledDeleteItemButton, StyledRank, StyledContainer } from './RHFAppointmentServices.styled';
import { StyledFormHelperText } from '../RHFItems.styled';
import { TSelectOption } from '../RHFSelect/RHFSelect';
import RHFTreeViewSelect from '../RHFTreeViewSelect';
import { TTreeViewOption } from '@/components/Atoms/TreeViewSingle/TreeViewSingle.types';
import RHFTextField from '../RHFTextField';

export type TRHFAppointmentServices = {
  name: string;
  containerProps?: StackProps;
  fieldWrapperProps?: StackProps;
  employeeOptions?: TSelectOption[];
  durationOptions?: TSelectOption[];
  serviceOption?: TTreeViewOption;
  disabled?: boolean;
  onSearchServices?: (search: string) => void;
  keywordService?: string;
  showOneItem?: boolean;
  allEmployeeOptions?: { serviceId: string; employees: TSelectOption[] }[];
};

export const RHFAppointmentServices: React.FC<TRHFAppointmentServices> = ({
  name,
  containerProps,
  fieldWrapperProps,
  employeeOptions = [],
  durationOptions = [],
  serviceOption,
  disabled,
  onSearchServices,
  keywordService,
  showOneItem = false,
  allEmployeeOptions = [],
}) => {
  const [lastRowIndex, setLastRowIndex] = useState<number>(-1);
  const {
    control,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();

  const { fields, append, remove } = useFieldArray({
    control, // control props comes from useForm (optional: if you are using FormContext)
    name, // unique name for your Field Array
  });
  // Update lastRowIndex when fields change
  useEffect(() => {
    const lastRowIndex = showOneItem ? 1 : fields.length - 1;
    setLastRowIndex(lastRowIndex);
  }, [fields]);

  useEffect(() => {
    const lastRow = watch(name)?.[fields.length - 1];
    if (
      lastRow?.product?.id &&
      lastRow?.duration?.id &&
      !isEmpty(lastRow?.employees) &&
      lastRowIndex === fields.length - 1 &&
      !showOneItem
    ) {
      // Check for conditions in the last row to determine if it's filled up
      append({
        product: undefined,
        duration: undefined,
        employees: [],
        price: 0,
      });
    }
  }, [JSON.stringify(watch(name)), append]);

  const fieldArrays = watch(name) || [];

  const fieldArrayMap = fields?.map((item, index) => ({
    ...item,
    ...(fieldArrays?.[index] || {}),
  }));

  return (
    <StyledContainer {...containerProps}>
      <Stack gap={1} {...fieldWrapperProps}>
        {fieldArrayMap?.map((item, index) => {
          const fieldProductId = item?.product?.id;
          const options = showOneItem
            ? employeeOptions
            : allEmployeeOptions?.find(item => item.serviceId === fieldProductId)?.employees || [];
          return (
            <Fragment key={item?.id}>
              <Grid container>
                <Grid
                  item
                  xs={0.5}
                  sm={0.5}
                  sx={{
                    position: 'relative',
                    [`&::before`]: {
                      content: "''",
                      position: 'absolute',
                      bottom: '100%',
                      left: '10px',
                      width: '1px',
                      height: '90%',
                      borderLeft: '1px dashed white',
                    },
                  }}
                >
                  <StyledRank isLast={index === fields.length - 1}> {index + 1}</StyledRank>
                </Grid>
                <Grid item xs={11.5} sm={11.5}>
                  <Stack
                    sx={(theme: Theme) => ({
                      position: 'relative',
                      gap: theme.spacing(1.5),
                      padding: theme.spacing(3),
                      backgroundColor: theme.palette.neutrals.N100.main,
                      borderRadius: theme.spacing(1.25),
                    })}
                  >
                    <Grid
                      key={`appointment-service-${item?.id}`}
                      container
                      rowSpacing={1}
                      columnSpacing={{ xs: 1, sm: 2.25 }}
                    >
                      <Grid item xs={6} sm={6}>
                        <RHFTreeViewSelect
                          name={`${name}.${index}.product`}
                          label="Service"
                          options={serviceOption}
                          formControlProps={{
                            sx: {
                              width: '100%',
                            },
                          }}
                          showHelperText={false}
                          disabled={disabled}
                          handleOnChange={(option: TTreeViewOption) => {
                            setValue(`${name}.${index}.price`, Number(option?.price || 0));
                            setValue(`${name}.${index}.duration`, option?.duration);
                          }}
                          onSearch={onSearchServices}
                          keyword={keywordService}
                        />
                      </Grid>
                      <Grid item xs={3} sm={3}>
                        <RHFSelect
                          name={`${name}.${index}.duration`}
                          label="Duration"
                          options={durationOptions}
                          formControlProps={{
                            sx: {
                              width: '100%',
                            },
                          }}
                          selectProps={{
                            sx: {
                              width: '100%',
                            },
                          }}
                          showHelperText={false}
                          disabled
                        />
                      </Grid>
                      <Grid item xs={3} sm={3}>
                        <RHFSelect
                          name={`${name}.${index}.employees`}
                          label="Therapist"
                          options={options}
                          showCheckbox
                          multiple
                          showAllOption={false}
                          formControlProps={{
                            sx: {
                              width: '100%',
                            },
                          }}
                          selectProps={{
                            sx: {
                              width: '100%',
                            },
                          }}
                          showHelperText={false}
                          disabled={disabled}
                        />
                      </Grid>
                    </Grid>
                    {index !== 0 && (
                      <Stack
                        direction="row"
                        sx={(theme: Theme) => ({
                          position: 'absolute',
                          right: theme.spacing(1.5),
                          top: theme.spacing(1.5),
                        })}
                      >
                        <StyledDeleteItemButton onClick={() => remove(index)} disableRipple>
                          <Icon variant="close" size={2.25} />
                        </StyledDeleteItemButton>
                      </Stack>
                    )}
                    <RHFTextField
                      name={`${name}.${index}.note`}
                      disabled={disabled}
                      label="Notes"
                      rows={4}
                      multiline
                      formControlProps={{
                        sx: theme => ({
                          width: '100%',
                        }),
                      }}
                    />
                  </Stack>
                </Grid>
              </Grid>
              {(errors?.[name] as any)?.[index]?.message && (
                <Stack
                  direction="row"
                  justifyContent="flex-start"
                  alignItems="center"
                  gap={0.5}
                  mt={0.5}
                  minHeight="20px"
                >
                  {(errors?.[name] as any)?.[index]?.message && (
                    <>
                      <Icon variant="warning" size={1.5} />
                      <StyledFormHelperText>{(errors?.[name] as any)?.[index]?.message}</StyledFormHelperText>
                    </>
                  )}
                </Stack>
              )}
            </Fragment>
          );
        })}
      </Stack>
      {(errors?.[name] as any)?.root?.message && (
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
          {(errors?.[name] as any)?.root?.message && (
            <>
              <Icon variant="warning" size={1.5} />
              <StyledFormHelperText>{(errors?.[name] as any)?.root?.message}</StyledFormHelperText>
            </>
          )}
        </Stack>
      )}
    </StyledContainer>
  );
};
