import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@/components/Atoms/Typography';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  overflowX: 'hidden',
  gap: theme.spacing(1),
}));

export const StyledDeleteItemButton = styled(IconButton)(({ theme }) => ({
  width: theme.spacing(2),
  height: theme.spacing(3),
  justifyContent: 'center',
  alignItems: 'center',
}));

export const StyledRank = styled(Typography)<{ isLast?: boolean }>(({ theme, isLast }) => ({
  width: theme.spacing(2.5),
  height: theme.spacing(2.5),
  borderRadius: '50%',
  color: isLast ? theme.palette.neutrals.N100.main : theme.palette.common.white,
  backgroundColor: isLast ? theme.palette.neutrals.N190.main : theme.palette.info.main,
  textAlign: 'center',
  ...theme.typography['heading-medium-700'],
  lineHeight: theme.spacing(2.5),
}));
