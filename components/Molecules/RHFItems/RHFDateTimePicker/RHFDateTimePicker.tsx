import Stack from '@mui/material/Stack';
import { FC } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { CustomDatePicker } from '@/components/Atoms/CustomDatePicker';
import { TCustomDatePicker } from '@/components/Atoms/CustomDatePicker/CustomDatePicker';
import Icon from '@/components/Atoms/Icon';
import { StyledFormHelperText } from '../RHFItems.styled';

export type TRHFDateTimePicker = {
  name: string;
  label?: string;
  showHelperText?: boolean;
} & Partial<TCustomDatePicker>;

export const RHFDateTimePicker: FC<TRHFDateTimePicker> = ({ name, showHelperText = true, ...restProps }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const helperText = errors[name] ? (errors[name]?.message as unknown as string) : '';
  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field: { ref, value, ...restField } }) => (
          <Stack sx={{ height: '100%' }}>
            <CustomDatePicker value={value} onChange={restField.onChange} {...restProps} />
            {showHelperText && (
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                gap={0.5}
                mt={0.5}
                minHeight="20px"
              >
                {helperText && (
                  <>
                    <Icon variant="warning" size={1.5} />
                    <StyledFormHelperText>{helperText}</StyledFormHelperText>
                  </>
                )}
              </Stack>
            )}
          </Stack>
        )}
      />
    </>
  );
};
