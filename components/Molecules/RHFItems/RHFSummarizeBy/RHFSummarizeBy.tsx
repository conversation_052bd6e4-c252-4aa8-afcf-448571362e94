import { FC, useId, useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Stack from '@mui/material/Stack';
import { isEmpty } from 'lodash';
import dayjs, { Dayjs } from 'dayjs';
import { DateView } from '@mui/x-date-pickers';
import Icon from '@/components/Atoms/Icon';
import { StyledFormHelperText, StyledLabel } from '../RHFItems.styled';
import { FORMAT_TIME_FULL, SUMMARIZE_BY_OPTIONS } from '@/lib/constants/dateTime';
import { ESummarizeByDateRange, ESummarizeByType } from '@/lib/types/enum/report';
import { TSelectOption } from '../RHFSelect/RHFSelect';
import Select from '@/components/Atoms/Select';
import { CustomDatePicker } from '@/components/Atoms/CustomDatePicker';

export type TRHFSummarizeBy = {
  label: string;
  name: string;
  startDateFieldName: string;
  endDateFieldName: string;
  rangeTypeFieldName: string;
  showHelperText?: boolean;
  rangeDateTypeOptions?: TSelectOption[];
};

export const RHFSummarizeBy: FC<TRHFSummarizeBy> = ({
  name,
  label,
  showHelperText = true,
  startDateFieldName,
  endDateFieldName,
  rangeTypeFieldName,
  rangeDateTypeOptions,
}) => {
  const {
    control,
    formState: { errors },
    setValue,
    getValues,
    watch,
  } = useFormContext();
  const helperText = errors[name] ? (errors[name]?.message as unknown as string) : '';
  const id = `range-type-select-${useId()}`;
  const labelId = `range-type-label-${useId()}`;
  const transformStartTimeValue = isEmpty(watch(startDateFieldName)) ? null : dayjs(watch(startDateFieldName));
  const transformEndTimeValue = isEmpty(watch(endDateFieldName)) ? null : dayjs(watch(endDateFieldName));
  const handleOnSelectChange = (value: string) => {
    setValue(rangeTypeFieldName, value);
    switch (value) {
      case ESummarizeByDateRange.THIS_YEAR:
        setValue(startDateFieldName, dayjs().startOf('year').format(FORMAT_TIME_FULL));
        setValue(endDateFieldName, dayjs().endOf('year').format(FORMAT_TIME_FULL));
        break;
      case ESummarizeByDateRange.LAST_YEAR:
        setValue(startDateFieldName, dayjs().subtract(1, 'year').startOf('year').format(FORMAT_TIME_FULL));
        setValue(endDateFieldName, dayjs().subtract(1, 'year').endOf('year').format(FORMAT_TIME_FULL));
        break;
      case ESummarizeByDateRange.SELECT_DATE:
        setValue(startDateFieldName, dayjs().startOf('year').format(FORMAT_TIME_FULL));
        setValue(endDateFieldName, dayjs().endOf('year').format(FORMAT_TIME_FULL));
        break;
      default:
        break;
    }
  };

  const showDateRange = watch(rangeTypeFieldName) === ESummarizeByDateRange.SELECT_DATE;
  const { views, formatDate } = useMemo(() => {
    if (watch(name) === ESummarizeByType.MONTH || watch(name) === ESummarizeByType.WEEK) {
      return { views: ['month', 'year'], formatDate: 'MM/YYYY' };
    }
    if (watch(name) === ESummarizeByType.YEAR) {
      return { views: ['year'], formatDate: 'YYYY' };
    }

    return { views: ['month', 'year', 'date'], formatDate: 'DD/MM/YYYY' };
  }, [watch(rangeTypeFieldName), watch(name)]);
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange } }) => (
        <Stack sx={{ width: '100%', height: '100%' }}>
          {label && <StyledLabel variant="heading-medium-700">{label}</StyledLabel>}
          <Stack direction="row" alignItems="center" gap={1.5}>
            <Select
              name={name}
              showAllOption={false}
              showCheckbox={false}
              multiple={false}
              showHelperText={false}
              options={SUMMARIZE_BY_OPTIONS}
              value={value}
              error={!!errors?.[name]}
              handleOnChange={value => {
                if (typeof value === 'string') setValue(name, value);
              }}
            />
            {!isEmpty(rangeDateTypeOptions) && (
              <Select
                id={id}
                labelId={labelId}
                multiline={false}
                value={watch(rangeTypeFieldName)}
                handleOnChange={value => {
                  if (typeof value === 'string') handleOnSelectChange(value);
                }}
                options={rangeDateTypeOptions}
                showHelperText={false}
                sx={{ minWidth: 'fit-content', width: '20%' }}
              />
            )}
            {showDateRange && (
              <>
                <CustomDatePicker
                  formControlProps={{
                    sx: {
                      width: '20%',
                    },
                  }}
                  views={views as readonly DateView[]}
                  format={formatDate}
                  value={transformStartTimeValue}
                  onChange={newValue => setValue(startDateFieldName, (newValue as Dayjs)?.format(FORMAT_TIME_FULL))}
                />
                <CustomDatePicker
                  formControlProps={{
                    sx: {
                      width: '20%',
                    },
                  }}
                  views={views as readonly DateView[]}
                  format={formatDate}
                  value={transformEndTimeValue}
                  onChange={newValue => setValue(endDateFieldName, (newValue as Dayjs)?.format(FORMAT_TIME_FULL))}
                />
              </>
            )}
          </Stack>

          {showHelperText && (
            <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
              {helperText && (
                <>
                  <Icon variant="warning" size={1.5} />
                  <StyledFormHelperText>{helperText}</StyledFormHelperText>
                </>
              )}
            </Stack>
          )}
        </Stack>
      )}
    />
  );
};
