import { FC } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import SelectAndTextfield, { TSelectAndTextField } from '@/components/Atoms/SelectAndTextfield';

export type TRHFSelectAndTextfield = {
  name: string;
  selectFieldName: string;
  textfieldFieldName: string;
} & Partial<TSelectAndTextField>;

export const RHFSelectAndTextfield: FC<TRHFSelectAndTextfield> = ({ name, ...restProps }) => {
  const {
    control,
    formState: { errors },
    setValue,
    getValues,
  } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange } }) => {
        const handleChangeValue = ({ field, value }: { field: string; value: string | number }) => {
          if (field === restProps.selectFieldName) {
            setValue(field, value);
          } else {
            onChange(value);
          }
        };
        const transfromValue = {
          [restProps.selectFieldName]:
            typeof getValues(restProps.selectFieldName) === 'string'
              ? getValues(restProps.selectFieldName)
              : getValues(restProps.selectFieldName)?.id,
          [restProps.textfieldFieldName]: getValues(restProps.textfieldFieldName),
        };
        return (
          <SelectAndTextfield
            {...restProps}
            value={transfromValue}
            onChange={handleChangeValue}
            error={!!errors[name]}
            helperText={errors[name] ? (errors[name]?.message as unknown as string) : ''}
          />
        );
      }}
    />
  );
};
