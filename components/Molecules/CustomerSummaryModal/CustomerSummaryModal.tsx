import { Theme } from '@mui/material';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import { useCallback, useState } from 'react';
import Modal from '@/components/Atoms/Modal';
import RangeDateField from '@/components/Atoms/RangeDateField';
import Search from '@/components/Atoms/Search';
import Table, { TColumnTable } from '@/components/Atoms/Table';
import Typography from '@/components/Atoms/Typography';
import { DATE_RANGE_OPTIONS, FORMAT_TIME_FULL } from '@/lib/constants/dateTime';
import { useCustomerSummaryDetail } from '@/lib/hooks/queries/sales';
import useDebounce from '@/lib/hooks/utils/useDebounce';
import { TCustomerSummaryDetail } from '@/lib/types/entities/sales';
import { ERangeDate } from '@/lib/types/enum/report';
import { concatenateNames } from '@/lib/utils/string';
import { customPalettes } from '@/theme/customPalettes';

type TFormValues = {
  remark: string;
};

type TCustomerSummaryModalProps = {
  isOpen: boolean;
  handleClose: () => void;
};

export const CustomerSummaryModal: React.FC<TCustomerSummaryModalProps> = ({ isOpen, handleClose }) => {
  const [keyword, setKeyword] = useState<string>('');
  const [startDate, setStartDate] = useState<string>(dayjs().startOf('month').format(FORMAT_TIME_FULL));
  const [endDate, setEndDate] = useState<string>(dayjs().endOf('month').format(FORMAT_TIME_FULL));
  const [rangeType, setRangeType] = useState(ERangeDate['SELECT_DATE']);
  const debouncedSearch = useDebounce(keyword, 500);
  const { data: customerSummaryDetail } = useCustomerSummaryDetail({
    keySearch: debouncedSearch,
    startTime: dayjs(startDate).startOf('day').format(FORMAT_TIME_FULL),
    endTime: dayjs(endDate).endOf('day').format(FORMAT_TIME_FULL),
  });

  const onChangeRangeTime = useCallback((val: { field: string; value: string }) => {
    if (val.field === 'rangeType') {
      setRangeType(val?.value as ERangeDate);
    }

    if (val.field === 'startDate') {
      setStartDate(val.value);
    }
    if (val.field === 'endDate') {
      setEndDate(val.value);
    }
  }, []);
  const CUSTOMER_SUMMARY_DETAIL_COLUMNS: TColumnTable[] = [
    {
      name: 'checkIn',
      label: 'CHECK IN',
      render: (row: TCustomerSummaryDetail) => (
        <Stack flexDirection="row" gap={1.25}>
          <Typography variant="body-xlarge-400" minWidth="80px">
            {dayjs(row?.checkIn).isValid() ? dayjs(row?.checkIn).format('DD/MM/YYYY') : ''}
          </Typography>
          <Typography variant="body-xlarge-400">
            {dayjs(row?.checkIn).isValid() ? dayjs(row?.checkIn).format('h:mm A') : ''}
          </Typography>
        </Stack>
      ),
    },
    {
      name: 'checkOut',
      label: 'CHECK OUT',
      render: (row: TCustomerSummaryDetail) => (
        <Stack flexDirection="row" gap={1.25}>
          <Typography variant="body-xlarge-400" minWidth="80px">
            {dayjs(row?.checkOut).isValid() ? dayjs(row?.checkOut).format('DD/MM/YYYY') : ''}
          </Typography>
          <Typography variant="body-xlarge-400">
            {dayjs(row?.checkOut).isValid() ? dayjs(row?.checkOut).format('h:mm A') : ''}
          </Typography>
        </Stack>
      ),
    },
    {
      name: 'customer',
      label: "CUSTOMER'S NAME",
      render: (row: TCustomerSummaryDetail) => (
        <Typography variant="body-xlarge-400">
          {concatenateNames(row?.customer?.firstName || '', row?.customer?.lastName || '')}
        </Typography>
      ),
    },
    {
      name: 'customer',
      label: "CUSTOMER'S ID",
      render: (row: TCustomerSummaryDetail) => <Typography variant="body-xlarge-400">{row?.customer?.code}</Typography>,
    },
    {
      name: 'rfid',
      label: 'TYPE',
      render: (row: TCustomerSummaryDetail) => (
        <Typography variant="body-xlarge-400">{row?.rfid?.group?.name}</Typography>
      ),
    },
    {
      name: 'rfid',
      label: 'LOCKER',
      render: (row: TCustomerSummaryDetail) => (
        <Typography variant="body-xlarge-400">
          {typeof row?.rfid?.lockerNumber === 'number' ? `${row?.rfid?.lockerNumber}`?.padStart(3, '0') : ''}
        </Typography>
      ),
    },
    {
      name: 'rfid',
      label: 'RFID',
      render: (row: TCustomerSummaryDetail) => (
        <Typography variant="body-xlarge-400">{row?.rfid?.serialCode}</Typography>
      ),
    },
  ];

  const onSearch = (keyword: string) => {
    setKeyword(keyword);
  };

  return (
    <Modal
      isOpen={isOpen}
      handleClose={handleClose}
      title={
        <Stack flexDirection="row" justifyContent="space-between" alignItems="center" component="div" flex={1}>
          <Stack flexDirection="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
            <Typography component="span" variant="heading-xlarge-700" color={customPalettes?.neutrals?.N50.main}>
              Current Customer
            </Typography>
            <Typography
              component="p"
              sx={theme => ({
                padding: theme.spacing(0.5, 1.5),
                backgroundColor: theme.palette.neutrals.N50.main,
                color: theme.palette.common.white,
                borderRadius: theme.spacing(1.25),
                ...theme.typography['body-xlarge-400'],
              })}
            >
              {customerSummaryDetail?.data?.length || 0}
            </Typography>
          </Stack>
        </Stack>
      }
    >
      <Stack
        sx={theme => ({
          width: theme.spacing(900 / 8),
          minHeight: theme.spacing(500 / 8),
          [theme.breakpoints.down('md')]: {
            width: 'auto',
          },
        })}
      >
        <Stack direction="row" gap="20px">
          <Search
            placeholder="Search"
            onSearch={onSearch}
            sx={(theme: Theme) => ({
              border: `1px solid ${theme.palette.neutrals.N40.main}`,
              borderRadius: '10px',
              '& .MuiInputBase-formControl.MuiOutlinedInput-root.MuiInputBase-adornedStart': { padding: '10px' },
            })}
          />

          <RangeDateField
            onChange={onChangeRangeTime}
            value={{
              startDate,
              endDate,
              rangeType,
            }}
            startDateFieldName="startDate"
            endDateFieldName="endDate"
            rangeTypeFieldName="rangeType"
            rangeDateTypeOptions={DATE_RANGE_OPTIONS}
            formControlProps={{
              sx: {
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'flex-start',
                '& > div': {
                  justifyContent: 'flex-start',
                },
              },
            }}
          />
        </Stack>

        <Table
          columns={CUSTOMER_SUMMARY_DETAIL_COLUMNS}
          tableCellProps={{
            sx: theme => ({
              '&.MuiTableCell-root': {
                borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
                padding: theme.spacing(2, 1),
              },
              '&.MuiTableCell-body': {
                ...theme.typography['body-xlarge-400'],
              },
              '& > h5': {
                fontSize: theme.typography['heading-xsmall-700'],
                textTransform: 'uppercase',
              },
            }),
          }}
          tableContainerProps={{ sx: theme => ({ padding: theme.spacing(0.25) }) }}
          sttProps={{
            sx: theme => ({
              '&.MuiTableCell-root': {
                borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
                padding: theme.spacing(2, 1),
              },
            }),
          }}
          rows={customerSummaryDetail?.data || []}
          showSTT
        />
      </Stack>
    </Modal>
  );
};
