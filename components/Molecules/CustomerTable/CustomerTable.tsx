import { Theme } from '@mui/material/styles';
import React from 'react';
import CustomTable from '@/components/Atoms/Table';
import { TCustomerTableProps } from './CustomerTable.types';

export const CustomerTable: React.FC<TCustomerTableProps> = ({
  loading = false,
  rows,
  columns,
  onRowClick,
  ...restProps
}) => (
  <CustomTable
    stickyHeader
    infinitiLoad
    isLoading={loading}
    onRowClick={(row: any) => {
      if (onRowClick) onRowClick(row);
    }}
    columns={columns.map(column => ({
      tableCellProps: {
        sx: (theme: Theme) => ({
          '&.MuiTableCell-root': {
            borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
            padding: theme.spacing(2, 1),
            ...(column.name === 'code' ? { cursor: 'pointer' } : {}),
          },
          '&.MuiTableCell-body': {
            ...theme.typography['body-xlarge-400'],
          },
          '& > h5': {
            fontSize: theme.typography['heading-xsmall-700'],
            textTransform: 'uppercase',
          },
        }),
      },
      ...column,
    }))}
    sttProps={{
      sx: (theme: Theme) => ({
        '&.MuiTableCell-root': {
          borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
          minWidth: theme.spacing(6),
          padding: theme.spacing(2, 1),
        },
      }),
    }}
    tableContainerProps={{
      sx: { padding: '0 24px 24px', maxHeight: 'calc(100vh - 64px - 24px - 50px - 24px - 24px)', overflow: 'auto' },
    }}
    rows={rows}
    showSTT
    hasHeaderBackground={false}
    {...restProps}
  />
);
