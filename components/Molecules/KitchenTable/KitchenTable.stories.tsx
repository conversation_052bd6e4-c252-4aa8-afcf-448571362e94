import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import KitchenTable from '.';

export default {
  title: 'Molecules/KitchenTable',
  component: KitchenTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof KitchenTable>;

export const Default: Story = {
  args: {
    rows: [],
    columns: [],
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <KitchenTable {...args} />
    </Box>
  ),
};
