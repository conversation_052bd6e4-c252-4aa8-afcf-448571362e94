// TODO: remove Sample-ROW and using dayjs
import { Theme } from '@mui/material/styles';
import React from 'react';
import CustomTable from '@/components/Atoms/Table';
import { TKitchenTableProps } from './KitchenTable.types';

export const KitchenTable: React.FC<TKitchenTableProps> = ({ rows, columns, loading, ...rest }) => (
  <CustomTable
    isLoading={loading}
    columns={columns.map(column => ({
      tableCellProps: {
        sx: (theme: Theme) => ({
          '&.MuiTableCell-root': {
            borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
            padding: theme.spacing(2, 1),
          },
          '&.MuiTableCell-body': {
            verticalAlign: 'center',
            ...theme.typography['body-xlarge-400'],
          },
          '& > h5': {
            fontSize: theme.typography['heading-xsmall-700'],
            textTransform: 'uppercase',
          },
        }),
      },
      ...column,
    }))}
    sttProps={{
      sx: (theme: Theme) => ({
        '&.MuiTableCell-root': {
          borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
          minWidth: theme.spacing(6),
          padding: theme.spacing(2, 1),
        },
        '&.MuiTableCell-body': {
          verticalAlign: 'center',
          ...theme.typography['body-xlarge-400'],
        },
      }),
    }}
    rows={rows}
    tableContainerProps={{
      sx: { padding: 3, maxHeight: 'calc(100vh - 64px - 24px - 50px - 24px - 20px)', overflow: 'auto' },
    }}
    showSTT
    hasHeaderBackground={false}
    {...rest}
  />
);
