import dayjs from 'dayjs';
import {
  TRHFCalendarProps,
  TRHFCheckProps,
  TRHFRangeDateProps,
  TRHFSelectProps,
  TRHFSummarizeByProps,
  TRHFTextFieldProps,
  TRHFTreeViewMultipleSelectProps,
} from '@/components/Molecules/RHFItems/RHFItems.types';
import {
  CUSTOMER_PREPAID_DETAIL_DATE_RANGE_OPTIONS,
  DATE_RANGE_OPTIONS,
  OPENING_CLOSING_CREDIT_TYPE_OPTIONS,
  PREPAID_SERVICE_SALE_TYPE_OPTIONS,
  SORT_BY_OPERATION_OPTIONS,
  SUMMARIZE_BY_DATE_RANGE_OPTIONS,
} from '@/lib/constants/dateTime';
import { OPTIONS_SHOW_ZERO } from '@/lib/constants/status';

type TSelectField =
  | 'status'
  | 'category'
  | 'serviceCategory'
  | 'creditType'
  | 'fieldSelect'
  | 'type'
  | 'treatedById'
  | 'saleEmployee'
  | 'prepaidServiceSaleType'
  | 'paymentMethod'
  | 'sortBy'
  | 'sortByField'
  | 'sortByOrder';

type TCalendar =
  | 'startDate'
  | 'endDate'
  | 'balanceOn'
  | 'purchaseStartDate'
  | 'purchaseEndDate'
  | 'purchasedStartDate'
  | 'purchasedEndDate'
  | 'redeemedStartDate'
  | 'redeemedEndDate';
type TTextfields = 'keySearch';
type TRangeDate = 'rangeDate' | 'prepaidFilterRange' | 'purchaseRangeDate' | 'rangeDate2';
type TTreeViewMultipleSelect = 'treeCategory';
type TCheckboxField = 'showZero' | 'showConversionSummary';
type TSummarizeBy = 'summarizeBy';

export type TKeyOfForm =
  | TCheckboxField
  | TSelectField
  | TTextfields
  | TCalendar
  | TRangeDate
  | TTreeViewMultipleSelect
  | TSummarizeBy;

export type TFieldOptions = {
  [k in TKeyOfForm]?: k extends TTextfields
    ? TRHFTextFieldProps
    : k extends TSelectField
    ? TRHFSelectProps
    : k extends TCalendar
    ? TRHFCalendarProps
    : k extends TRangeDate
    ? TRHFRangeDateProps
    : k extends TTreeViewMultipleSelect
    ? TRHFTreeViewMultipleSelectProps
    : k extends TCheckboxField
    ? TRHFCheckProps
    : k extends TSummarizeBy
    ? TRHFSummarizeByProps
    : Partial<TRHFTextFieldProps>;
};

export type TCustomFieldOptions = {
  [k in TKeyOfForm]?: k extends TTextfields
    ? Partial<TRHFTextFieldProps>
    : k extends TSelectField
    ? Partial<TRHFSelectProps>
    : k extends TCalendar
    ? Partial<TRHFCalendarProps>
    : k extends TRangeDate
    ? Partial<TRHFRangeDateProps>
    : k extends TTreeViewMultipleSelect
    ? Partial<TRHFTreeViewMultipleSelectProps>
    : k extends TCheckboxField
    ? Partial<TRHFCheckProps>
    : Partial<TRHFTextFieldProps>;
};

export const defaultFieldOptions: TFieldOptions = {
  keySearch: {
    type: 'textfield',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'keySearch',
      label: 'Search',
      placeholder: 'Search',
      showHelperText: false,
      showCloseIcon: true,
    },
  },
  startDate: {
    type: 'calendar',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'startDate',
      label: 'Start date',
      minDate: dayjs(),
      showHelperText: false,
    },
  },
  endDate: {
    type: 'calendar',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'endDate',
      label: 'End date',
      minDate: dayjs(),
      showHelperText: false,
    },
  },
  creditType: {
    type: 'select',
    containerProps: {
      flex: '17%',
      maxWidth: '17%',
    },
    attributes: {
      name: 'creditType',
      label: 'Credit Type',
      showCheckbox: true,
      showAllOption: false,
      multiple: true,
      sx: {
        minWidth: 'fit-content',
      },
      showHelperText: false,
    },
  },
  category: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: '204px',
    },
    attributes: {
      name: 'categoryId',
      label: 'Category',
      isGrouping: false,
      showCheckbox: true,
      multiple: true,
      showAllOption: false,
      showHelperText: false,
    },
  },
  serviceCategory: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: '204px',
    },
    attributes: {
      name: 'categoryId',
      label: 'Category',
      isGrouping: true,
      showCheckbox: true,
      multiple: true,
      showAllOption: false,
      showHelperText: false,
    },
  },
  type: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'type',
      label: 'Show Period',
      showCheckbox: false,
      showAllOption: false,
      multiple: false,
      showHelperText: false,
      options: OPENING_CLOSING_CREDIT_TYPE_OPTIONS,
    },
  },
  prepaidServiceSaleType: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'type',
      label: 'Type',
      showCheckbox: true,
      showAllOption: false,
      multiple: true,
      showHelperText: false,
      options: PREPAID_SERVICE_SALE_TYPE_OPTIONS,
    },
  },
  treatedById: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: '20%',
    },
    attributes: {
      name: 'treatedById',
      label: 'Sale employee',
      showCheckbox: true,
      showAllOption: false,
      multiple: true,
      showHelperText: false,
    },
  },
  saleEmployee: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: '20%',
    },
    attributes: {
      name: 'saleEmployee',
      label: 'Sale Employee',
      showCheckbox: true,
      showAllOption: false,
      multiple: true,
      showHelperText: false,
    },
  },
  paymentMethod: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'methodId',
      label: 'Payment Method',
      showCheckbox: true,
      showAllOption: false,
      multiple: true,
      showHelperText: false,
    },
  },
  summarizeBy: {
    type: 'summarizeBy',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'summarizeBy',
      label: 'Summarize By',
      showHelperText: false,
      startDateFieldName: 'startDate',
      endDateFieldName: 'endDate',
      rangeTypeFieldName: 'rangeDateType',
      rangeDateTypeOptions: SUMMARIZE_BY_DATE_RANGE_OPTIONS,
    },
  },

  treeCategory: {
    type: 'treeViewMultipleSelect',
    containerProps: {
      flex: '20%',
      maxWidth: '204px',
    },
    attributes: {
      name: 'categoryId',
      label: 'Category',
      showHelperText: false,
    },
  },
  rangeDate: {
    type: 'rangeDate',
    containerProps: {
      flex: '40%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'rangeDate',
      label: 'SelectDate',
      startDateFieldName: 'purchasedStartDate',
      endDateFieldName: 'purchasedEndDate',
      rangeTypeFieldName: 'rangeDateType',
      showHelperText: false,
    },
  },

  rangeDate2: {
    type: 'rangeDate',
    containerProps: {
      flex: '40%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'rangeDate2',
      label: 'SelectDate',
      startDateFieldName: 'redeemedStartDate',
      endDateFieldName: 'redeemedEndDate',
      rangeTypeFieldName: 'rangeDateType',
      showHelperText: false,
    },
  },
  sortBy: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'sortBy',
      label: 'Sort By Operation',
      showCheckbox: false,
      showAllOption: false,
      multiple: false,
      showHelperText: false,
      options: SORT_BY_OPERATION_OPTIONS,
    },
  },
  sortByField: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'sortBy',
      label: 'Sort by',
      showCheckbox: false,
      showAllOption: false,
      multiple: false,
      showHelperText: false,
      options: [],
    },
  },

  sortByOrder: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'sortOrder',
      label: 'Sort order',
      showCheckbox: false,
      showAllOption: false,
      multiple: false,
      showHelperText: false,
      options: [],
    },
  },
  status: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'statusId',
      label: 'Status',
      showCheckbox: true,
      showAllOption: false,
      multiple: true,
      showHelperText: false,
    },
  },
  balanceOn: {
    type: 'calendar',
    containerProps: {
      flex: '20%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'balanceOn',
      label: 'Show Balance On',
      showHelperText: false,
    },
  },
  prepaidFilterRange: {
    type: 'rangeDate',
    containerProps: {
      flex: '40%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'prepaidFilterRange',
      label: 'Filter',
      startDateFieldName: 'startDate',
      endDateFieldName: 'endDate',
      rangeTypeFieldName: 'prepaidFilter',
      rangeDateTypeOptions: CUSTOMER_PREPAID_DETAIL_DATE_RANGE_OPTIONS,
      showHelperText: false,
    },
  },
  fieldSelect: {
    type: 'select',
    containerProps: {
      flex: '20%',
      maxWidth: '204px',
    },
    attributes: {
      name: 'fieldSelect',
      label: 'Show Field',
      showCheckbox: true,
      showAllOption: false,
      multiple: true,
      showHelperText: false,
    },
  },
  showZero: {
    type: 'checkbox',
    containerProps: {
      flex: '30%',
      maxWidth: '30%',
    },

    attributes: {
      name: 'showZero',
      label: 'Show Zero',
      options: OPTIONS_SHOW_ZERO,
      labelProps: {
        marginBottom: '8px',
        maxHeight: '19.5px',
      },

      gridItemProps: {
        marginTop: '8px',
        minWidth: '120px',
      },
    },
  },
  showConversionSummary: {
    type: 'checkbox',
    containerProps: {
      flex: '30%',
      maxWidth: '30%',
    },

    attributes: {
      name: 'showConversionSummary',
      label: 'Show Conversion Summary',
      options: OPTIONS_SHOW_ZERO,
      labelProps: {
        marginBottom: '8px',
        maxHeight: '19.5px',
      },

      gridItemProps: {
        marginTop: '8px',
        minWidth: '120px',
      },
    },
  },
  purchaseRangeDate: {
    type: 'rangeDate',
    containerProps: {
      flex: '40%',
      maxWidth: 'fit-content',
    },
    attributes: {
      name: 'purchaseRangeDate',
      label: 'Purchase Date',
      startDateFieldName: 'purchaseStartDate',
      endDateFieldName: 'purchaseEndDate',
      rangeTypeFieldName: 'purchaseRangeDateType',
      rangeDateTypeOptions: DATE_RANGE_OPTIONS,
      showHelperText: false,
    },
  },
};
