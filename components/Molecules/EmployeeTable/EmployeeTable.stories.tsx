import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import { TEmployee } from '@/lib/types/entities/employee';
import EmployeeTable from '.';
import { IEmployeeColumn } from './EmployeeTable.types';

export default {
  title: 'Molecules/EmployeeTable',
  component: EmployeeTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof EmployeeTable>;

const SAMPLE_ROWS: TEmployee[] = [];

const SAMPLE_COLUMNS: IEmployeeColumn[] = [
  {
    name: 'fullName',
    label: 'NAME',
  },
  {
    name: 'branch',
    label: 'BRANCH',
  },
  {
    name: 'phone',
    label: 'PHONE',
  },
  {
    name: 'email',
    label: 'EMAIL',
  },
  {
    name: 'status',
    label: 'STATUS',
  },
  {
    name: 'actions',
    label: ' ',
  },
];

export const Default: Story = {
  args: {
    loading: false,
    rows: SAMPLE_ROWS,
    columns: SAMPLE_COLUMNS,
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <EmployeeTable {...args} />
    </Box>
  ),
};
