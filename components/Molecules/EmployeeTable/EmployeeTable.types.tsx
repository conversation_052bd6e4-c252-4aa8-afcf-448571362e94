import { TColumnTable, TTableProps } from '@/components/Atoms/Table/Table';
import { TEmployee } from '@/lib/types/entities/employee';

export interface IEmployeeColumn extends TColumnTable {
  name: keyof TEmployee | 'actions';
}

export type TEmployeeTableProps = {
  loading?: boolean;
  rows: TEmployee[] | [];
  columns: IEmployeeColumn[];
  onSort: (id: string, newPosition: number, beforeId?: string) => void;
} & TTableProps;
