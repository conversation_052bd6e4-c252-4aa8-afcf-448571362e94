import { Divider, Theme } from '@mui/material';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import Button from '@/components/Atoms/Button';
import ConfirmModal from '@/components/Atoms/ConfirmModal';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import { FORMAT_DD_MM_YYYY } from '@/lib/constants/dateTime';
import { TAX, IN } from '@/lib/constants/tax';
import Price from '../Price';
import { StyledTextField } from './MembershipRefundHistory.styled';
import { TMembershipRefundHistoryProps, TRefundNewCreditData } from './MembershipRefundHistory.types';

export const MembershipRefundHistory: React.FC<TMembershipRefundHistoryProps> = ({
  data,
  onRefundNewCredit,
  refundList,
}) => {
  const [dataRefund, setDataRefund] = useState<TRefundNewCreditData[]>([]);
  const [openModal, setOpenModal] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState(false);
  const handleModifyPrice = ({ orderId, indexItem, value }: { orderId: string; indexItem: number; value: number }) => {
    const newValue = dataRefund?.map(order => {
      if (order.orderId !== orderId) return order;
      const findIndxItem = order?.items?.at(indexItem);
      if (!findIndxItem) return order;
      const newItems = order?.items?.map((i, indx) => {
        if (indx === indexItem) return { ...i, nonMemberPrice: value };
        return i;
      });
      return {
        orderId,
        items: newItems,
      };
    });
    setDataRefund(newValue);
  };

  const getInputValue = ({ orderId, indexItem }: { orderId: string; indexItem: number }) => {
    const findValue = dataRefund?.find(d => d.orderId === orderId);
    return findValue?.items?.at(indexItem)?.nonMemberPrice || 0;
  };

  const caculateRefundValue = ({
    memberPrice,
    nonMemberPrice,
    quantity,
  }: {
    memberPrice: number;
    nonMemberPrice: number;
    quantity: number;
  }) => Math.min((memberPrice - nonMemberPrice) * quantity, 0);

  const findRefundItem = useMemo(() => {
    const findRefundItem = refundList?.find(item => item?.referenceNo === data?.referenceNo);
    return findRefundItem;
  }, [data?.referenceNo, refundList]);

  const totalNonMemberShip = useMemo(() => {
    const getOrderHaveNonePrice = dataRefund?.filter(order => order?.items?.some(i => i.nonMemberPrice > 0));
    const totalNonMemberPrice =
      getOrderHaveNonePrice
        ?.flatMap(flat => flat.items)
        ?.reduce(
          (pre, cur) =>
            pre +
            caculateRefundValue({
              memberPrice: cur.memberPrice,
              nonMemberPrice: cur.nonMemberPrice,
              quantity: cur.quantity,
            }),
          0
        ) || 0;
    return totalNonMemberPrice;
  }, [dataRefund]);

  const total = (data?.refund || findRefundItem?.refund || 0) + totalNonMemberShip;
  const taxCaculated = (total * TAX) / 100;
  const totalRefund = total + taxCaculated;

  useEffect(() => {
    const newValue =
      data?.orders?.map<TRefundNewCreditData>(o => ({
        orderId: o.id,
        items: o.items?.map<{
          id: string;
          nonMemberPrice: number;
          quantity: number;
          memberPrice: number;
        }>(i => ({
          id: i?.id || '',
          nonMemberPrice: 0,
          quantity: i?.quantity || 0,
          memberPrice: i.product?.price || 0,
        })),
      })) || [];
    setDataRefund(newValue);
    setIsSubmiting(false);
  }, [JSON.stringify(data?.orders)]);

  const handleSubmit = (isKeepCommissions: boolean) => {
    setIsSubmiting(true);
    onRefundNewCredit(dataRefund, isKeepCommissions);
  };

  return !openModal ? (
    <Stack
      overflow="auto"
      maxHeight="500px"
      width="100%"
      minWidth="1150px"
      gap={2}
      bgcolor="white"
      padding={2}
      borderRadius="10px"
    >
      <Stack flexDirection="row" flexWrap="nowrap" width="100%" justifyContent="space-between" gap={2}>
        <Stack gap={1.5} width="130px">
          <Typography variant="heading-medium-700" color="primary">
            PURCHASE DATE
          </Typography>
          <Typography color="primary" variant="body-xlarge-400">
            {dayjs(data?.purchaseDate).format(FORMAT_DD_MM_YYYY)}
          </Typography>
        </Stack>
        <Stack gap={1.5} width="130px">
          <Typography variant="heading-medium-700">REFERENCE NO.</Typography>
          <Typography color="primary" variant="body-xlarge-400">
            {data?.referenceNo && IN}
            {data?.referenceNo || ''}
          </Typography>
        </Stack>
        <Stack gap={1.5}>
          <Typography variant="heading-medium-700">DESCRIPTION</Typography>
          <Typography color="primary" variant="body-xlarge-400">
            {data?.description || ''}
          </Typography>
        </Stack>
        <Stack gap={1.5} flexGrow="1" alignItems="flex-end">
          <Typography variant="heading-medium-700">REFUND</Typography>
          <Price
            amount={data?.refund || findRefundItem?.refund || 0}
            typographyProps={{
              color: 'primary',
              variant: 'body-xlarge-400',
            }}
          />
        </Stack>
      </Stack>
      <Divider
        sx={(theme: Theme) => ({
          borderBottomWidth: theme.spacing(1 / 16),
          borderColor: theme.palette.neutrals.N40.main,
          width: '100%',
        })}
      />
      <Typography variant="heading-medium-700" color="primary">
        Service has been used
      </Typography>
      {data?.orders?.map(order => (
        <>
          <Stack flexDirection="row" flexWrap="nowrap" width="100%" justifyContent="space-between" gap={2}>
            <Stack gap={1.5} width="130px">
              <Typography color="primary" variant="body-xlarge-400">
                {dayjs(order?.date).format(FORMAT_DD_MM_YYYY)}
              </Typography>
            </Stack>
            <Stack gap={1.5} width="130px">
              <Typography color="primary" variant="body-xlarge-400">
                {order?.referenceNo && IN}
                {order?.referenceNo || ''}
              </Typography>
            </Stack>
            <Stack gap={0.5} flexGrow="1">
              {order?.items?.map((item, indx) => (
                <Stack flexDirection="row" flexWrap="nowrap" alignItems="center" justifyContent="space-between">
                  <Stack flexDirection="row" flexWrap="nowrap" gap={1} alignItems="center" width="300px">
                    <Typography color="primary" variant="body-xlarge-400">
                      {item?.quantity || 0}
                    </Typography>
                    <Typography color="primary" variant="body-xlarge-400">
                      {item?.product?.name || ''}
                    </Typography>
                  </Stack>
                  <Stack flexDirection="row" flexWrap="nowrap" alignItems="center" gap={1} minWidth="170px">
                    <Price
                      prefix="Member price: "
                      amount={item?.product?.price || 0}
                      typographyProps={{
                        color: 'primary',
                        variant: 'body-xlarge-400',
                      }}
                    />
                  </Stack>
                  <Stack flexDirection="row" flexWrap="nowrap" alignItems="center" gap={1}>
                    <Typography color="primary" variant="body-xlarge-400">{`Non-member price: `}</Typography>
                    <StyledTextField
                      value={getInputValue({ orderId: order.id, indexItem: indx })}
                      defaultValue={item?.product?.price || 0}
                      InputProps={{ inputProps: { min: item?.product?.price || 0 } }}
                      type="number"
                      onChange={e => {
                        const value = Number(e.target.value);
                        handleModifyPrice({ orderId: order.id, indexItem: indx, value });
                      }}
                    />
                  </Stack>
                  <Price
                    amount={caculateRefundValue({
                      memberPrice: item?.product?.price || 0,
                      nonMemberPrice: getInputValue({ orderId: order.id, indexItem: indx }),
                      quantity: item?.quantity || 0,
                    })}
                    typographyProps={{
                      color: 'primary',
                      variant: 'body-xlarge-400',
                      width: '80px',
                      overflow: 'auto',
                      maxWidth: '100px',
                      textAlign: 'right',
                    }}
                  />
                </Stack>
              ))}
            </Stack>
          </Stack>
          <Divider
            sx={(theme: Theme) => ({
              borderBottomWidth: theme.spacing(1 / 16),
              borderColor: theme.palette.neutrals.N40.main,
              width: '100%',
            })}
          />
        </>
      ))}
      <Stack flexDirection="row" flexWrap="nowrap" width="100%" justifyContent="space-between" alignItems="center">
        <Typography variant="heading-medium-700" color="primary">
          Total
        </Typography>
        <Price
          amount={total}
          typographyProps={{
            color: 'primary',
            variant: 'heading-medium-700',
          }}
        />
      </Stack>
      <Stack flexDirection="row" flexWrap="nowrap" width="100%" justifyContent="space-between" alignItems="center">
        <Typography variant="heading-medium-700" color="primary">{`Tax ${TAX}%`}</Typography>
        <Price
          amount={taxCaculated}
          typographyProps={{
            color: 'primary',
            variant: 'heading-medium-700',
          }}
        />
      </Stack>
      <Stack flexDirection="row" flexWrap="nowrap" width="100%" justifyContent="space-between" alignItems="center">
        <Typography variant="heading-medium-700" color="primary">
          TOTAL REFUND
        </Typography>
        <Price
          amount={totalRefund}
          typographyProps={{
            color: 'primary',
            variant: 'heading-xlarge-700',
          }}
        />
      </Stack>
      <Button
        sx={{ maxWidth: '300px', margin: '0 auto' }}
        variant="contained"
        label="Refund"
        onClick={() => setOpenModal(true)}
      />
    </Stack>
  ) : (
    <ConfirmModal
      dialogProps={{
        sx: (theme: Theme) => ({
          '.Modal-content > div': {
            maxWidth: '444px',
            padding: theme.spacing(4),
          },
        }),
      }}
      isOpen={openModal}
      title={
        <Stack flexDirection="column" gap={1.5} alignItems="center" justifyContent="center">
          <Icon variant="warning_yellow" size={6} />
          <Typography color="#F5A623" variant="heading-xlarge-700">
            Do you want to keep employee commissions?
          </Typography>
        </Stack>
      }
      bodyContent={
        <Typography color="primary" variant="body-xlarge-400" textAlign="center">
          The employee&apos;s commission when selling this package will be retained or deducted
        </Typography>
      }
      onCancel={() => handleSubmit(false)}
      onClose={() => handleSubmit(false)}
      onConfirm={() => handleSubmit(true)}
      cancelLabel="Yes"
      confirmLabel="No"
      buttonGroupProps={{
        direction: 'row-reverse',
      }}
      cancelButtonProps={{
        disabled: isSubmiting,
      }}
      confirmButtonProps={{
        color: 'primary',
        variant: 'outlined',
        isLoading: isSubmiting,
      }}
    />
  );
};
