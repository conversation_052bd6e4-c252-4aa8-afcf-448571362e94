import { TMembershipRefundHistory, TMembershipRefund } from '@/lib/types/entities/membership';

export type TRefundNewCreditData = {
  orderId: string;
  items: { id: string; memberPrice: number; nonMemberPrice: number; quantity: number }[];
};

export type TMembershipRefundHistoryProps = {
  data?: TMembershipRefundHistory;
  refundList?: TMembershipRefund[];
  onRefundNewCredit: (data: TRefundNewCreditData[], isKeepCommissions: boolean) => void;
};
