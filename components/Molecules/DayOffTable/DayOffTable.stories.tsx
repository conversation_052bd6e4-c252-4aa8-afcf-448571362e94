import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import DayOffTable from '.';
import { IDayOffColumn } from './DayOffTable.types';
import { TDayoff } from '@/lib/types/entities/dayoff';

export default {
  title: 'Molecules/DayOffTable',
  component: DayOffTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof DayOffTable>;

const SAMPLE_ROWS: TDayoff[] = [];

const SAMPLE_COLUMNS: IDayOffColumn[] = [
  {
    name: 'employee',
    label: 'THERAPIST',
  },
  {
    name: 'employee',
    label: 'BRANCH',
  },
  {
    name: 'startDate',
    label: 'No.of days',
  },
  {
    name: 'description',
    label: 'DESCRIPTION',
  },
  {
    name: 'actions',
    label: ' ',
  },
];

export const Default: Story = {
  args: {
    loading: false,
    rows: SAMPLE_ROWS,
    columns: SAMPLE_COLUMNS,
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <DayOffTable {...args} />
    </Box>
  ),
};
