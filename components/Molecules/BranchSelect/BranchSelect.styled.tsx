import styled from '@emotion/styled';
import { MenuItem, Stack, menuItemClasses } from '@mui/material';
import Typography from '@/components/Atoms/Typography';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  // paddingInline: theme.spacing(2),
  flexDirection: 'column',
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.neutrals.N220.main,
}));

export const StyledValueSelected = styled(Typography)(({ theme }) => ({
  color: theme.palette.common.white,
}));

export const StyledMenuContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  justifyContent: 'space-between',
  cursor: 'pointer',
}));

export const StyledMenuItems = styled(MenuItem)(({ theme }) => ({
  ':hover': {
    background: theme.palette.neutrals.N190.main,
  },
  [`&.${menuItemClasses.selected}`]: {
    background: theme.palette.neutrals.N190.main,
  },
  padding: theme.spacing(1),
  maxWidth: '180px',
}));
