import Menu from '@mui/material/Menu';
import Stack from '@mui/material/Stack';
import { useState } from 'react';
import Icon from '@/components/Atoms/Icon';
import Skeleton from '@/components/Atoms/Skeleton';
import Typography from '@/components/Atoms/Typography';
import { TBranch } from '@/lib/types/entities/branch';
import {
  StyledContainer,
  StyledMenuContainer,
  StyledMenuItems,
  StyledTitle,
  StyledValueSelected,
} from './BranchSelect.styled';

export type TBranchSelect = {
  value?: TBranch;
  options: TBranch[];
  onBranchSelect: (value: TBranch) => void;
  isLoading: boolean;
};

export const BranchSelect: React.FC<TBranchSelect> = ({ isLoading, value, options, onBranchSelect }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<any>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const valueShow = value?.name || '';

  return (
    <StyledContainer>
      {isLoading ? (
        <Skeleton />
      ) : (
        <>
          <StyledTitle variant="label-small-400">Branch</StyledTitle>
          <StyledMenuContainer onClick={handleClick}>
            <StyledValueSelected variant="heading-xsmall-700">{valueShow}</StyledValueSelected>
            <Stack>
              <Icon variant="arrow_down" />
            </Stack>
          </StyledMenuContainer>
        </>
      )}
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          paper: {
            sx: theme => ({
              marginTop: theme.spacing(0.5),
              background: theme.palette.neutrals.N230.main,
            }),
          },
        }}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
          sx(theme) {
            return {
              background: theme.palette.neutrals.N230.main,
              color: 'white',
              padding: 0,
            };
          },
        }}
      >
        {options?.map(o => (
          <StyledMenuItems
            key={`branch-${o.id}`}
            selected={o?.id === value?.id}
            onClick={() => {
              onBranchSelect(o);
              handleClose();
            }}
          >
            <Typography
              variant="heading-xsmall-700"
              sx={{
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                width: '100%',
              }}
            >
              {o?.name}
            </Typography>
          </StyledMenuItems>
        ))}
      </Menu>
    </StyledContainer>
  );
};
