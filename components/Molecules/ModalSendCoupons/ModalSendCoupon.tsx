import { Theme } from '@emotion/react';
import { dialogClasses } from '@mui/material/Dialog';
import Stack from '@mui/material/Stack';
import { isEmpty } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { z } from 'zod';
import { CondOperator } from '@nestjsx/crud-request';
import Button from '@/components/Atoms/Button';
import Modal from '@/components/Atoms/Modal';
import Typography from '@/components/Atoms/Typography';
import { SWRKey } from '@/lib/constants/SWRKey';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { useIntersectionObserver } from '@/lib/hooks/utils/useIntersectionObserver';
import { useMutationSendManyCodes } from '@/lib/hooks/mutation/issue-coupon';
import { useCustomer } from '@/lib/hooks/queries/customer';
import useDebounce from '@/lib/hooks/utils/useDebounce';
import { TCustomer } from '@/lib/types/entities/customer';
import { concatenateNames } from '@/lib/utils/string';
import { TTransformPaginationResponse } from '@/lib/utils/transformResponse';
import Autocomplete from '@/components/Atoms/Autocomplete';
import { TOption } from '../RHFItems/RHFAutocomplete/RHFAutocomplete';

type TModalSendCoupon = {
  isOpen: boolean;
  onClose: () => void;
  couponId: string;
};

const arrayEmailValidation = z.array(z.string().email());

export const ModalSendCoupon: React.FC<TModalSendCoupon> = ({ couponId, isOpen, onClose }) => {
  const { showWarning, showSuccess, showError } = useAlert();

  const [customerKeySearch, setCustomerKeySearch] = useState('');

  const [pagination, setPagination] = useState(1);

  const debouncedSearch = useDebounce(customerKeySearch, 300);
  const { data: customerResponse } = useCustomer<TTransformPaginationResponse<TCustomer>>({
    keySearch: debouncedSearch,
    query: {
      pagination: {
        page: pagination,
        limit: SWRKey.CUSTOMER.LIMIT,
      },
      filterOptions: [{ field: 'email', operator: CondOperator.NOT_NULL, value: 'true' }],
    },
    searchFields: ['firstName', 'lastName', 'phone'],
  });

  const customerData = customerResponse?.data;
  const maxPage = customerResponse?.pageCount || 2;

  const { customerOptions }: { customerOptions: TOption[] } = useMemo(
    () => ({
      customerOptions:
        customerData?.map(customer => ({
          id: customer?.id || '',
          name: concatenateNames(customer?.firstName, customer?.lastName),
          phone: customer?.phone || '',
          email: customer?.email,
        })) || [],
    }),
    [customerData]
  );
  const [customerChosen, setCustomerChosen] = useState<TOption[]>([]);
  const [errorText, setErrorText] = useState('');

  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 1 });

  const onLoadMore = () => {
    setPagination(pre => {
      const newPage = pre + 1;
      if (newPage > maxPage) return pre;
      return newPage;
    });
  };

  const onSearch = (event: any) => {
    setCustomerKeySearch(event?.target.value);
    setPagination(1);
  };

  const lastOption = customerOptions?.at(customerOptions.length - 1);

  useEffect(() => {
    if (isIntersecting) onLoadMore();
  }, [isIntersecting]);

  const onChooseCustomer = (value: TOption[]) => {
    setErrorText('');
    setCustomerChosen(value);
  };
  const { onSendManyCodes, isMutating } = useMutationSendManyCodes();

  const onSend = async () => {
    try {
      const emails = customerChosen?.map(customer => customer?.email);
      if (isEmpty(emails)) {
        showWarning({ title: 'empty list!' });
        return;
      }
      // emails Validation
      arrayEmailValidation.parse(emails);
      const response = await onSendManyCodes({ emails, issueCouponId: couponId });
      if (response?.status === 'success') {
        showSuccess({ title: 'email was sent!' });
        onClose();
        setCustomerChosen([]);
      }
    } catch (err) {
      if (err instanceof z.ZodError) {
        // const errorIds = (err?.issues?.flatMap(e => selectedIds?.at(Number(e?.path || -1))?.id) || []) as string[];
        showError({ title: 'Invalid email' });
      }
    }
  };

  const renderOption = (props: React.HTMLAttributes<HTMLLIElement>, option: TOption, { selected }: any) => (
    <li {...props}>
      <Stack
        ref={option?.id === lastOption?.id ? ref : null}
        justifyContent="space-between"
        flexDirection="row"
        width="100%"
      >
        <Typography variant="body-xlarge-400" sx={{ pl: 1 }}>
          {option?.name}
        </Typography>
        <Typography variant="body-xlarge-400" sx={{ pl: 1 }}>
          {option?.email}
        </Typography>
      </Stack>
    </li>
  );

  return (
    <Modal
      handleClose={onClose}
      isOpen={isOpen}
      title="SEND EMAIL"
      dialogProps={{
        sx: (theme: Theme) => ({
          [`& .${dialogClasses.paper}`]: {
            maxWidth: theme.spacing(126.5),
            height: 'fit-content',
            padding: theme.spacing(3),
            maxHeight: 'calc(100% - 48px)',
            backgroundColor: theme.palette.neutrals.N100.main,
            borderRadius: theme.spacing(1.25),
            [theme.breakpoints.down('md')]: {
              maxWidth: 'auto',
              padding: theme.spacing(2),
            },
          },
        }),
      }}
      action={
        <>
          <Button
            loadingText="..."
            isLoading={isMutating}
            label="Cancel"
            sx={{ maxWidth: '135px' }}
            onClick={() => onClose()}
          />
          <Button
            loadingText="..."
            isLoading={isMutating}
            label="Send"
            variant="contained"
            sx={{ maxWidth: '135px' }}
            onClick={() => onSend()}
            // disabled={isEmpty(selectedIds)}
          />
        </>
      }
    >
      <Stack width="700px" gap={1} my={1.75}>
        <Stack flexDirection="row" flexWrap="nowrap" alignItems="center" gap={1.5}>
          <Typography variant="heading-medium-700">Send to</Typography>
          <Typography
            sx={{
              minWidth: '30px',
              height: '16px',
              borderRadius: '10px',
              textAlign: 'center',
              background: 'black',
              color: 'white',
              lineHeight: '16px',
            }}
            variant="body-small-400"
          >
            {customerChosen?.length || 0}
          </Typography>
        </Stack>
        <Stack
          sx={{
            justifyContent: 'center',
          }}
        >
          <Autocomplete
            value={customerChosen}
            filterOptions={(options, state) => options}
            disableCloseOnSelect={false}
            options={customerOptions}
            textfieldProps={{ onChange: onSearch }}
            handleOnChange={onChooseCustomer}
            renderOption={renderOption}
            error={!!errorText}
            helperText={errorText}
            name="cutomers"
          />
        </Stack>
      </Stack>
    </Modal>
  );
};
