import { customPalettes } from '@/theme/customPalettes';

export enum LOCKER_STATUS {
  INACTIVE = 'inActive',
  USED = 'used',
  EMPTY = 'empty',
}

export enum LOCKER_VARIANT {
  MALE = 'male',
  FEMALE = 'female',
}
export const LOCKER_STATUS_COLORS: Record<
  LOCKER_STATUS,
  {
    label: string;
    color?: string;
    labelBackgroundColor?: string;
    wrapperBackgroundColor?: string;
    textColor?: string;
  }
> = {
  [LOCKER_STATUS['INACTIVE']]: {
    label: 'Inactive',
    labelBackgroundColor: customPalettes?.warning?.main,
    wrapperBackgroundColor: customPalettes?.neutrals?.N10?.main,
    textColor: customPalettes?.neutrals?.N190?.main,
  },
  [LOCKER_STATUS['USED']]: {
    label: 'Being used',
    color: customPalettes?.neutrals?.N100?.main,
    labelBackgroundColor: customPalettes?.neutrals?.N160?.main,
    wrapperBackgroundColor: customPalettes?.neutrals?.N10?.main,
    textColor: customPalettes?.neutrals?.N190?.main,
  },
  [LOCKER_STATUS['EMPTY']]: {
    label: 'Empty',
    labelBackgroundColor: customPalettes?.success?.main,
    wrapperBackgroundColor: customPalettes?.common?.white,
    textColor: customPalettes?.common?.white,
  },
};

export const VARIANT_COLORS: Record<LOCKER_VARIANT, { color?: string }> = {
  [LOCKER_VARIANT['FEMALE']]: {
    color: customPalettes?.neutrals?.N310?.main,
  },
  [LOCKER_VARIANT['MALE']]: {
    color: customPalettes?.neutrals?.N300?.main,
  },
};

export type TRFIDLockerProps = {
  text: string;
  status: LOCKER_STATUS;
  variant: LOCKER_VARIANT;
  canEdit: boolean;
  onEdit: () => void;
  onDetail: () => void;
  hasPermissionViewDetailRFID: boolean;
};
