import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import RFIDLocker from '.';
import { LOCKER_STATUS, LOCKER_VARIANT } from './RFIDLocker.types';

export default {
  title: 'Molecules/RFIDLocker',
  component: RFIDLocker,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof RFIDLocker>;

export const Default: Story = {
  args: {
    status: LOCKER_STATUS['USED'],
    variant: LOCKER_VARIANT['FEMALE'],
    text: '0001',
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: 'grey' }}>
      <RFIDLocker {...args} />
    </Box>
  ),
};
