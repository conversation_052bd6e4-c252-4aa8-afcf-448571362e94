import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Typography from '@/components/Atoms/Typography';

export const StyledWrapper = styled(Stack)<{ textColor?: string; backgroundColor?: string }>(
  ({ theme, textColor, backgroundColor }) => ({
    width: 'fit-content',
    position: 'relative',
    borderRadius: theme.spacing(1.25),
    padding: theme.spacing(30 / 8, 22 / 8, 0.75, 22 / 8),
    backgroundColor: backgroundColor || theme.palette.common.white,
    '&:before': {
      content: 'attr(data-text)',
      width: '100%',
      height: '100%',
      position: 'absolute',
      left: 0,
      top: 'calc(50% + 10px)',
      color: textColor || theme.palette.common.white,
      overflow: 'hidden',
      textAlign: 'center',
      ...theme.typography['heading-xlarge-700'],
    },
  })
);

export const StyledStatusLabel = styled(Typography)<{ bgColor?: string }>(({ theme, bgColor }) => ({
  width: 'fit-content',
  position: 'absolute',
  top: theme.spacing(0.75),
  left: theme.spacing(0.75),
  padding: theme.spacing(0.25, 0.5),
  color: theme.palette.common.white,
  backgroundColor: bgColor || theme.palette.success.main,
  borderRadius: theme.spacing(1.25),
  ...theme.typography['heading-xxxsmall-700'],
}));
