import React from 'react';
import Stack from '@mui/material/Stack';
import { LockIcon } from '@/components/Atoms/IconsComponent';
import { LOCKER_STATUS, LOCKER_STATUS_COLORS, TRFIDLockerProps, VARIANT_COLORS } from './RFIDLocker.types';
import { StyledWrapper, StyledStatusLabel } from './RFIDLocker.styled';
import Icon from '@/components/Atoms/Icon';

export const RFIDLocker: React.FC<TRFIDLockerProps> = ({
  text,
  variant,
  status,
  onDetail,
  canEdit,
  onEdit,
  hasPermissionViewDetailRFID,
}) => (
  <StyledWrapper
    data-text={text}
    color={
      status === LOCKER_STATUS['EMPTY'] ? VARIANT_COLORS?.[variant]?.color : LOCKER_STATUS_COLORS?.['used']?.['color']
    }
    backgroundColor={LOCKER_STATUS_COLORS?.[status]?.['wrapperBackgroundColor']}
    textColor={LOCKER_STATUS_COLORS?.[status]?.['textColor']}
  >
    <LockIcon />
    <StyledStatusLabel bgColor={LOCKER_STATUS_COLORS?.[status]?.labelBackgroundColor}>
      {LOCKER_STATUS_COLORS?.[status]?.label}
    </StyledStatusLabel>
    {status === LOCKER_STATUS['USED'] ? (
      <Stack
        sx={theme => ({
          width: theme.spacing(3),
          height: theme.spacing(3),
          position: 'absolute',
          top: theme.spacing(0.75),
          right: theme.spacing(0.75),
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: theme.palette.common.white,
          borderRadius: '50%',
          cursor: hasPermissionViewDetailRFID ? 'pointer' : 'not-allowed',
          opacity: hasPermissionViewDetailRFID ? 1 : '50%',
        })}
        onClick={onDetail}
      >
        <Icon variant="bold_eye" size={14 / 8} />
      </Stack>
    ) : (
      <Stack
        sx={theme => ({
          width: theme.spacing(3),
          height: theme.spacing(3),
          position: 'absolute',
          top: theme.spacing(0.75),
          right: theme.spacing(0.75),
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: theme.palette.neutrals.N210.main,
          borderRadius: '50%',
          cursor: canEdit ? 'pointer' : 'not-allowed',
          opacity: canEdit ? 1 : '50%',
        })}
        onClick={onEdit}
      >
        <Icon variant="bold_edit" size={14 / 8} />
      </Stack>
    )}
  </StyledWrapper>
);
