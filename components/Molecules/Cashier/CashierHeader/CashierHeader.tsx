import React, { useMemo } from 'react';
import Icon, { IconVariantTypes } from '@/components/Atoms/Icon/Icon';
import Typography from '@/components/Atoms/Typography';
import { CustomSwitch, TPriceType } from '../../../Atoms/CustomSwitch/CustomSwitch';
import {
  StyledAction,
  StyledActionContainer,
  StyledCount,
  StyledHeader,
  StyledHeaderBackButton,
  StyledHeaderTitle,
} from './CashierHeader.styled';

type TCashierHeader = {
  handleClose: () => void;
  title?: string;
  onCartClick: () => void;
  onOrderClick: () => void;
  cardCount: number;
  orderCount: number;
  onChangePriceType: (val: TPriceType) => void;
  activePriceType: TPriceType | string;
  hiddenSwitch: boolean;
  hasPermissionViewOrder: boolean;
};

export const CashierHeader: React.FC<TCashierHeader> = ({
  handleClose,
  title,
  onCartClick,
  onOrderClick,
  cardCount = 0,
  orderCount = 0,
  onChangePriceType,
  activePriceType,
  hiddenSwitch,
  hasPermissionViewOrder,
}) => {
  const ACTIONS: {
    iconType: IconVariantTypes;
    label: string;
    count: number;
    onClick: () => void;
    disabled: boolean;
  }[] = [
    {
      iconType: 'cart',
      label: 'Cart',
      count: cardCount,
      onClick: onCartClick,
      disabled: false,
    },
    {
      iconType: 'bell',
      label: 'Orders',
      count: orderCount,
      onClick: onOrderClick,
      disabled: !hasPermissionViewOrder,
    },
  ];

  return (
    <StyledHeader>
      <StyledHeaderBackButton variant="back" size={4} onClick={handleClose} />
      {title && <StyledHeaderTitle>{title}</StyledHeaderTitle>}
      <StyledActionContainer>
        {hiddenSwitch ? (
          <></>
        ) : (
          <CustomSwitch onChange={val => onChangePriceType(val as TPriceType)} activeValue={activePriceType} />
        )}
        {ACTIONS?.map(({ iconType, label, count, onClick, disabled }) => (
          <StyledAction
            key={iconType}
            onClick={() => {
              if (disabled) return;
              onClick();
            }}
            $disabled={disabled}
          >
            <Icon variant={iconType} />
            <Typography variant="heading-medium-700">{label}</Typography>
            <StyledCount $disabled={disabled} variant="heading-medium-700">
              {count}
            </StyledCount>
          </StyledAction>
        ))}
      </StyledActionContainer>
    </StyledHeader>
  );
};
