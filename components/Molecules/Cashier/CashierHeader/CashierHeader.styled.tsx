import styled from '@emotion/styled';
import { Chip } from '@mui/material';
import Stack from '@mui/material/Stack';
import Typography from '@/components/Atoms/Typography';
import Icon from '@/components/Atoms/Icon/Icon';

export const StyledHeader = styled(Stack)(({ theme }) => ({
  width: '100%',
  justifyContent: 'center',
  alignItems: 'center',
  flex: 0,
  position: 'relative',
  padding: theme.spacing(1.5, 3),
  backgroundColor: theme.palette.neutrals.N20.main,
  minHeight: theme.spacing(7),
}));

export const StyledHeaderBackButton = styled(Icon)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: theme.spacing(3),
  transform: 'translateY(-50%)',
  cursor: 'pointer',
}));

export const StyledHeaderTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.neutrals.N50.main,
  textTransform: 'uppercase',
  ...theme.typography['heading-xlarge-700'],
}));

export const StyledActionContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(1.5),
  width: '100%',
  justifyContent: 'flex-end',
}));

export const StyledAction = styled(Stack)<{ $disabled: boolean }>(({ theme, $disabled }) => ({
  padding: theme.spacing(0.5, 1.5),
  borderRadius: theme.spacing(4),
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.spacing(1.25),
  background: $disabled ? theme.palette.neutrals.N40.main : 'white',
  justifyContent: 'space-between',
  height: theme.spacing(4),
  width: theme.spacing(18.75),
  cursor: $disabled ? 'not-allowed' : 'pointer',
}));

export const StyledCount = styled(Typography)<{ $disabled: boolean }>(({ theme, $disabled }) => ({
  background: $disabled ? 'white' : theme.palette.neutrals.N220.main,
  borderRadius: theme.spacing(3),
  padding: theme.spacing(0.25, 1),
  color: $disabled ? 'black' : 'white',
  minWidth: theme.spacing(5),
  textAlign: 'center',
}));
