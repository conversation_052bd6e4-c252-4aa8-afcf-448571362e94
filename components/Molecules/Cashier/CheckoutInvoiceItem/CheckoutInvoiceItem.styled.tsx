import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  flexDirection: 'row',
  gap: theme.spacing(1.5),
  justifyContent: 'space-between',
}));

export const StyledStatus = styled(Stack)<{ isPaid: boolean }>(({ theme, isPaid }) => ({
  justifyContent: 'center',
  background: isPaid ? theme.palette.success.main : theme.palette.neutrals.N160.main,
  color: 'white',
  width: '104px',
  alignItems: 'center',
  height: '22px',
  borderRadius: '5px',
}));
