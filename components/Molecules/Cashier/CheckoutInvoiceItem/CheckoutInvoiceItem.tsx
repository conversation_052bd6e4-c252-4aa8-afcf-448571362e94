import { Stack } from '@mui/material';
import dayjs from 'dayjs';
import Typography from '@/components/Atoms/Typography';
import { TOrderItem } from '@/lib/types/entities/order';
import { EInvoiceStatus } from '@/lib/types/enum/invoice';
import { Price } from '../../Price/Price';
import { StyledContainer, StyledStatus } from './CheckoutInvoiceItem.styled';

type TCheckoutInvoiceItem = {
  time?: string;
  items?: Partial<TOrderItem>[];
  status?: EInvoiceStatus;
  tax?: number;
  totalPrice: number;
};

export const CheckoutInvoiceItem: React.FC<TCheckoutInvoiceItem> = ({
  time,
  items,
  status,
  totalPrice: totalPriceOrder,
}) => {
  const timeFormat = dayjs(time).format('HH:mm');
  return (
    <StyledContainer>
      <Typography width="58px" variant="body-xlarge-400">
        {timeFormat}
      </Typography>
      <Stack gap={0.5} flex={1}>
        {items?.map(i => (
          <Typography key={i?.id} variant="heading-small-700">
            {i?.product?.name}
          </Typography>
        ))}
      </Stack>
      {status && <StyledStatus isPaid={status === EInvoiceStatus.PAID}>{status}</StyledStatus>}
      <Price amount={Number(totalPriceOrder)} typographyProps={{ variant: 'heading-xlarge-700' }} />
    </StyledContainer>
  );
};
