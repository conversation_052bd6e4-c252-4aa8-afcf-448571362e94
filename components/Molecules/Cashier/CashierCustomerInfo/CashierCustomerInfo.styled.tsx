import styled from '@emotion/styled';
import Avatar, { avatarClasses } from '@mui/material/Avatar';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  gap: theme.spacing(1.5),
  flexWrap: 'nowrap',
  background: 'white',
  padding: theme.spacing(2),
  borderRadius: '10px',
  flexDirection: 'row',
  justifyContent: 'space-between',
  backgroundColor: theme.palette.neutrals.N100.main,
}));

export const StyledCustomerInfo = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(1.5),
  alignItems: 'center',
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  minWidth: theme.spacing(8.5),
}));

export const StyledCustomerInfoContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(2.25),
  alignItems: 'flex-start',
  padding: theme.spacing(1.5),
  borderRadius: theme.spacing(1.25),
  background: 'white',
  border: `1px solid ${theme.palette.neutrals.N180.main}`,
}));

export const StyledAvatar = styled(Avatar)(({ theme }) => ({
  [`&.${avatarClasses.root}`]: {
    width: theme.spacing(8),
    height: theme.spacing(8),
    textTransform: 'uppercase',
    color: theme.palette.common.white,
    backgroundColor: theme.palette.neutrals.N50.main,
    ...theme.typography['heading-xlarge-700'],
  },
}));
