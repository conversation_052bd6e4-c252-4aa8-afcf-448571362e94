import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import Typography from '@/components/Atoms/Typography';
import {
  StyledAvatar,
  StyledCustomerInfo,
  StyledCustomerInfoContainer,
  StyledTitle,
} from './CashierCustomerInfo.styled';
import { round2decimal } from '@/lib/utils/string';

dayjs.extend(utc);
dayjs.extend(timezone);

type TCashierCustomerInfo = {
  rfid: string;
  name: string;
  phone: string;
  credit: number;
  avatar: string;
  oldCredit: number;
  isCreditNewExpired: boolean;
  isCreditOldExpired: boolean;
  isCreditNewShow: boolean;
  isCreditOldShow: boolean;
  passportExpiryDate?: string;
};

export const CashierCustomerInfo: React.FC<TCashierCustomerInfo> = ({
  name,
  credit,
  avatar,
  oldCredit,
  isCreditNewExpired,
  isCreditOldExpired,
  passportExpiryDate,
  isCreditNewShow,
  isCreditOldShow,
}) => (
  <StyledCustomerInfoContainer>
    <StyledAvatar src={avatar}>{name?.[0]}</StyledAvatar>
    <Stack gap="4px">
      <Typography variant="heading-xmedium-700" textTransform="uppercase">
        {name}
      </Typography>
      {/* <StyledCustomerInfo>
        <StyledTitle variant="heading-small-700">Phone</StyledTitle>
        <StyledTitle variant="body-large-400">{phone || ''}</StyledTitle>
      </StyledCustomerInfo>
      <StyledCustomerInfo>
        <StyledTitle variant="heading-small-700">RFID</StyledTitle>
        <StyledTitle variant="body-large-400">{rfid}</StyledTitle>
      </StyledCustomerInfo> */}
      {isCreditNewShow && (
        <StyledCustomerInfo>
          <StyledTitle variant="heading-small-700">Credit</StyledTitle>
          <StyledTitle variant="heading-small-700">{`$ ${round2decimal(Math.round(credit * 100) / 100)}`}</StyledTitle>
          <Typography
            variant="body-medium-400"
            color="white"
            sx={{ background: isCreditNewExpired ? 'red' : '#3BA716', borderRadius: '10px', padding: '4px 10px' }}
          >
            {isCreditNewExpired ? 'Expired' : 'Valid'}
          </Typography>
        </StyledCustomerInfo>
      )}
      {isCreditOldShow && (
        <StyledCustomerInfo>
          <StyledTitle variant="heading-small-700">Old Credit</StyledTitle>
          <StyledTitle variant="heading-small-700">{`$ ${round2decimal(
            Math.round(oldCredit * 100) / 100
          )}`}</StyledTitle>
          <Typography
            variant="body-medium-400"
            color="white"
            sx={{ background: isCreditOldExpired ? 'red' : '#3BA716', borderRadius: '10px', padding: '4px 10px' }}
          >
            {isCreditOldExpired ? 'Expired' : 'Valid'}
          </Typography>
        </StyledCustomerInfo>
      )}
      {passportExpiryDate && (
        <StyledCustomerInfo>
          <StyledTitle variant="heading-small-700">Passport</StyledTitle>
          <StyledTitle variant="heading-small-700">{dayjs.utc(passportExpiryDate).format('DD/MM/YYYY')}</StyledTitle>
        </StyledCustomerInfo>
      )}
    </Stack>
  </StyledCustomerInfoContainer>
);
