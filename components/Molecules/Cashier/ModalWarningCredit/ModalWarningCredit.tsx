import Stack from '@mui/material/Stack';
import Icon from '@/components/Atoms/Icon';
import Modal from '@/components/Atoms/Modal';
import Typography from '@/components/Atoms/Typography';
import { StyledButton } from './ModalWarningCredit.styled';

type TModalWarningCreditProps = {
  isOpen: boolean;
  handleClose: () => void;
  onSubmit: () => void;
};

export const ModalWarningCredit: React.FC<TModalWarningCreditProps> = ({ isOpen, handleClose, onSubmit }) => (
  <Modal
    isOpen={isOpen}
    handleClose={handleClose}
    showCloseButton={false}
    action={<StyledButton variant="contained" label="Unlock Non-member Menu" onClick={onSubmit} />}
  >
    <Stack flexDirection="column" gap={1.5} alignItems="center" justifyContent="center" maxWidth="478px">
      <Icon variant="warning_yellow" size={6} />
      <Typography color="#F5A623" variant="heading-xlarge-700">
        Not enough credits
      </Typography>
      <Typography color="primary" variant="body-xlarge-400" textAlign="center">
        Your credit is not enough to make the payment. Please confirm switching to the Non-member menu to continue
        ordering
      </Typography>
    </Stack>
  </Modal>
);
