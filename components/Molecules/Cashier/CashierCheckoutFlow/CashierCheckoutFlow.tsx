import { Stack } from '@mui/material';
import { useMemo, useState } from 'react';
import { mutate } from 'swr';
import { isEmpty } from 'lodash';
import { TAppointment } from '@/lib/types/entities/appointment';
import { TInvoice } from '@/lib/types/entities/invoice';
import { TotalOutStandingHeader } from '../../TotalOutstandingHeader/TotalOutstandingHeader';
import { CashierCheckoutListing } from '../CashierCheckoutListing/CashierCheckoutListing';
import { CashierPaymentCaculator } from '../CashierPaymentCaculator/CashierPaymentCaculator';
import { SWRKey } from '@/lib/constants/SWRKey';
import { ECouponType } from '@/lib/types/enum/coupon';
import { TVoucherChecked } from '@/lib/hooks/utils/useCouponApply';

export type TMockInvoices = Partial<TInvoice> & { items: { id: string; name: string }[] };

// NOTE: handle step checkout
const ALL_STEPS = ['paymentListing', 'paymentCaculator'] as const;

export type TAllPaymentStep = (typeof ALL_STEPS)[number];

type TCashierCheckoutFlow = {
  onPayment: () => void;
  onCloseModal: () => void;
  onContinueOrder: () => void;
  appointmentDetail?: TAppointment;
  lockerNumber: number;
};

export const CashierCheckoutFlow: React.FC<TCashierCheckoutFlow> = ({
  appointmentDetail,
  onPayment,
  onCloseModal,
  lockerNumber,
  onContinueOrder,
}) => {
  const [modalState, setModalState] = useState(0);
  const [paymentIds, setPaymentId] = useState<{ orderIds: string[]; invoiceIds: string[] }>({
    orderIds: [],
    invoiceIds: [],
  });
  // TODO: checking with API
  const [completedIds, setCompletedIds] = useState<string[]>([]);

  const resetState = () => {
    setPaymentId({ orderIds: [], invoiceIds: [] });
  };

  const onNext = () => {
    setModalState(pre => pre + 1);
  };

  const onBack = () => {
    setModalState(pre => pre - 1);
    resetState();
  };

  const onPaymentOrderClick = ({ orderIds, invoiceIds = [] }: { orderIds: string[]; invoiceIds?: string[] }) => {
    setPaymentId({ orderIds, invoiceIds });
    onNext();
  };

  const onPaymentComplete = () => {
    setPaymentId({ orderIds: [], invoiceIds: [] });
    setCompletedIds(pre => [...pre, ...paymentIds.orderIds, ...paymentIds.invoiceIds]);
    // Checking complete Invoice
    const orderIds = appointmentDetail?.orders?.map(o => o.id) || [];
    const invoiceIds = appointmentDetail?.invoices?.map(i => i.id) || [];
    const isRemainOrder = [...orderIds, ...invoiceIds].some(
      id => ![...completedIds, ...paymentIds.invoiceIds, ...paymentIds.orderIds].includes(id || '')
    );

    // refresh data rfid by code
    mutate(SWRKey.RFID.getRFIDByCode(appointmentDetail?.rfid || ''));
    onBack();
    // if (isRemainOrder) return onBack();
    // onPayment();
  };

  const ordersPayment = appointmentDetail?.orders?.filter(o => paymentIds.orderIds.includes(o?.id || ''));
  const invoicePayment = appointmentDetail?.invoices?.filter(o => paymentIds.invoiceIds.includes(o?.id || ''));

  const defaultCouponApply: TVoucherChecked | undefined = useMemo(() => {
    if (invoicePayment?.[0]) {
      return {
        voucherChoose:
          invoicePayment?.[0]?.newPaid?.invoiceCoupon
            ?.map(res => ({
              id: res.id,
              couponType: res?.couponType || ECouponType.MONEY,
              couponCode: `${
                res?.couponType === ECouponType.PERCENTAGE
                  ? res?.percent || ''
                  : res?.couponCode || res?.discountValue || ''
              }`,
            }))
            ?.filter(res => !isEmpty(res?.couponCode)) || [],
        voucherResponse:
          invoicePayment?.[0]?.newPaid?.invoiceCoupon?.map(res => ({
            id: res.id,
            discountProductId: res.id,
            discountMoney: res.discountValue || 0,
            couponType: res?.couponType || ECouponType.MONEY,
            couponCode: res?.couponCode,
            couponName: res?.couponName,
            percent: res?.percent,
          })) || [],
      };
    }
    return undefined;
  }, [invoicePayment]);

  const STEPS_SETTING: Record<TAllPaymentStep, any> = {
    paymentListing: (
      <CashierCheckoutListing
        rfid={appointmentDetail?.rfid}
        checkInTime={appointmentDetail?.checkIn}
        customer={appointmentDetail?.customer}
        orders={appointmentDetail?.orders?.filter(o => !completedIds.includes(o?.id || ''))}
        invoices={appointmentDetail?.invoices?.filter(i => !completedIds.includes(i?.id || ''))}
        onCloseModal={onCloseModal}
        onNextStep={onNext}
        lockerNumber={lockerNumber}
        onPayment={onPaymentOrderClick}
        onCheckout={onPayment}
        onContinueOrder={onContinueOrder}
      />
    ),
    paymentCaculator: (
      <Stack width="800px" gap={3}>
        <TotalOutStandingHeader title="Payment" onBack={onBack} />
        <CashierPaymentCaculator
          orders={ordersPayment}
          invoices={invoicePayment}
          rfid={appointmentDetail?.rfid}
          onComplete={onPaymentComplete}
          appointmentId={appointmentDetail?.id || ''}
          customerInfo={appointmentDetail?.customer}
          defaultCouponApply={defaultCouponApply}
          defaultPaymentMethods={invoicePayment?.[0]?.invoicePayments}
        />
      </Stack>
    ),
  };

  return STEPS_SETTING[ALL_STEPS[modalState]];
};
