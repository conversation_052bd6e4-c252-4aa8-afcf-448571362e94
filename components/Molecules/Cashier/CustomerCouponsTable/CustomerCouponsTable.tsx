import React, { useEffect } from 'react';
import dayjs from 'dayjs';
import { formatCurrency } from '@/lib/utils/string';
import Typography from '@/components/Atoms/Typography';
import Table, { TColumnTable } from '@/components/Atoms/Table';
import { TCustomerCoupon } from '@/lib/types/entities/coupon';
import { StyledUseButton } from './CustomerCouponsTable.styled';
import { UseCountEditable } from './UseCountEditable/UseCountEditable';
import { FORMAT_DD_MM_YYYY } from '@/lib/constants/dateTime';

export type TCustomerCouponsTableProps = {
  coupons: TCustomerCoupon[];
  usedCouponCodes: string[];
  onUse: (coupon: TCustomerCoupon) => void;
};

export const CustomerCouponsTable: React.FC<TCustomerCouponsTableProps> = ({ coupons, usedCouponCodes, onUse }) => {
  const [localData, setLocalData] = React.useState<TCustomerCoupon[]>([]);

  useEffect(() => {
    setLocalData(
      coupons?.map((item: TCustomerCoupon) => {
        const availableCodes = item?.codes?.filter(code => !usedCouponCodes?.includes(code));
        const usedCount = item?.codes?.length - availableCodes?.length;
        return {
          ...item,
          use: 1,
          usedCount,
          count: Number(item?.count || 0) + usedCount,
          codes: availableCodes,
          isAvailable: availableCodes?.length > 0,
        };
      }) || []
    );
  }, [coupons, usedCouponCodes]);

  const onUpdateUseCount = (id: string, value: string) => {
    const temp = [...localData];
    const foundIndex = temp?.findIndex(data => data?.id === id);
    const newCountUse = Number(value);
    if (foundIndex > -1 && !Number.isNaN(newCountUse)) {
      temp[foundIndex] = {
        ...temp[foundIndex],
        use: newCountUse,
      };
      setLocalData(temp);
    }
  };

  const CUSTOMER_SUMMARY_DETAIL_COLUMNS: TColumnTable[] = [
    {
      name: 'couponname',
      label: 'COUPON',
    },
    {
      name: 'qty',
      label: 'QTY',
      render: (row: TCustomerCoupon) => (
        <Typography variant="body-xlarge-400">
          {`${row?.codes?.length || 0}`.padStart(2, '0')} /{' '}
          {`${(row?.codes?.length || 0) + (row?.usedCount || 0)}`.padStart(2, '0')}
        </Typography>
      ),
    },
    {
      name: 'expireddate',
      label: 'EXPIRED DATE',
      render: (row: TCustomerCoupon) => dayjs(row?.expiredDate).format(FORMAT_DD_MM_YYYY),
    },
    {
      name: 'price',
      label: 'PRICE',
      render: (row: TCustomerCoupon) => (
        <Typography variant="body-xlarge-400">{formatCurrency(row?.price || 0)}</Typography>
      ),
    },
    {
      name: 'use',
      label: 'USE',
      tableCellProps: { sx: { minWidth: '64px' } },
      render: (row: TCustomerCoupon) => (
        <UseCountEditable value={`${row?.use}`} onChange={value => onUpdateUseCount(row?.id || '', value)} />
      ),
    },
    {
      name: 'actions',
      label: '',
      render: (row: TCustomerCoupon) => (
        <StyledUseButton
          onClick={() => onUse(row)}
          sx={theme => ({
            opacity: row.isAvailable ? 1 : 0.5,
            cursor: row.isAvailable ? 'pointer' : 'not-allowed',
            pointerEvents: row.isAvailable ? 'auto' : 'none',
          })}
        >
          {row.isAvailable ? 'Use' : 'Used'}
        </StyledUseButton>
      ),
    },
  ];
  return (
    <Table
      columns={CUSTOMER_SUMMARY_DETAIL_COLUMNS}
      tableCellProps={{
        sx: theme => ({
          '&.MuiTableCell-root': {
            borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
            padding: theme.spacing(2, 1),
          },
          '&.MuiTableCell-body': {
            ...theme.typography['body-xlarge-400'],
          },
          '& > h5': {
            fontSize: theme.typography['heading-xsmall-700'],
            textTransform: 'uppercase',
          },
        }),
      }}
      tableContainerProps={{
        sx: theme => ({ padding: theme.spacing(2) }),
      }}
      rows={localData || []}
      showSTT={false}
    />
  );
};
