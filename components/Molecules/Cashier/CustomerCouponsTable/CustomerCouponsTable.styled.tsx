import styled from '@emotion/styled';
import Box from '@mui/material/Box';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';

export const StyledTitleWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  width: '100%',
  position: 'relative',
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  width: '100%',
}));

export const StyledCloseIcon = styled(Icon)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  right: 0,
  cursor: 'pointer',
}));

export const StyledUseButton = styled(Typography)(({ theme }) => ({
  width: 'fit-content',
  minWidth: theme.spacing(75 / 8),
  padding: theme.spacing(0.25, 0.5),
  borderRadius: theme.spacing(1.25),
  color: theme.palette.common.white,
  textAlign: 'center',
  backgroundColor: theme.palette.success.main,
  textTransform: 'capitalize',
  cursor: 'pointer',
  ...theme.typography['heading-medium-700'],
}));
