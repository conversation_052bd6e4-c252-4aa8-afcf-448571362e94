import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import { Box } from '@mui/material';
import Typography from '@/components/Atoms/Typography';

export const StyledWrapper = styled(Stack)(({ theme }) => ({
  minWidth: theme.spacing(90 / 8),
  width: 'fit-content',
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.spacing(1.5),
  padding: theme.spacing(0, 1.5),
  borderRadius: theme.spacing(1.25),
  textAlign: 'center',
}));

export const StyledTextEditableWrapper = styled(Box)<{ isBold?: boolean; isUppercase?: boolean }>(
  ({ theme, isBold, isUppercase }) => ({
    flex: 1,
    '.category_setting_item--texteditable': {
      border: '1px solid transparent',
      whiteSpace: 'nowrap',
      textTransform: isUppercase ? 'uppercase' : 'none',
      ...(isBold ? theme.typography['heading-medium-700'] : theme.typography['body-xlarge-400']),
      '&:focus': {
        border: '1px solid transparent',
      },
    },
  })
);

export const StyledCounterText = styled(Typography)(({ theme }) => ({
  width: 'fit-content',
  minWidth: theme.spacing(8),
  padding: theme.spacing(0.25, 0.5),
  borderRadius: theme.spacing(1.25),
  color: theme.palette.neutrals.N50.main,
  textAlign: 'center',
  backgroundColor: theme.palette.neutrals.N210.main,
  textTransform: 'uppercase',
  ...theme.typography['body-large-400'],
}));
