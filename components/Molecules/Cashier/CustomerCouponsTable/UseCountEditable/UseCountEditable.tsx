'use client';

import React, { useRef, useState } from 'react';
import ContentEditable from 'react-contenteditable';
import { StyledTextEditableWrapper, StyledWrapper, StyledCounterText } from './UseCountEditable.styled';

export type TUseCountEditableProps = {
  value: string;
  onChange: (value: string) => void;
};

export const UseCountEditable: React.FC<TUseCountEditableProps> = ({ value, onChange }) => {
  const ref = useRef<HTMLDivElement>(null);
  const editableRef = useRef<HTMLDivElement>(null);
  const [focused, setFocused] = useState<boolean>(false);

  return (
    <>
      <StyledWrapper ref={ref}>
        {focused ? (
          <StyledTextEditableWrapper>
            <ContentEditable
              innerRef={editableRef}
              html={value} // innerHTML of the editable div
              disabled={false} // use true to disable editing
              onChange={() => onChange(editableRef.current?.innerText || '')} // handle innerHTML change
              onFocus={() => setFocused(true)}
              onBlur={() =>
                setTimeout(() => {
                  setFocused(false);
                }, 300)
              }
              className="category_setting_item--texteditable"
            />
          </StyledTextEditableWrapper>
        ) : (
          <StyledCounterText onClick={() => setFocused(true)}>{value}</StyledCounterText>
        )}
      </StyledWrapper>
    </>
  );
};
