import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Avatar, { avatarClasses } from '@mui/material/Avatar';
import Typography from '@/components/Atoms/Typography';
import Button from '@/components/Atoms/Button';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(2.25),
  alignItems: 'flex-start',
  background: 'white',
  justifyContent: 'space-between',
  borderRadius: theme.spacing(1.25),
}));

export const StyledContainerWrapper = styled(Stack)(({ theme }) => ({
  gap: theme.spacing(1.5),
  padding: theme.spacing(1.5),
  borderRadius: theme.spacing(1.25),
  background: 'white',
  overflow: 'auto',
  height: 'fit-content',
  maxHeight: '265px',
  justifyContent: 'space-between',
}));

export const StyledCustomerInfo = styled(Stack)(({ theme }) => ({
  width: '100%',
  flexDirection: 'row',
  gap: theme.spacing(1.5),
  alignItems: 'center',
  justifyContent: 'space-between',
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  minWidth: theme.spacing(9),
}));

export const StyledAvatar = styled(Avatar)(({ theme }) => ({
  [`&.${avatarClasses.root}`]: {
    width: theme.spacing(8),
    height: theme.spacing(8),
    textTransform: 'uppercase',
    cursor: 'pointer',
    color: theme.palette.common.white,
    backgroundColor: theme.palette.neutrals.N50.main,
    ...theme.typography['heading-xxlarge-700'],
  },
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  width: '100% ',
  flex: 1,
  padding: theme.spacing(1.5, 0),
  backgroundColor: `${theme.palette.neutrals.N210.main} !important`,
  color: `${theme.palette.primary.main} !important`,
  border: 'none !important',
}));
