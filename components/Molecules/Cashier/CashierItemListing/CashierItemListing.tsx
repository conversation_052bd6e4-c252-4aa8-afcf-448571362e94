import { useEffect, useMemo, useState } from 'react';
import { CustomImage } from '@/components/Atoms/CustomImage/CustomImage';
import Typography from '@/components/Atoms/Typography';
import Price from '../../Price';
import {
  StyledContainer,
  StyledImageWrapper,
  StyledItem,
  StyledItemContainer,
  StyledItemImageWrapper,
  StyledListingContainer,
  StyledSectionItem,
  StyledSectionListing,
  StyledTitle,
} from './CashierItemListing.styled';
import { TItemCashier } from '../CashierSearch/CashierSearch';

export type TCashierCategory = {
  id: string;
  name: string;
  avatar?: { id: string; url: string };
  children: Array<TCashierCategory>;
  items?: (TItemCashier & { status?: { id: string; name: string; type: string } })[];
};

type TCashierItemListing = {
  cashierCategory: TCashierCategory[];
  haveSectionLevel: boolean;
  onItemSelect: (item: TItemCashier) => void;
};

export const CashierItemListing: React.FC<TCashierItemListing> = ({
  haveSectionLevel,
  cashierCategory,
  onItemSelect,
}) => {
  const [activeSectionId, setActiveSectionId] = useState<string>();
  const {
    sectionListing,
    categoryListing,
  }: { sectionListing: Array<Pick<TCashierCategory, 'id' | 'name' | 'avatar'>>; categoryListing: TCashierCategory[] } =
    useMemo(() => {
      const indexOfCategory = cashierCategory?.findIndex(cas => cas?.id === activeSectionId);
      const categoryListing = haveSectionLevel ? cashierCategory[indexOfCategory]?.children : cashierCategory;
      const sectionListing = haveSectionLevel
        ? cashierCategory?.map(cas => ({ id: cas?.id, name: cas?.name, avatar: cas?.avatar }))
        : [];
      return { sectionListing, categoryListing };
    }, [cashierCategory, haveSectionLevel, activeSectionId]);

  const onSectionSelect = (id: string) => {
    setActiveSectionId(id);
  };

  useEffect(() => {
    if (!activeSectionId) {
      setActiveSectionId(cashierCategory[0]?.id);
    }
  }, [cashierCategory]);

  useEffect(() => {
    if (cashierCategory?.[0]?.id) {
      setActiveSectionId(cashierCategory?.[0]?.id);
    }
  }, [cashierCategory?.[0]?.id]);

  return (
    <StyledContainer>
      {sectionListing?.length > 0 && (
        <StyledSectionListing>
          {sectionListing?.map(s => (
            <StyledSectionItem key={s.id} isActive={activeSectionId === s.id} onClick={() => onSectionSelect(s?.id)}>
              <StyledImageWrapper>
                <CustomImage src={s?.avatar?.url || ''} alt="" />
              </StyledImageWrapper>
              <Typography variant="heading-medium-700">{s?.name}</Typography>
            </StyledSectionItem>
          ))}
        </StyledSectionListing>
      )}

      {categoryListing?.length > 0 &&
        categoryListing?.map(category => (
          <StyledListingContainer key={category?.id}>
            <StyledTitle variant="heading-large-700">{category?.name}</StyledTitle>
            <StyledItemContainer>
              {category?.items?.map(
                i =>
                  i.status?.name === 'Active' && (
                    <StyledItem key={i?.id} onClick={() => onItemSelect(i)} style={{ padding: '12px' }}>
                      <StyledItemImageWrapper>
                        <CustomImage
                          src={i?.avatar?.url || ''}
                          alt=""
                          style={{
                            objectFit: 'cover',
                          }}
                        />
                        <Price
                          amount={i?.price}
                          containerProps={{
                            sx: theme => ({
                              position: 'absolute',
                              borderRadius: theme.spacing(3.75),
                              bottom: '6px',
                              right: '6px',
                              padding: theme.spacing(0.5, 1.5),
                              background: 'white',
                              ...theme.typography['heading-xsmall-700'],
                            }),
                          }}
                          typographyProps={{ variant: 'heading-xsmall-700' }}
                        />
                      </StyledItemImageWrapper>
                      <Typography variant="heading-medium-700" width="100%">
                        {i?.name}
                      </Typography>
                    </StyledItem>
                  )
              )}
            </StyledItemContainer>
          </StyledListingContainer>
        ))}
    </StyledContainer>
  );
};
