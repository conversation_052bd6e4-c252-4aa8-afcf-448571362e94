import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Typography from '@/components/Atoms/Typography';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  height: '100%',
  backgroundColor: theme.palette.neutrals.N10.main,
  padding: theme.spacing(3),
  gap: theme.spacing(3),
  maxHeight: 'calc( 100dvh - 56px )',
  overflow: 'auto',
  flex: '75%',
  [`${theme.breakpoints.down('lg')}`]: {
    flex: '50%',
  },
}));

export const StyledSectionListing = styled(Stack)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(5,1fr)',
  gap: theme.spacing(1.5),
  overflow: 'auto',
  minHeight: theme.spacing(72 / 8),
  height: 'fit-content',
}));

export const StyledImageWrapper = styled(Stack)(({ theme }) => ({
  minWidth: theme.spacing(5.25),
  height: theme.spacing(5.25),
  background: theme.palette.neutrals.N270.main,
  borderRadius: '50%',
  position: 'relative',
  img: {
    borderRadius: '50%',
  },
}));

export const StyledSectionItem = styled(Stack)<{ isActive: boolean }>(({ theme, isActive }) => ({
  borderRadius: theme.spacing(1.25),
  padding: theme.spacing(1.5),
  background: isActive ? 'white' : theme.palette.neutrals.N100.main,
  border: isActive ? `3px solid ${theme.palette.neutrals.N220.main} ` : 'none',
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.spacing(1.25),
  ':hover': {
    cursor: 'pointer',
  },
}));

export const StyledListingContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  backgroundColor: theme.palette.neutrals.N10.main,
  gap: theme.spacing(1.5),
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.neutrals.N220.main,
  textTransform: 'uppercase',
  letterSpacing: ' 0.3px',
}));

export const StyledItemContainer = styled(Stack)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(5,1fr)',
  gap: theme.spacing(1.5),
  [`${theme.breakpoints.down('lg')}`]: {
    gridTemplateColumns: 'repeat(3,1fr)',
  },
  [`${theme.breakpoints.down('md')}`]: {
    gridTemplateColumns: 'repeat(2,1fr)',
  },
}));

export const StyledItem = styled(Stack)(({ theme }) => ({
  borderRadius: theme.spacing(1.25),
  background: theme.palette.neutrals.N100.main,
  gap: theme.spacing(1.25),
  alignItems: 'center',
  height: '100%',
  width: '100%',
  paddingTop: 0,
  ':hover': {
    cursor: 'pointer',
  },
}));

export const StyledItemImageWrapper = styled(Stack)(({ theme }) => ({
  position: 'relative',
  overflow: 'hidden',
  height: theme.spacing(15),
  borderRadius: theme.spacing(1.25),
  width: '100%',
  img: {
    borderRadius: theme.spacing(1.25),
  },
}));

export const StyledPrice = styled(Stack)(({ theme }) => ({
  borderRadius: theme.spacing(3.75),
  position: 'absolute',
  bottom: '6px',
  right: '6px',
  padding: theme.spacing(0.5, 1.5),
  background: 'white',
  ...theme.typography['heading-xsmall-700'],
}));
