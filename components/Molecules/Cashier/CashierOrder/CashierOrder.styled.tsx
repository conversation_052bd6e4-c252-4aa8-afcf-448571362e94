import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import { IconButton } from '@mui/material';
import { TextField } from '@/components/Atoms/TextField/TextField';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  borderRadius: theme.spacing(1.25),
  padding: theme.spacing(1.5),
  background: 'white',
  height: '100%',
  overflow: 'auto',
  flex: '2',
  [`${theme.breakpoints.down('lg')}`]: {
    flex: '2',
  },
}));

export const StyledItemContainer = styled(Stack)(({ theme }) => ({
  height: '100%',
  overflow: 'auto',
  '&::-webkit-scrollbar': {
    width: '5px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: '#f1f1f1',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: '#888',
    borderRadius: '3px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    backgroundColor: '#555',
  },
}));

export const StyledRemarkContainer = styled(Stack)(({ theme }) => ({
  height: '40%',
  overflow: 'auto',
  justifyContent: 'flex-end',
}));

export const StyledMenuItem = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(1),
  width: '100%',
}));

export const StyledImageWrapper = styled(Stack)(({ theme }) => ({
  position: 'relative',
  maxWidth: '40px',
  height: '40px',
  borderRadius: '10px',
  flex: '1 1 40px',
  alignItems: 'center',
  overflow: 'hidden',
}));
export const StyledBody = styled(Stack)(({ theme }) => ({
  gap: theme.spacing(0.5),
  flex: '1 1 180px',
}));

export const StyledCounter = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(0.5),
  alignItems: 'center',
}));

export const StyledCountBtn = styled(IconButton)(() => ({
  padding: 0,
  [`.Mui-disabled`]: {
    opacity: 0.3,
    pointerEvents: 'none',
  },
}));

export const StyledCountLabel = styled(TextField)(({ theme }) => ({
  maxWidth: theme.spacing(8),
  [`.MuiInputBase-input`]: {
    borderRadius: theme.spacing(10),
    textAlign: 'center',
    background: theme.palette.neutrals.N210.main,
    padding: theme.spacing(1),
    ...theme.typography['body-large-400'],
  },
  [`.MuiOutlinedInput-notchedOutline`]: {
    borderRadius: theme.spacing(1.25),
    border: 'unset',
  },
}));

export const StyledEmployee = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(0.5),
  alignItems: 'center',
  padding: theme.spacing(0.25, 0.5),
  background: theme.palette.neutrals.N210.main,
  borderRadius: theme.spacing(1.25),
}));
