import { IconButton, Stack } from '@mui/material';
import ListItem from '@mui/material/ListItem';
import { isEmpty } from 'lodash';
import React, { useContext } from 'react';
import { CustomImage } from '@/components/Atoms/CustomImage/CustomImage';
import Icon from '@/components/Atoms/Icon/Icon';
import Typography from '@/components/Atoms/Typography';
import { TOrder, TOrderDetail } from '@/lib/types/entities/order';
import Price from '../../Price';
import { TInvoice } from '@/lib/types/entities/invoice';
import { CashierMenuContext } from '@/lib/context/CashierMenuContext';

import {
  StyledBody,
  StyledContainer,
  StyledCountLabel,
  StyledCounter,
  StyledCountBtn,
  StyledImageWrapper,
  StyledItemContainer,
  StyledRemarkContainer,
  StyledMenuItem,
} from './CashierOrder.styled';

type TOrderItems = TOrder['items'];

type TCashierOrder = {
  orderDetail?: TOrder;
  invoiceDetail?: TInvoice;
  items: TOrderItems;
  onCounterChange: (id: string, type: 'increment' | 'decrement') => void;
  onRemoveItem: (id: string, couponCode?: string) => void;
  onClearOrder: () => void;
  onOpenModalRemark: (itemId: string, note?: string, couponCode?: string, typeConfirmCustom?: string) => void;
  onInputCounterChange: any;
};

export const CashierOrder: React.FC<TCashierOrder> = ({
  items = [],
  onCounterChange,
  onRemoveItem,
  onClearOrder,
  onOpenModalRemark,
  onInputCounterChange,
  orderDetail,
  invoiceDetail,
}) => {
  const handleClearAll = () => {
    items?.forEach(i => {
      onRemoveItem(i?.product?.id || '', i?.couponCode || '');
    });
    onClearOrder();
  };

  const { remarkTotalNote } = useContext(CashierMenuContext);

  return (
    <StyledContainer>
      {!isEmpty(items) && (
        <Typography
          onClick={handleClearAll}
          variant="body-large-400"
          sx={theme => ({
            position: 'sticky',
            top: 0,
            textAlign: 'right',
            color: theme.palette.neutrals.N110.main,
            cursor: 'pointer',
          })}
        >
          Clear all
        </Typography>
      )}
      <StyledItemContainer>
        {items?.map(i => (
          <ListItem
            key={i?.product?.id}
            divider
            sx={{
              paddingTop: '12px',
              paddingBottom: '12px',
              paddingInline: 0,
            }}
          >
            <StyledMenuItem>
              <StyledImageWrapper>
                <CustomImage src={i?.product?.avatar?.url || ''} alt={i?.product?.name || ''} />
              </StyledImageWrapper>
              <StyledBody>
                <Stack flexDirection="row" alignItems="flex-start" gap="8px">
                  <Typography sx={{ flex: 1 }} variant="body-large-400">
                    {i?.product?.name}
                  </Typography>

                  <Stack gap={1.5} justifyContent="center">
                    <Price
                      amount={i?.couponCode ? 0 : i?.product?.price}
                      typographyProps={{
                        width: 'fit-content',
                        minWidth: '25px',
                        textAlign: 'right',
                        variant: 'heading-medium-700',
                      }}
                    />
                  </Stack>
                  <IconButton
                    sx={{ padding: 0 }}
                    onClick={() => onRemoveItem(i?.product?.id || '', i?.couponCode || '')}
                  >
                    <Icon variant="close" />
                  </IconButton>
                </Stack>

                {(i?.product?.type !== 'coupon' || (i?.product?.type === 'coupon' && !i?.couponCode)) && (
                  <StyledCounter>
                    <IconButton onClick={() => onCounterChange(i?.product?.id || '', 'decrement')}>
                      <Icon size={2.5} variant="subtract_round" />
                    </IconButton>
                    <StyledCountLabel
                      // variant="body-large-400"
                      hiddenLabel
                      size="small"
                      value={i?.quantity}
                      onChange={e => onInputCounterChange(i?.product?.id || '', Number(e?.target?.value))}
                    />
                    {i?.product?.type === 'coupon' ? (
                      <StyledCountBtn>
                        <IconButton
                          disabled={(i?.quantity ?? 0) >= (i?.product?.remain ?? 0)}
                          color="warning"
                          onClick={() => onCounterChange(i?.product?.id || '', 'increment')}
                          title={
                            (i?.quantity ?? 0) >= (i?.product?.remain ?? 0)
                              ? `Maximum quantity reached. Only ${i?.product?.remain ?? 0} coupons available.`
                              : undefined
                          }
                        >
                          <Icon size={2.5} variant="plus_round" />
                        </IconButton>
                      </StyledCountBtn>
                    ) : (
                      <IconButton onClick={() => onCounterChange(i?.product?.id || '', 'increment')}>
                        <Icon size={2.5} variant="plus_round" />
                      </IconButton>
                    )}

                    <Typography
                      sx={{ ':hover': { cursor: 'pointer' }, marginLeft: 'auto' }}
                      variant="heading-xxsmall-700"
                      color="#316DC9"
                      textAlign="right"
                      onClick={() => onOpenModalRemark(i?.product?.id || '', i?.note || '', i?.couponCode)}
                    >
                      Remark
                    </Typography>
                  </StyledCounter>
                )}
                {!isEmpty(i?.couponCode) && (
                  <Typography maxWidth="180px" noWrap textOverflow="ellipsis" variant="heading-xxsmall-700">
                    {i?.couponCode}
                  </Typography>
                )}
                {!isEmpty(i?.note) && (
                  <Typography maxWidth="180px" noWrap textOverflow="ellipsis" variant="heading-xxsmall-700">
                    {i?.note}
                  </Typography>
                )}
              </StyledBody>
            </StyledMenuItem>
          </ListItem>
        ))}
      </StyledItemContainer>
      {!isEmpty(items) && (
        <StyledRemarkContainer>
          <Stack marginBottom="15px" flexDirection="row" justifyContent="flex-end" alignItems="end">
            <Typography
              variant="heading-medium-700"
              color="#316DC9"
              sx={{
                cursor: 'pointer',
              }}
              textAlign="right"
              onClick={() =>
                onOpenModalRemark(
                  '',
                  orderDetail?.note || invoiceDetail?.note || remarkTotalNote?.note,
                  '',
                  'invoiceFull'
                )
              }
            >
              Remark
            </Typography>
          </Stack>
          <Typography fontSize={16}>{orderDetail?.note || invoiceDetail?.note || remarkTotalNote?.note}</Typography>
        </StyledRemarkContainer>
      )}
    </StyledContainer>
  );
};
