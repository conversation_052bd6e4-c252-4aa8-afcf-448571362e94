import { Theme } from '@mui/material';
import Box from '@mui/material/Box';
import { dialogClasses } from '@mui/material/Dialog';
import React, { useState } from 'react';
import Button from '@/components/Atoms/Button';
import ComponentPortal from '@/components/Atoms/ComponentPortal';
import Modal from '@/components/Atoms/Modal';
import Search from '@/components/Atoms/Search';
import { useCouponByCustomer } from '@/lib/hooks/queries/coupon';
import { useIssueCouponCodes } from '@/lib/hooks/queries/issue-coupon';
import { TCustomerCoupon } from '@/lib/types/entities/coupon';
import { TCouponCode } from '@/lib/types/entities/issue-coupon';
import { TTransformPaginationResponse } from '@/lib/utils/transformResponse';
import CustomerCouponsTable from '../CustomerCouponsTable';
import { StyledButtonContainer, StyledHeaderWrapper, StyledSearchWrapper } from './ComboCouponModal.styled';
import { useMutationCheckCoupon } from '@/lib/hooks/mutation/coupon';
import { useAlert } from '@/lib/hooks/context/useAlert';

export type TComboCouponModalProps = {
  isOpen: boolean;
  customerId: string;
  usedComboCodes: string[];
  onUse: (coupon: TCustomerCoupon) => void;
  onClose: () => void;
};

export const ComboCouponModal: React.FC<TComboCouponModalProps> = ({
  isOpen,
  customerId,
  usedComboCodes,
  onUse,
  onClose,
}) => {
  const [couponId, setCounponId] = useState('');
  const { showError, showWarning } = useAlert();
  const { isMutating, onCheckCoupon } = useMutationCheckCoupon();
  const onSearch = (keyword: string) => {
    setCounponId(keyword);
  };
  const { data } = useCouponByCustomer(customerId, true);

  const handleApplyCoupon = async () => {
    if (usedComboCodes.includes(couponId)) {
      return showWarning({ title: 'The coupon has been selected by you' });
    }
    const res = await onCheckCoupon({ customerId, code: couponId });
    if (res.status === 'success') {
      return onUse(res.data);
    }
    showError({ title: 'No coupon code exists' });
  };

  return (
    <ComponentPortal>
      <Modal
        isOpen={isOpen}
        handleClose={onClose}
        title="USE COMBO COUPON"
        dialogProps={{
          sx: (theme: Theme) => ({
            [`& .${dialogClasses.paper}`]: {
              maxWidth: theme.spacing(112.75),
              height: 'fit-content',
              padding: theme.spacing(3),
              maxHeight: 'calc(100% - 48px)',
              backgroundColor: theme.palette.neutrals.N100.main,
              borderRadius: theme.spacing(1.25),
              [theme.breakpoints.down('md')]: {
                maxWidth: 'auto',
                padding: theme.spacing(2),
              },
            },
          }),
        }}
      >
        <StyledHeaderWrapper>
          <StyledSearchWrapper>
            <Search placeholder="Coupon Code" onSearch={onSearch} />
          </StyledSearchWrapper>

          <StyledButtonContainer>
            <Button
              color="primary"
              isLoading={isMutating}
              label="Apply"
              onClick={handleApplyCoupon}
              variant="contained"
              fullWidth={false}
            />
          </StyledButtonContainer>
        </StyledHeaderWrapper>
        <Box
          sx={(theme: Theme) => ({
            width: theme.spacing(112.75),
            overflow: 'hidden',
            [theme.breakpoints.down('md')]: {
              width: 'auto',
            },
          })}
        >
          <CustomerCouponsTable coupons={data || []} usedCouponCodes={usedComboCodes} onUse={onUse} />
        </Box>
      </Modal>
    </ComponentPortal>
  );
};
