import Stack from '@mui/material/Stack';
import { useEffect, useState } from 'react';
import { Box, Theme } from '@mui/material';
import { dialogClasses } from '@mui/material/Dialog';
import { TextField } from '@/components/Atoms/TextField/TextField';
import Modal from '@/components/Atoms/Modal';
import { StyledButton } from './ShopifyCodeModal.styled';

type TFormValues = {
  billCode: string;
};

type TShopifyCodeModal = {
  isOpen: boolean;
  defaultValues?: TFormValues;
  handleClose: () => void;
  handleConfirm: (value: TFormValues) => void;
};

export const ShopifyCodeModal: React.FC<TShopifyCodeModal> = ({
  isOpen,
  handleClose,
  handleConfirm,
  defaultValues,
}) => {
  const [billCode, setBillCode] = useState<string>();

  useEffect(() => {
    setBillCode(defaultValues?.billCode || '');
    return () => setBillCode('');
  }, [defaultValues]);

  return (
    <Modal
      isOpen={isOpen}
      handleClose={handleClose}
      title="Code"
      dialogProps={{
        sx: (theme: Theme) => ({
          [`& .${dialogClasses.paper}`]: {
            maxWidth: theme.spacing(500 / 8),
            height: 'fit-content',
            padding: theme.spacing(3),
            maxHeight: 'calc(100% - 48px)',
            backgroundColor: theme.palette.neutrals.N100.main,
            borderRadius: theme.spacing(1.25),
            [theme.breakpoints.down('md')]: {
              maxWidth: 'auto',
              padding: theme.spacing(2),
            },
          },
        }),
      }}
    >
      <Stack
        direction="row"
        gap={1.5}
        sx={(theme: Theme) => ({
          width: theme.spacing(500 / 8),
          alignItems: 'stretch',
          [theme.breakpoints.down('md')]: {
            width: 'auto',
          },
        })}
      >
        <Box width="79%" height="100%">
          <TextField
            variant="outlined"
            sx={{ mb: 0 }}
            value={billCode}
            placeholder="Enter your code"
            type="text"
            onChange={e => {
              setBillCode(e?.target?.value);
            }}
            formControlProps={{
              sx: {
                width: '100%',
                height: '100%',
              },
            }}
            inputProps={{
              sx: { height: '100% !important' },
            }}
          />
        </Box>
        <Box width="20%" height="100%">
          <StyledButton
            variant="contained"
            label="Confirm"
            disabled={!billCode}
            fullWidth
            onClick={() => {
              handleConfirm({ billCode: billCode || '' });
              setBillCode('');
            }}
          />
        </Box>
      </Stack>
    </Modal>
  );
};
