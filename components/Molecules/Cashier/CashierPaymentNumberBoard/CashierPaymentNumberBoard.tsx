import Typography from '@/components/Atoms/Typography';
import { StyledContainer, StyledKeyboard } from './CashierPaymentNumberBoard.styled';

type TCashierPaymentNumberBoard = {
  onNumberPress: (v: { value: string | number; type: 'default' | 'clear' | 'decimal' }) => void;
};

const keyboardNumbs = [7, 8, 9, 4, 5, 6, 1, 2, 3, 'c', 0, ','];

export const CashierPaymentNumberBoard: React.FC<TCashierPaymentNumberBoard> = ({ onNumberPress }) => (
  <StyledContainer>
    {keyboardNumbs?.map(value => (
      <StyledKeyboard
        key={`${value}`}
        onClick={() => {
          if (typeof value === 'number') {
            return onNumberPress({ value, type: 'default' });
          }
          if (value === 'c') {
            return onNumberPress({ value, type: 'clear' });
          }
          if (value === ',') {
            return onNumberPress({ value, type: 'decimal' });
          }
        }}
      >
        <Typography minHeight="27px" variant="heading-xlarge-700">
          {value}
        </Typography>
      </StyledKeyboard>
    ))}
  </StyledContainer>
);
