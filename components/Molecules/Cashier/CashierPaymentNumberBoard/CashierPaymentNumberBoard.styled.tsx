import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  gap: theme.spacing(1.5),
  flexWrap: 'wrap',
  background: 'white',
  borderRadius: '10px',
  flexDirection: 'row',
  justifyContent: 'space-between',
  backgroundColor: theme.palette.neutrals.N100.main,
}));

export const StyledKeyboard = styled(Stack)(({ theme }) => ({
  flex: '1 1 30%',
  textAlign: 'center',
  justifyContent: 'center',
  alignContent: 'center',
  borderRadius: theme.spacing(1.25),
  border: `1px solid ${theme.palette.neutrals.N180.main}`,
  background: 'white',
  padding: theme.spacing(1.5, 3),
  cursor: 'pointer',
}));
