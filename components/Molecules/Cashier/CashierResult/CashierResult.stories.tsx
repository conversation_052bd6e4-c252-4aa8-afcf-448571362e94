import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import { CashierResult } from './CashierResult';

export default {
  title: 'Molecules/CashierResult',
  component: CashierResult,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof CashierResult>;

// const mockItems: TCashierResult = {
//   subTotal:15,
//   tax:8
//   total:10
// };

export const Default: Story = {
  args: {},
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <CashierResult {...args} />
    </Box>
  ),
};
