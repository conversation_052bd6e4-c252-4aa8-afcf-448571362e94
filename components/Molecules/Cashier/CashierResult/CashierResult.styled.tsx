import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import { inputBaseClasses } from '@mui/material';
import Select from '@/components/Atoms/Select';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  borderRadius: theme.spacing(1.25),
  padding: theme.spacing(1.5),
  background: theme.palette.common.white,
  justifyContent: 'space-between',
  overflow: 'auto',
  flex: '1 1 5%',
  maxHeight: '100px',
}));

export const StyledLineContainer = styled(Stack)(({ theme }) => ({
  justifyContent: 'space-between',
  alignItems: 'center',
  flexDirection: 'row',
}));

export const StyledLineWithSelect = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.spacing(1.5),
}));

export const StyledSelect = styled(Select)(({ theme }) => ({
  [`&.${inputBaseClasses.root}`]: {
    minWidth: '34px',
    height: '26px',
  },
}));
