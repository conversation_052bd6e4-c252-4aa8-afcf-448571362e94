import { Stack } from '@mui/material';
import React, { useMemo } from 'react';
import Button from '@/components/Atoms/Button';
import Typography from '@/components/Atoms/Typography';
import { useCashierLogic } from '@/lib/hooks/utils/useCashierLogic';
import { TOrderItem } from '@/lib/types/entities/order';
import Price from '../../Price';
import { StyledContainer, StyledLineContainer } from './CashierResult.styled';

export type TCashierResult = {
  items: TOrderItem[];
  isUpdate: boolean;
  isLoading: boolean;
  onHoldCard: (val: { subtotal: number; taxCalculated: number; totalBill: number }) => void;
  onConfirm: (val: { subtotal: number; taxCalculated: number; totalBill: number }) => void;
  onCancel: () => void;
};

export const CashierResult: React.FC<TCashierResult> = ({
  items,
  onHoldCard,
  onConfirm,
  isUpdate,
  isLoading,
  onCancel,
}) => {
  const { subTotal, taxCalculated, totalBill, isDisableCheckout } = useCashierLogic({ items, isUpdate });

  const isDisableAction = isDisableCheckout;

  return (
    <>
      <StyledContainer>
        <Stack gap={1}>
          <StyledLineContainer>
            <Typography variant="heading-medium-700">Subtotal</Typography>
            <Price amount={subTotal} typographyProps={{ variant: 'heading-medium-700' }} />
          </StyledLineContainer>

          {/* <StyledLineContainer>
            <Typography variant="heading-medium-700">{`Tax(${TAX}%)`}</Typography>
            <Price amount={Number(taxCalculated)} typographyProps={{ variant: 'heading-medium-700' }} />
          </StyledLineContainer> */}

          {/* <Divider /> */}
          {/* <StyledLineContainer>
            <Typography variant="heading-medium-700">Total</Typography>
            <Price amount={Number(totalBill)} typographyProps={{ variant: 'heading-xlarge-700' }} />
          </StyledLineContainer> */}
        </Stack>
        <Stack flexDirection="row" flexWrap="wrap" gap={1}>
          <Button
            variant={isUpdate ? 'outlined' : 'contained'}
            color="primary"
            label={isUpdate ? 'Cancel' : 'Hold card'}
            disabled={isDisableAction}
            sx={{ flex: '40%' }}
            onClick={() => {
              if (isUpdate) return onCancel();
              onHoldCard({ totalBill, taxCalculated: Number(taxCalculated), subtotal: Number(subTotal) });
            }}
          />
          <Button
            variant="contained"
            isLoading={isLoading}
            sx={{ flex: '40%' }}
            color="success"
            label={isUpdate ? 'Update' : 'Confirm'}
            loadingText={`${isUpdate ? 'Updating' : 'Confirming'} ...`}
            disabled={isDisableAction}
            onClick={() => onConfirm({ totalBill, taxCalculated: Number(taxCalculated), subtotal: Number(subTotal) })}
          />
        </Stack>
      </StyledContainer>
    </>
  );
};
