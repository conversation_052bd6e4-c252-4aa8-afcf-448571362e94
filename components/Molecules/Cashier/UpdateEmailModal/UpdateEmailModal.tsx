import { dialogClasses } from '@mui/material/Dialog';
import React, { useState } from 'react';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import { isEmpty } from 'lodash';
import ComponentPortal from '@/components/Atoms/ComponentPortal';
import Modal from '@/components/Atoms/Modal';
import { TextField } from '@/components/Atoms/TextField/TextField';
import { StyledButton } from './UpdateEmailModal.styled';
import { useMutationCustomerUpdate } from '@/lib/hooks/mutation/customer';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { TApiError } from '@/lib/utils/transformResponse';

export type TUpdateEmailModalProps = {
  isOpen: boolean;
  customerId: string;
  onSuccess: () => void;
  onClose: () => void;
};

export const UpdateEmailModal: React.FC<TUpdateEmailModalProps> = ({ customerId, isOpen, onSuccess, onClose }) => {
  const { onUpdateCustomer, isMutating: updating } = useMutationCustomerUpdate();
  const [email, setEmail] = useState<string>('');
  const { showError, showSuccess } = useAlert();
  const onEdit = async () => {
    const response = await onUpdateCustomer({
      id: customerId,
      email,
    });
    if (response.status === 'error') {
      if (typeof response?.message?.error === 'string') {
        showError({ title: response?.message?.error });
        return;
      }
      if (Array.isArray(response.message.error)) {
        response.message.error.forEach(err => showError({ title: err.constraints }));
        return;
      }
      // It's a TApiError
      const apiError = response.message.error as TApiError;
      showError({ title: apiError?.message });

      return;
    }
    showSuccess({ title: 'Updated !' });
    onSuccess();
  };
  return (
    <ComponentPortal>
      <Modal
        isOpen={isOpen}
        handleClose={onClose}
        title="UPDATE EMAIL"
        dialogProps={{
          fullScreen: false,
          sx: theme => ({
            color: theme.palette.primary.main,
            [`& .${dialogClasses.paper}`]: {
              margin: theme.spacing(3),
              padding: theme.spacing(3),
              height: 'fit-content',
              backgroundColor: theme.palette.neutrals.N100.main,
              borderRadius: theme.spacing(1.25),
              [theme.breakpoints.down('md')]: {
                width: '100%',
              },
            },
            [`& .${dialogClasses.root}`]: {
              [theme.breakpoints.down('md')]: {
                width: '100%',
              },
            },
          }),
        }}
      >
        <Stack
          sx={theme => ({
            width: theme.spacing(502 / 8),
            gap: theme.spacing(18 / 8),
            borderRadius: theme.spacing(1.25),
            overflow: 'hidden',
            [theme.breakpoints.down('md')]: {
              width: 'auto',
            },
          })}
        >
          <Stack direction="row" gap={1.5}>
            <Box width="80%">
              <TextField
                variant="outlined"
                sx={{ mb: 0 }}
                value={email}
                placeholder="Your email"
                type="text"
                onChange={e => setEmail(e.target.value)}
                formControlProps={{
                  sx: {
                    width: '100%',
                  },
                }}
              />
            </Box>
            <Box width="20%">
              <StyledButton
                variant="contained"
                label="Confirm"
                fullWidth
                disabled={isEmpty(email)}
                onClick={onEdit}
                isLoading={updating}
                loadingText="Confirming"
              />
            </Box>
          </Stack>
        </Stack>
      </Modal>
    </ComponentPortal>
  );
};
