import styled from '@emotion/styled';
import Box from '@mui/material/Box';
import { buttonClasses } from '@mui/material/Button';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import Button from '@/components/Atoms/Button';

export const StyledTitleWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  width: '100%',
  position: 'relative',
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  width: '100%',
}));

export const StyledCloseIcon = styled(Icon)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  right: 0,
  cursor: 'pointer',
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    width: '100%',
    height: 'fit-content',
    color: theme.palette.common.white,
    paddingLeft: theme.spacing(3),
    paddingRight: theme.spacing(3),
  },
}));
