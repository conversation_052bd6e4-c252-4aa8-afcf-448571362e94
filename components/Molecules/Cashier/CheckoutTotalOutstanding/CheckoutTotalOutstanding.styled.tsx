import styled from '@emotion/styled';
import { Accordion } from '@mui/material';
import Stack from '@mui/material/Stack';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  alignItems: 'flex-end',
  flexDirection: 'row',
  flexWrap: 'wrap',
  background: 'white',
  padding: theme.spacing(2),
  borderRadius: '10px',
  maxHeight: '40dvh',
  overflow: 'auto',
  gap: theme.spacing(3),
}));

export const StyledStatus = styled(Stack)<{ isPaid: boolean }>(({ theme, isPaid }) => ({
  justifyContent: 'center',
  background: isPaid ? theme.palette.success.main : theme.palette.neutrals.N160.main,
  color: 'white',
  width: '104px',
  alignItems: 'center',
  height: '22px',
  borderRadius: '5px',
}));

export const StyledAccordion = styled(Accordion)(({ theme }) => ({
  padding: theme.spacing(0),
  minHeight: '17.5px !important',
  boxShadow: 'none',
  '.MuiButtonBase-root': {
    padding: 0,
    minHeight: '17.5px !important',
    alignItems: 'center',
  },
  '.MuiAccordionSummary-content': {
    padding: 0,
    margin: '0 !important',
    marginRight: `${theme.spacing(20 / 8)} !important`,
  },
  svg: {
    color: 'white',
  },
  '.MuiAccordionDetails-root': {
    padding: 0,
    paddingRight: '24px',
  },
  // img: {
  //   width: '35px',
  //   height: '35px',
  // },
}));
