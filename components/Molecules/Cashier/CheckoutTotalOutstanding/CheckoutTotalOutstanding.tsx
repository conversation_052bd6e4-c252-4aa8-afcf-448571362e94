import { AccordionDetails, AccordionSummary, Divider, Stack } from '@mui/material';
import { useMemo } from 'react';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import { TCouponApply } from '@/components/Organisms/Cashier/Cashier.hook';
import { TInvoice } from '@/lib/types/entities/invoice';
import { TOrder, TOrderItem } from '@/lib/types/entities/order';
import { Price } from '../../Price/Price';
import { StyledAccordion, StyledContainer, StyledStatus } from './CheckoutTotalOutstanding.styled';
import { TAX } from '@/lib/constants/tax';
import { customPalettes } from '@/theme/customPalettes';

type TTotalInvoice = {
  subTotal: number;
  paid?: number;
  total: number;
  tax?: number;
  coupon?: TCouponApply;
  id: string;
  type: 'order' | 'invoice';
};

export const TotalInvoice: React.FC<TTotalInvoice> = ({
  subTotal = 0,
  paid = 0,
  total = 0,
  tax = TAX,
  coupon,
  id,
  type,
}) => {
  const renderTax = useMemo(() => {
    if (type === 'invoice') return <></>;
    return (
      <Stack width="100%" justifyContent="space-between" alignItems="center" flexDirection="row">
        <Typography color="#8E7A6D" variant="heading-medium-700">
          {`Tax ${tax}%`}
        </Typography>
        <Price amount={Number((subTotal / 100) * tax)} typographyProps={{ variant: 'heading-medium-700' }} />
      </Stack>
    );
  }, [type, tax]);

  return (
    <Stack width="275px" gap={1.5} pr={5.625}>
      <Stack width="100%" justifyContent="space-between" alignItems="center" flexDirection="row">
        <Typography color="#8E7A6D" variant="heading-medium-700">
          Subtotal
        </Typography>
        <Price amount={subTotal} typographyProps={{ variant: 'heading-medium-700' }} />
      </Stack>
      {paid > 0 && (
        <Stack width="100%" justifyContent="space-between" alignItems="center" flexDirection="row">
          <Typography color="#8E7A6D" variant="heading-medium-700">
            Paid
          </Typography>
          <Price amount={paid * -1} typographyProps={{ variant: 'heading-medium-700' }} />
        </Stack>
      )}
      {(coupon?.discountMoney || 0) > 0 && (
        <Stack width="100%" justifyContent="space-between" alignItems="center" flexDirection="row">
          <Typography color="#8E7A6D" variant="heading-medium-700">
            Discount
          </Typography>
          <Price amount={Number(coupon?.discountMoney || 0) * -1} typographyProps={{ variant: 'heading-medium-700' }} />
        </Stack>
      )}
      {renderTax}
      <Divider />
      <Stack width="100%" justifyContent="space-between" alignItems="center" flexDirection="row">
        <Typography variant="heading-medium-700">Total</Typography>
        <Price amount={total} typographyProps={{ variant: 'heading-xlarge-700' }} />
      </Stack>
    </Stack>
  );
};

type TCheckoutItemsAccordion = { items?: TOrderItem[]; invoice?: TInvoice };

export const CheckoutItemsAccordion: React.FC<TCheckoutItemsAccordion> = ({ items, invoice }) => {
  if (!items) return <></>;
  const titleParent = `IN${invoice?.code}`;

  return (
    <Stack width="360px" flex={1}>
      <StyledAccordion sx={{ boxShadow: 'none' }}>
        <AccordionSummary
          expandIcon={<Icon variant="accordion" size={21 / 8} />}
          aria-controls="panel1a-content"
          id="panel1a-header"
        >
          <Stack flexDirection="row" justifyContent="space-between" width="100%" gap={3}>
            <Typography variant="heading-small-700" sx={{ width: '50%' }}>
              {titleParent}
            </Typography>
            <Price
              amount={invoice?.total}
              typographyProps={{
                variant: 'heading-medium-700',
                sx: {
                  color: customPalettes?.neutrals?.N180?.main,
                  width: 'fit-content',
                  minWidth: '20%',
                  textDecoration: 'line-through',
                },
              }}
            />
            <Price amount={invoice?.unPaid} typographyProps={{ variant: 'heading-medium-700' }} />
          </Stack>
        </AccordionSummary>
        <AccordionDetails>
          {items?.map(i => {
            const titleChild = `${i?.quantity || ''} ${i?.product?.name || ''}`;
            return (
              <Stack
                key={i?.id}
                flexDirection="row"
                justifyContent="space-between"
                width="100%"
                my={1}
                paddingLeft="28px"
              >
                <Typography
                  key={i?.product?.id}
                  variant="heading-small-700"
                  color={customPalettes?.neutrals?.N180?.main}
                >
                  {titleChild}
                </Typography>
              </Stack>
            );
          })}
        </AccordionDetails>
      </StyledAccordion>
    </Stack>
  );
};

type TItemsAccordion = { items?: TOrderItem[] };

export const ItemsAccordion: React.FC<TItemsAccordion> = ({ items }) => {
  if (!items) return <></>;
  if (items?.length === 1)
    return (
      <Stack flexDirection="row" justifyContent="space-between" width="100%" pr="45px">
        <Typography variant="heading-small-700">{items?.at(0)?.product?.name}</Typography>

        <Price amount={items?.at(0)?.product?.price} typographyProps={{ variant: 'heading-medium-700' }} />
      </Stack>
    );
  const [firstItems, ...restItems] = items;
  const titleParent = `${firstItems?.quantity || ''} ${firstItems?.product?.name}`;

  return (
    <Stack width="360px" flex={1}>
      <StyledAccordion sx={{ boxShadow: 'none' }}>
        <AccordionSummary
          expandIcon={<Icon variant="accordion" size={21 / 8} />}
          aria-controls="panel1a-content"
          id="panel1a-header"
        >
          <Stack flexDirection="row" justifyContent="space-between" width="100%" height="17.5px">
            <Typography variant="heading-small-700">{titleParent}</Typography>

            <Price amount={firstItems?.product?.price} typographyProps={{ variant: 'heading-medium-700' }} />
          </Stack>
        </AccordionSummary>
        <AccordionDetails>
          {restItems?.map(i => {
            const titleChild = `${i?.quantity || ''} ${i?.product?.name || ''}`;
            return (
              <Stack key={i?.id} flexDirection="row" justifyContent="space-between" width="100%" my={1}>
                <Typography key={i?.product?.id} variant="heading-small-700">
                  {titleChild}
                </Typography>
                <Price amount={i?.product?.price} typographyProps={{ variant: 'heading-medium-700' }} />
              </Stack>
            );
          })}
        </AccordionDetails>
      </StyledAccordion>
    </Stack>
  );
};

type TCheckoutTotalOutstanding = {
  invoices?: TInvoice[];
  orders?: TOrder[];
};

export const CheckoutTotalOutstanding: React.FC<TCheckoutTotalOutstanding> = ({ invoices, orders = [] }) => (
  <StyledContainer>
    {invoices?.map(invoice => (
      <Stack key={invoice?.id} width="100%" flexDirection="column" justifyContent="flex-end" alignItems="flex-end">
        <Stack width="100%" flexDirection="row" justifyContent="space-between" gap={3}>
          <Stack gap={0.75}>
            <Typography variant="heading-medium-700" width="105px">
              Invoice: #{invoice?.code}
            </Typography>
            <StyledStatus isPaid={false}>Part Paid</StyledStatus>
          </Stack>
          <ItemsAccordion items={invoice?.orders?.flatMap<TOrderItem>(o => o?.items || [])} />
        </Stack>
        <TotalInvoice
          subTotal={invoice?.total ?? 0}
          total={invoice?.unPaid ?? 0}
          paid={invoice?.paid}
          type="invoice"
          id={invoice.id}
        />
        <Divider sx={{ width: '80%', paddingY: '24px' }} />
      </Stack>
    ))}
    {orders?.map(order => (
      <Stack key={order?.id} width="100%" flexDirection="column" justifyContent="flex-end" alignItems="flex-end">
        <Stack width="100%" flexDirection="row" justifyContent="space-between" gap={3}>
          <Stack>
            <Typography variant="heading-medium-700" width="105px">
              Order: #{order?.code || ''}
            </Typography>
          </Stack>
          <ItemsAccordion items={order?.items} />
        </Stack>

        <TotalInvoice
          type="order"
          id={order.id || ''}
          subTotal={order?.subTotal ?? 0}
          total={order?.subTotal ?? 0}
          tax={order?.tax}
          coupon={{
            couponName: order?.couponName || '',
            discountProductId: order?.discountProductId || '',
            discountMoney: order?.discountMoney || 0,
            couponCode: order?.discountProductId || '',
          }}
        />
        <Divider sx={{ width: '80%', paddingY: '24px' }} />
      </Stack>
    ))}
  </StyledContainer>
);
