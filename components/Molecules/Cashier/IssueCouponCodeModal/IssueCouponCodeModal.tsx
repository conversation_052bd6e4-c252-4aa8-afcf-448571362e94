import { dialogClasses } from '@mui/material/Dialog';
import Stack from '@mui/material/Stack';
import { Theme } from '@mui/material/styles';
import Modal from '@/components/Atoms/Modal';
import Search from '@/components/Atoms/Search';
import CustomTable, { TColumnTable } from '@/components/Atoms/Table';
import Typography from '@/components/Atoms/Typography';
import { TCouponCode } from '@/lib/types/entities/issue-coupon';
import IssueCouponSendEmail from '../../IssueCouponSendEmail';
import { StyledStatus } from './IssueCouponCodeModal.styled';
import { customPalettes } from '@/theme/customPalettes';

type TIssueCouponCodeModal = {
  coupons?: TCouponCode[];
  isOpen: boolean;
  title: string;
  page: number;
  onSearch: (keyword: string) => void;
  handleClose: () => void;
  onLoadMore: () => void;
  onRefetchCouponCodes: () => void;
};

export const IssueCouponCodeModal: React.FC<TIssueCouponCodeModal> = ({
  title,
  onSearch,
  coupons = [],
  isOpen,
  handleClose,
  page,
  onLoadMore,
  onRefetchCouponCodes,
}) => {
  const COLUMNS: TColumnTable[] = [
    { name: 'code', label: 'CODE' },
    {
      name: 'isUsed',
      label: 'STATUS',
      render: (row: TCouponCode) =>
        row?.isUsed ? (
          <StyledStatus bgcolor={customPalettes?.neutrals?.N50?.main}>
            <Typography textAlign="center" color="white" variant="heading-medium-700">
              Used
            </Typography>
          </StyledStatus>
        ) : (
          <>
            <StyledStatus bgcolor={customPalettes?.success?.main}>
              <Typography textAlign="center" color="white" variant="heading-medium-700">
                Available
              </Typography>
            </StyledStatus>
          </>
        ),
    },
    {
      name: 'email',
      label: 'EMAIL',
      render: (row: TCouponCode) => {
        const isUsed = row?.isUsed || false;
        if (isUsed || !row?.email) return <Typography>{row?.email || ''}</Typography>;
        return (
          <IssueCouponSendEmail
            code={row?.code}
            sentEmail={row?.email || ''}
            disable={isUsed}
            onRefetchCouponCodes={onRefetchCouponCodes}
          />
        );
      },
    },
  ];

  return (
    <Modal
      isOpen={isOpen}
      handleClose={handleClose}
      title={title}
      dialogProps={{
        sx: (theme: Theme) => ({
          [`& .${dialogClasses.paper}`]: {
            maxWidth: theme.spacing(126.5),
            height: 'fit-content',
            padding: theme.spacing(3),
            maxHeight: 'calc(100% - 48px)',
            backgroundColor: theme.palette.neutrals.N100.main,
            borderRadius: theme.spacing(1.25),
            [theme.breakpoints.down('md')]: {
              maxWidth: 'auto',
              padding: theme.spacing(2),
            },
          },
        }),
      }}
    >
      <Stack gap={1.5} width="730px">
        <Stack>
          <Search placeholder="Search" onSearch={onSearch} />
        </Stack>
        <CustomTable
          columns={COLUMNS.map(column => ({
            tableCellProps: {
              sx: (theme: Theme) => ({
                '&.MuiTableCell-root': {
                  borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
                  padding: theme.spacing(2, 1),
                },
                '&.MuiTableCell-body': {
                  verticalAlign: 'middle',
                  ...theme.typography['body-xlarge-400'],
                },
                '& > h5': {
                  fontSize: theme.typography['heading-xsmall-700'],
                  textTransform: 'uppercase',
                },
              }),
            },
            ...column,
          }))}
          rows={coupons}
          stickyHeader
          sttProps={{
            sx: (theme: Theme) => ({
              '&.MuiTableCell-root': {
                verticalAlign: 'middle',
                borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
                minWidth: theme.spacing(6),
              },
            }),
          }}
          tableContainerProps={{
            sx: theme => ({ padding: theme.spacing(0, 0, 3, 0), height: '450px', overflow: 'auto' }),
          }}
          tableCellProps={{ sx: { textAlign: 'left' } }}
          page={page}
          onLoadMore={onLoadMore}
          infinitiLoad
        />
      </Stack>
    </Modal>
  );
};
