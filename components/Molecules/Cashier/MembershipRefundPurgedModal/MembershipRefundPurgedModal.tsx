import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import Divider from '@mui/material/Divider';
import { Fragment } from 'react';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import Modal from '@/components/Atoms/Modal';
import Table, { TColumnTable } from '@/components/Atoms/Table';
import Typography from '@/components/Atoms/Typography';
import { customPalettes } from '@/theme/customPalettes';
import { useMembershipRefundPurgedByCustomerId } from '@/lib/hooks/queries/membership';
import { TMembershipRefundPurged } from '@/lib/types/entities/membership';
import CustomTableRow from '@/components/Atoms/CustomTableRow';
import { StyledBodyTableCell } from '@/components/Atoms/Table/Table.styled';
import { customTypography } from '@/theme/customTypography';

dayjs.extend(utc);
dayjs.extend(timezone);

type TMembershipRefundPurgedModalProps = {
  isOpen: boolean;
  customerId: string;
  handleClose: () => void;
};

export const MembershipRefundPurgedModal: React.FC<TMembershipRefundPurgedModalProps> = ({
  isOpen,
  customerId,
  handleClose,
}) => {
  const { data: membershipRefundPurgedData } = useMembershipRefundPurgedByCustomerId(customerId);
  const CUSTOMER_SUMMARY_DETAIL_COLUMNS: TColumnTable[] = [
    {
      name: 'purgedDate',
      tableCellProps: {
        sx: theme => ({
          borderBottom: `1px solid ${theme.palette.neutrals.N380.main} !important`,
        }),
      },
      label: (
        <Typography variant="body-xlarge-400" color={customPalettes?.neutrals?.N180?.main} textTransform="capitalize">
          Purget Date
        </Typography>
      ),
      render: (row: TMembershipRefundPurged) => (
        <Typography variant="body-xlarge-400">{`${dayjs.utc(row.purgedDate).format('HH:mm A')}`}</Typography>
      ),
    },
    {
      name: 'purgedDate',
      label: '',
      tableCellProps: {
        sx: theme => ({
          borderBottom: `1px solid ${theme.palette.neutrals.N380.main} !important`,
        }),
      },
      render: (row: TMembershipRefundPurged) => (
        <Typography variant="body-xlarge-400">{`${dayjs.utc(row.purgedDate).format('DD/MM/YYYY')}`}</Typography>
      ),
    },
    {
      name: 'purgedCredit',
      tableCellProps: {
        sx: theme => ({
          borderBottom: `1px solid ${theme.palette.neutrals.N380.main} !important`,
        }),
      },
      label: (
        <Typography variant="body-xlarge-400" color={customPalettes?.neutrals?.N180?.main} textTransform="capitalize">
          Purged Credits
        </Typography>
      ),
      render: (row: TMembershipRefundPurged) => <Typography variant="body-xlarge-400">{row?.purgedCredit}</Typography>,
    },
  ];
  return (
    <Modal
      isOpen={isOpen}
      handleClose={handleClose}
      title={
        <Stack flexDirection="row" justifyContent="flex-start" alignItems="center" component="span" gap={1.5}>
          <Typography component="span" variant="heading-xlarge-700" color={customPalettes?.neutrals?.N50.main}>
            Purged Credits Details
          </Typography>
        </Stack>
      }
    >
      <Stack
        sx={theme => ({
          width: theme.spacing(600 / 8),
          gap: theme.spacing(3),
          justifyContent: 'flex-start',
          [theme.breakpoints.down('md')]: {
            width: 'auto',
          },
        })}
      >
        <Divider
          sx={theme => ({
            borderBottomWidth: theme.spacing(1 / 8),
            opacity: 0.25,
            borderColor: theme.palette.neutrals.N180.main,
          })}
        />
        <Table
          columns={CUSTOMER_SUMMARY_DETAIL_COLUMNS}
          tableCellProps={{
            sx: theme => ({
              '&.MuiTableCell-root': {
                borderBottom: `1px solid ${theme.palette.neutrals.N380.main} !important`,
                padding: theme.spacing(2, 1),
              },
              '&.MuiTableCell-body': {
                ...theme.typography['body-xlarge-400'],
              },
              '& > h5': {
                fontSize: theme.typography['heading-xsmall-700'],
                textTransform: 'uppercase',
              },
            }),
          }}
          tableContainerProps={{ sx: theme => ({ padding: theme.spacing(0.25) }) }}
          // sttProps={{
          //   sx: theme => ({
          //     '&.MuiTableCell-root': {
          //       borderBottom: `1px solid ${theme.palette.neutrals.N180.main} !important`,
          //       padding: theme.spacing(2, 1),
          //     },
          //   }),
          // }}
          rows={membershipRefundPurgedData?.data || []}
          showSTT={false}
          tableFooter={
            <Fragment key="table-rows-container-total">
              <CustomTableRow key="table-rows-total">
                <StyledBodyTableCell
                  key="text-total"
                  component="th"
                  scope="row"
                  colSpan={2}
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'left',
                      padding: '8px',
                    },
                    '& > h5': {
                      fontSize: customTypography['heading-medium-700'],
                      textTransform: 'uppercase',
                    },
                  }}
                >
                  <Typography variant="heading-xsmall-700" textTransform="uppercase">
                    TOTAL:
                  </Typography>
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'left',
                      padding: '8px',
                    },
                  }}
                >
                  <Typography variant="heading-medium-700" textTransform="uppercase">
                    {membershipRefundPurgedData?.total}
                  </Typography>
                </StyledBodyTableCell>
              </CustomTableRow>
            </Fragment>
          }
        />
      </Stack>
    </Modal>
  );
};
