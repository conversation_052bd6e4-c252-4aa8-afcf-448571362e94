import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Button from '@/components/Atoms/Button';

export const StyledFormContainer = styled(Stack)(({ theme }) => ({
  gap: theme.spacing(2.25),
  overflow: 'auto',
  flexDirection: 'row',
  justifyContent: 'space-between',
  flexWrap: 'wrap',
  '&::-webkit-scrollbar': {
    background: '#DCE5F2',
    borderRadius: '5px',
    width: '5px',
  },
  '&:: -webkit-scrollbar-thumb': {
    background: '#99A3B4',
    borderRadius: '5px',
  },
}));

export const StyledButtonWrapper = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  spacing: theme.spacing(2),
  justifyContent: 'center',
  padding: theme.spacing(4),
  paddingBottom: 0,
  gap: theme.spacing(1.5),
  height: theme.spacing(12),
  alignItems: 'center',
  background: theme.palette.neutrals.N70.main,
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  maxWidth: theme.spacing(23.75),
}));
