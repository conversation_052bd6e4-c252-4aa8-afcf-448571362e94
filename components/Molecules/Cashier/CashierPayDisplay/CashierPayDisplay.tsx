import Typography from '@/components/Atoms/Typography';
import Price from '../../Price';
import { StyledContainer } from './CashierPayDisplay.styled';
import Skeleton from '@/components/Atoms/Skeleton';

type TCashierPayDisplay = { value: number | string; isLoading: boolean };

export const CashierPayDisplay: React.FC<TCashierPayDisplay> = ({ value, isLoading }) => (
  <StyledContainer>
    <Typography variant="heading-large-700">Pay</Typography>
    {isLoading ? (
      <Skeleton />
    ) : (
      <Price typographyProps={{ variant: 'heading-xxlarge-700' }} amount={Math.max(Number(value), 0)} />
    )}
  </StyledContainer>
);
