import styled from '@emotion/styled';
import { buttonClasses } from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Button from '@/components/Atoms/Button';

export const StyledHeaderContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  justifyContent: 'space-between',
  width: '100%',
  alignItems: 'center',
}));

export const StyledStatus = styled(Stack)<{ isPaid: boolean }>(({ theme, isPaid }) => ({
  justifyContent: 'center',
  background: isPaid ? theme.palette.success.main : theme.palette.neutrals.N160.main,
  color: 'white',
  width: '104px',
  alignItems: 'center',
  height: '22px',
  borderRadius: '5px',
}));

export const StyledPaymentButton = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    width: theme.spacing(125 / 8),
    height: 'fit-content',
    paddingTop: theme.spacing(1),
    paddingBottom: theme.spacing(1),
    color: theme.palette.common.white,
    backgroundColor: theme.palette.neutrals.N220.main,
    border: `1px solid ${theme.palette.neutrals.N220.main}`,
  },
}));

export const StyledImageWrapper = styled(Stack)(({ theme }) => ({
  minWidth: '120px',
  height: '120px',
  position: 'relative',
}));
