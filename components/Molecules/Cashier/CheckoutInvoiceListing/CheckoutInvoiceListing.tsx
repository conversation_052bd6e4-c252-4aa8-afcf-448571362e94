import { Divider, ListItemText, Stack } from '@mui/material';
import List from '@mui/material/List';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { isEmpty } from 'lodash';
import { TOrder } from '@/lib/types/entities/order';
import { TInvoice } from '@/lib/types/entities/invoice';
import Typography from '@/components/Atoms/Typography';
import { Price } from '../../Price/Price';
import { StyledHeaderContainer, StyledImageWrapper, StyledPaymentButton } from './CheckoutInvoiceListing.styled';
import { customPalettes } from '@/theme/customPalettes';
import { EOrderType } from '@/lib/types/enum/order';
import { ORDER_TYPE_LABELS } from '@/lib/constants/order';
import { CustomImage } from '@/components/Atoms/CustomImage/CustomImage';

type TCheckoutInvoiceListing = {
  invoices?: TInvoice[];
  orders?: TOrder[];
  onPayment?: (values: { orderIds: string[]; invoiceIds?: string[] }) => void;
  onCheckout: () => void;
};

type TInvoiceOrderGroup = Record<EOrderType, { orders: TOrder[]; invoices: TInvoice[]; isVisible: boolean }>;

export const CheckoutInvoiceListing: React.FC<TCheckoutInvoiceListing> = ({
  invoices = [],
  orders = [],
  onPayment,
  onCheckout,
}) => {
  const totalInvoiceAndOrder: number = useMemo(() => {
    const totalOrderInvoice = invoices
      ?.reduce((pre, cur) => [...pre, ...(cur?.orders || [])], [] as TOrder[])
      ?.reduce((pre, cur) => pre + (cur?.subTotal ?? 0), 0);
    const totalOrders = orders?.reduce((pre, cur) => pre + (cur?.subTotal ?? 0), 0);
    return (totalOrders ?? 0) + (totalOrderInvoice ?? 0);
  }, [invoices, orders]);
  const dateString = dayjs().format('ddd, DD MMM YYYY');
  const groups: TInvoiceOrderGroup = useMemo(() => {
    const temp: TInvoiceOrderGroup = {
      [EOrderType['MEMBERSHIP']]: { orders: [], invoices: [], isVisible: false },
      [EOrderType['OTHERS']]: { orders: [], invoices: [], isVisible: false },
      [EOrderType['FOOD_BEVERAGE']]: { orders: [], invoices: [], isVisible: false },
      [EOrderType['TRANSFER']]: { orders: [], invoices: [], isVisible: false },
    };

    orders.forEach(order => {
      if (order.orderType) {
        temp[order.orderType]['orders'].push(order);
        temp[order.orderType]['isVisible'] = true;
      }
    });

    // Process invoice orders
    if (invoices.length > 0) {
      temp[EOrderType['OTHERS']]['invoices'] = [...invoices];
      temp[EOrderType['OTHERS']]['isVisible'] = false;

      invoices.forEach(invoice => {
        (invoice?.orders || []).forEach(order => {
          if (order.orderType) {
            temp[order.orderType]['orders'].push(order);
            temp[order.orderType]['isVisible'] = true;
          }
        });
      });
    }

    return temp;
  }, [invoices, orders]);

  const isEmptyOrder = orders?.length === 0 && invoices?.length === 0;

  const hasBothTransferAndFoodBeverage = useMemo(
    () => groups[EOrderType.TRANSFER]?.isVisible && groups[EOrderType.FOOD_BEVERAGE]?.isVisible,
    [groups]
  );

  const getCombinedFBOrderIds = useMemo(() => {
    const transferOrderIds = groups[EOrderType.TRANSFER]?.orders?.map(o => o?.id || '') || [];
    const fbOrderIds = groups[EOrderType.FOOD_BEVERAGE]?.orders?.map(o => o?.id || '') || [];
    return [...transferOrderIds, ...fbOrderIds];
  }, [groups]);

  return (
    <List
      sx={{
        width: '100%',
        padding: 3,
        bgcolor: 'background.paper',
        borderRadius: '10px',
        maxHeight: '40dvh',
        overflow: 'auto',
      }}
    >
      {isEmptyOrder ? (
        <Stack width="100%" justifyContent="space-between" alignItems="center" minHeight="200px">
          <StyledImageWrapper>
            <CustomImage
              // containerProps={{ minHeight: '120px', maxWidth: '120px' }}
              src="/assets/images/emptyOrders.png"
              alt="empty orders"
            />
          </StyledImageWrapper>
          <Typography variant="body-xlarge-400" color="primary">
            There are no items requiring payment
          </Typography>
          {/* <StyledPaymentButton label="Checkout" variant="contained" onClick={() => onCheckout()} /> */}
        </Stack>
      ) : (
        <>
          <ListItemText>
            <StyledHeaderContainer>
              <Typography sx={{ flex: '50%' }} variant="heading-large-700">
                {dateString}
              </Typography>
              <Typography sx={{ flex: '15%' }} color={customPalettes?.neutrals?.N220?.main} variant="body-xlarge-400">
                Total Unpaid
              </Typography>
              <Price
                amount={Number(totalInvoiceAndOrder)}
                typographyProps={{
                  variant: 'heading-xlarge-700',
                  color: customPalettes?.neutrals?.N220?.main,
                  textAlign: 'right',
                }}
                containerProps={{ sx: { flex: '20%' } }}
              />
            </StyledHeaderContainer>
          </ListItemText>
          <Divider component="li" sx={{ mb: 3 }} />
          <Stack gap={3}>
            {Object.keys(groups)?.map((key: string) =>
              groups?.[key as EOrderType]?.isVisible ? (
                <Stack key={key} gap={3}>
                  <Stack flexDirection="row" justifyContent="space-between" alignItems="center">
                    <Typography variant="heading-medium-700" color={customPalettes?.neutrals?.N220?.main}>
                      {key === EOrderType.TRANSFER
                        ? `Transfer #${groups?.[key as EOrderType]?.['orders']?.[0]?.code}`
                        : ORDER_TYPE_LABELS[key as EOrderType]?.label}
                    </Typography>
                    {/* Show payment button only for non-F&B/Transfer groups, or for F&B when Transfer is not present, or for Transfer when F&B is not present */}
                    {(key !== EOrderType.TRANSFER && key !== EOrderType.FOOD_BEVERAGE) ||
                    (key === EOrderType.FOOD_BEVERAGE && !groups[EOrderType.TRANSFER]?.isVisible) ||
                    (key === EOrderType.TRANSFER && !groups[EOrderType.FOOD_BEVERAGE]?.isVisible) ? (
                      <StyledPaymentButton
                        label="Payment"
                        variant="contained"
                        onClick={() =>
                          onPayment &&
                          onPayment({
                            orderIds:
                              key === EOrderType['OTHERS'] &&
                              (groups?.[key as EOrderType]?.['invoices'] || []).length > 0
                                ? []
                                : groups?.[key as EOrderType]?.['orders']?.map(o => o?.id || '') || [],
                            invoiceIds:
                              key === EOrderType['OTHERS'] &&
                              (groups?.[key as EOrderType]?.['invoices'] || []).length > 0
                                ? (groups?.[key as EOrderType]?.['invoices'] || [])?.map(o => o?.id || '') || []
                                : [],
                          })
                        }
                      />
                    ) : (
                      key === EOrderType.FOOD_BEVERAGE &&
                      hasBothTransferAndFoodBeverage && (
                        <StyledPaymentButton
                          label="Payment"
                          variant="contained"
                          onClick={() =>
                            onPayment &&
                            onPayment({
                              orderIds: getCombinedFBOrderIds,
                              invoiceIds: [],
                            })
                          }
                        />
                      )
                    )}
                  </Stack>
                  <Stack gap={1.5}>
                    {groups?.[key as EOrderType]?.['orders']?.map(order => {
                      const filteredItems = (order?.items || []).filter(
                        item => !item.couponCode || item.product?.type === 'coupon'
                      );
                      return (
                        <Stack
                          key={order?.id}
                          width="100%"
                          flexDirection="column"
                          justifyContent="flex-end"
                          alignItems="flex-end"
                          gap={1.5}
                        >
                          {filteredItems.map(item => (
                            <Stack
                              key={item?.id}
                              flexDirection="row"
                              justifyContent="space-between"
                              alignItems="center"
                              width="100%"
                            >
                              <Stack>
                                <Stack gap={0.5} flexDirection="row" alignItems="center">
                                  <Typography variant="heading-small-700" minWidth="24px">
                                    {`${item?.quantity}`.padStart(2, '0')}{' '}
                                  </Typography>
                                  <Typography variant="heading-small-700">
                                    {item?.product?.name}
                                    {item?.couponCode ? ` (${item?.couponCode})` : ``}
                                  </Typography>
                                </Stack>
                                {item?.product?.type === 'service' &&
                                  !!item?.duration?.name &&
                                  !isEmpty(item?.employees) && (
                                    <Typography variant="heading-small-700" minWidth="24px">
                                      {item?.duration?.name} with{' '}
                                      {item?.employees
                                        ?.map(employee => employee?.displayName || employee?.fullName)
                                        ?.join(', ')}
                                    </Typography>
                                  )}
                              </Stack>
                              <Price
                                amount={item?.product?.type === 'coupon' && item?.couponCode ? 0 : item?.product?.price}
                                typographyProps={{ variant: 'heading-medium-700' }}
                              />
                            </Stack>
                          ))}
                        </Stack>
                      );
                    })}
                  </Stack>
                  <Divider sx={{ width: '100%' }} />
                </Stack>
              ) : (
                <></>
              )
            )}
          </Stack>
        </>
      )}
    </List>
  );
};
