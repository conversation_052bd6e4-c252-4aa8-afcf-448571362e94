import { useContext } from 'react';
import Icon, { IconVariantTypes } from '@/components/Atoms/Icon/Icon';
import { StyleContainer, StyleItemContainer, StyleTitle } from './LeftSideBar.styled';
import { CashierMenuContext, TCASHIER_MENU } from '@/lib/context/CashierMenuContext';

// const CASHIER_OPTIONS = ['PRODUCT', 'SERVICE', 'MEMBERSHIP', 'FOODBEVERAGE', 'SEARCH'] as const;

// export type TCASHIER_OPTION = (typeof CASHIER_OPTIONS)[number];

const CASHIER_MENUS: Record<
  TCASHIER_MENU,
  {
    label: string;
    defaultIcon: IconVariantTypes;
    activeIcon: IconVariantTypes;
  }
> = {
  PRODUCT: {
    label: 'Product',
    defaultIcon: 'cashier_product_default',
    activeIcon: 'cashier_product_selected',
  },
  SERVICE: {
    label: 'Service',
    defaultIcon: 'cashier_service_default',
    activeIcon: 'cashier_service_selected',
  },
  MEMBERSHIP: {
    label: 'Membership',
    defaultIcon: 'cashier_package_default',
    activeIcon: 'cashier_package_selected',
  },
  FOOD: {
    label: 'Food',
    defaultIcon: 'cashier_fb_default',
    activeIcon: 'cashier_fb_selected',
  },
  BEVERAGE: {
    label: 'Beverage',
    defaultIcon: 'cashier_beverage_default',
    activeIcon: 'cashier_beverage_selected',
  },
  COUPON: {
    label: 'Coupon',
    defaultIcon: 'cashier_coupon_default',
    activeIcon: 'cashier_coupon_selected',
  },
  SEARCH: {
    label: 'SEARCH',
    defaultIcon: 'cashier_search',
    activeIcon: 'active_cashier_search',
  },
};

export const LeftSideBar: React.FC = () => {
  const { cashierMenus, activeLeftSidebarMenu, setActiveLeftSidebarMenu } = useContext(CashierMenuContext);
  return (
    <StyleContainer>
      {cashierMenus?.map((o: TCASHIER_MENU) => {
        const isActive = activeLeftSidebarMenu === o;
        const iconVariant = isActive ? CASHIER_MENUS?.[o]?.activeIcon : CASHIER_MENUS?.[o]?.defaultIcon;

        return (
          <StyleItemContainer isActive={isActive} key={o} onClick={() => setActiveLeftSidebarMenu(o)}>
            <Icon size={5} variant={iconVariant} />
            <StyleTitle variant="heading-medium-700" isActive={isActive}>
              {CASHIER_MENUS?.[o]?.label}
            </StyleTitle>
          </StyleItemContainer>
        );
      })}
    </StyleContainer>
  );
};
