import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import { LeftSideBar } from './LeftSideBar';

export default {
  title: 'Molecules/LeftSideBar',
  component: LeftSideBar,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof LeftSideBar>;

export const Default: Story = {
  args: { value: 'FOODBEVERAGE' },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <LeftSideBar {...args} />
    </Box>
  ),
};
