import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Typography from '@/components/Atoms/Typography';

export const StyleContainer = styled(Stack)(({ theme }) => ({
  width: theme.spacing(16),
  height: 'calc(100vh - 56px)',
  overflow: 'auto',
  backgroundColor: theme.palette.neutrals.N20.main,
  padding: theme.spacing(3, 1.5),
  // gap: theme.spacing(3),
}));

export const StyleItemContainer = styled(Stack)<{ isActive: boolean }>(({ theme, isActive }) => ({
  padding: theme.spacing(1.5, 0.5),
  gap: theme.spacing(1.5),
  alignItems: 'center',
  backgroundColor: isActive ? theme.palette.neutrals.N50.main : theme.palette.neutrals.N20.main,
  ':hover': {
    cursor: 'pointer',
  },
  borderRadius: isActive ? '10px' : '0px',
}));

export const StyleTitle = styled(Typography)<{ isActive: boolean }>(({ theme, isActive }) => ({
  color: isActive ? 'white' : theme.palette.neutrals.N50.main,
}));
