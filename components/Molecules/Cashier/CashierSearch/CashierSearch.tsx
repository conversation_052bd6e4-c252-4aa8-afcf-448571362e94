import { useContext, useState } from 'react';
import { CondOperator } from '@nestjsx/crud-request';
import { CustomImage } from '@/components/Atoms/CustomImage/CustomImage';
import Typography from '@/components/Atoms/Typography';
import { TOrderItem } from '@/lib/types/entities/order';
import Price from '../../Price';
import {
  StyledContainer,
  StyledItem,
  StyledItemContainer,
  StyledItemImageWrapper,
  StyledListingContainer,
  StyledTitle,
} from './CashierSearch.styled';
import useDebounce from '@/lib/hooks/utils/useDebounce';
import Search from '@/components/Atoms/Search';
import { useProducts } from '@/lib/hooks/queries/product';
import { CashierMenuContext } from '@/lib/context/CashierMenuContext';

export type TItemCashier = TOrderItem['product'] & {
  duration?: TOrderItem['product'];
  couponCode?: string;
};

export type TCashierCategory = {
  id: string;
  name: string;
  avatar?: { id: string; url: string };
  children: Array<TCashierCategory>;
  items?: TItemCashier[];
};

type TCashierItemListing = {
  onItemSelect: (item: TItemCashier) => void;
  isMember: boolean;
};

export const CashierSearch: React.FC<TCashierItemListing> = ({ isMember, onItemSelect }) => {
  const [keySearch, setKeySearch] = useState('');
  const { cashierMenus } = useContext(CashierMenuContext);
  const debouncedSearch = useDebounce(keySearch, 300);
  const searchMenus: string[] = cashierMenus
    ?.filter(cashierMenu => cashierMenu !== 'SEARCH')
    ?.map(menu => menu.toLowerCase());
  const { data: productData } = useProducts(debouncedSearch, {
    filterOptions: [
      {
        field: 'type',
        value: searchMenus,
        operator: CondOperator['IN'],
      },
      {
        field: 'isMember',
        value: isMember ? 'true' : 'false',
        operator: CondOperator['EQUALS'],
      },
    ],
  });

  return (
    <StyledContainer>
      <Search placeholder="Search" onSearch={(keyword: string) => setKeySearch(keyword)} />
      <StyledListingContainer>
        <StyledTitle variant="heading-large-700">RESULT</StyledTitle>
        <StyledItemContainer>
          {productData?.map(product => (
            <StyledItem key={product?.id} onClick={() => onItemSelect(product)} style={{ padding: '12px' }}>
              <StyledItemImageWrapper>
                <CustomImage
                  src={product?.avatar?.url || ''}
                  alt=""
                  style={{
                    objectFit: 'cover',
                  }}
                />
                <Price
                  amount={product?.price}
                  containerProps={{
                    sx: theme => ({
                      position: 'absolute',
                      borderRadius: theme.spacing(3.75),
                      bottom: '6px',
                      right: '6px',
                      padding: theme.spacing(0.5, 1.5),
                      background: 'white',
                      ...theme.typography['heading-xsmall-700'],
                    }),
                  }}
                  typographyProps={{ variant: 'heading-xsmall-700' }}
                />
              </StyledItemImageWrapper>
              <Typography variant="heading-medium-700" width="100%">
                {product?.name}
              </Typography>
            </StyledItem>
          ))}
        </StyledItemContainer>
      </StyledListingContainer>
    </StyledContainer>
  );
};
