import { Box } from '@mui/material';
import Stack from '@mui/material/Stack';
import isEmpty from 'lodash/isEmpty';
import { useEffect, useMemo, useRef, useState, useContext } from 'react';
import { useReactToPrint } from 'react-to-print';
import { CondOperator } from '@nestjsx/crud-request';
import { useSearchParams } from 'next/navigation';
import { CashierMenuContext } from '@/lib/context/CashierMenuContext';
import InvoiceOrderView, { TInvoiceOrder } from '@/components/Molecules/InvoiceOrderView';
import { TAX } from '@/lib/constants/tax';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { useMutationInvoiceCreate, useMutationInvoiceUpdate } from '@/lib/hooks/mutation/invoice';
import { usePaymentMethods } from '@/lib/hooks/queries/paymentMethod';
import { TPaymentState, usePaymentCaculator } from '@/lib/hooks/utils/usePaymentCaculator';
import { checkCouponCode } from '@/lib/services/coupon';
import { TCouponCheckRes } from '@/lib/types/entities/coupon';
import { TCustomer } from '@/lib/types/entities/customer';
import { TInvoice, TInvoiceNewPaid } from '@/lib/types/entities/invoice';
import { TOrder } from '@/lib/types/entities/order';
import { TPaymentMethod } from '@/lib/types/entities/paymentMethod';
import { ECouponType } from '@/lib/types/enum/coupon';
import { EInvoiceStatus } from '@/lib/types/enum/invoice';
import { ECreditMethods } from '@/lib/types/enum/paymentMethod';
import { getCredits } from '@/lib/utils/credit';
import { calculateTax, concatenateNames, roundToNearestHalf } from '@/lib/utils/string';
import SelectCouponModal from '../../SelectCouponModal';
import { CashierCustomerInfo } from '../CashierCustomerInfo/CashierCustomerInfo';
import { CashierPayDisplay } from '../CashierPayDisplay/CashierPayDisplay';
import { CashierPaymentNumberBoard } from '../CashierPaymentNumberBoard/CashierPaymentNumberBoard';
import { CashierResultCaculator } from '../CashierResultCaculator/CashierResultCaculator';
import {
  StyledContainer,
  StyledPaymentContainer,
  StyledPaymentItem,
  StyledPaymentText,
} from './CashierPaymentCaculator.styled';
import { TVoucherChecked, useCouponApply } from '@/lib/hooks/utils/useCouponApply';
import { TApiError } from '@/lib/utils/transformResponse';
import { parseDialCodeAndPhoneNumber } from '@/lib/utils/phoneNumber';
import { ShopifyCodeModal } from '../ShopifyCodeModal/ShopifyCodeModal';
import { ModalRemark } from '../../ModalRemark/ModalRemark';
import { useCustomerById } from '@/lib/hooks/queries/customer';
import { useSetting } from '@/lib/hooks/queries/setting';

type TCashierPaymentCaculator = {
  rfid?: string;
  appointmentId: string;
  customerInfo?: TCustomer;
  invoices?: TInvoice[];
  orders?: TOrder[];
  onComplete: () => void;
  type?: 'create' | 'update';
  defaultCouponApply?: TVoucherChecked;
  defaultPaymentMethods?: TPaymentState;
};

export const CashierPaymentCaculator: React.FC<TCashierPaymentCaculator> = ({
  customerInfo,
  invoices,
  orders,
  onComplete,
  rfid,
  appointmentId,
  type = 'create',
  defaultCouponApply,
  defaultPaymentMethods,
}) => {
  const [valueInput, setValueInput] = useState<string>('0');
  const [isPrintReceipt, setIsPrintReceipt] = useState(true);
  const [openShopifyCodeModal, setOpenShopfiCodeModal] = useState<{
    isOpen: boolean;
    shopifyCode: string;
    paid: number;
    paymentMethod?: TPaymentMethod;
  }>({
    isOpen: false,
    paid: 0,
    paymentMethod: undefined,
    shopifyCode: '',
  });
  const [openRemarkModal, setOpenRemarkModal] = useState<{
    isOpen: boolean;
    note: string;
  }>({
    isOpen: false,
    note: '',
  });
  const [couponError, setCouponError] = useState<string>();
  const [invoiceDetail, setInvoiceDetail] = useState<TInvoice>();
  const [invoiceNewPaid, setInvoiceNewPaid] = useState<TInvoiceNewPaid>({
    invoiceCoupon: [],
    totalBeforeTax: 0,
    totalPaid: 0,
    subTotal: 0,
    isEdited: false,
  });
  const { remarkTotalNote, setRemarkTotalNote } = useContext(CashierMenuContext);
  const { paymentMethods, onAddPaymentMethod, onRemovePaymentMethod, totalPayment, onClearPaymentMethod } =
    usePaymentCaculator(defaultPaymentMethods);
  const { onCreateInvoice, isMutating: isCreating } = useMutationInvoiceCreate();
  const { onUpdateInvoice, isMutating: isUpdating } = useMutationInvoiceUpdate();
  const { data: currentCustomer, mutate: refetchCurrentCustomer } = useCustomerById(customerInfo?.id || '');
  const { showError, showSuccess } = useAlert();
  const { data: OPTION_BY_KEY } = useSetting(['status']);
  const activeStatus = OPTION_BY_KEY?.['status']?.find(option => option?.name === 'Active');
  const searchParams = useSearchParams();

  const orderId = searchParams.get('orderId');
  const orderDetail = orders?.find(order => order?.id === orderId);
  const { data: TOTAL_PAYMENT_METHOD } = usePaymentMethods({
    query: {
      filterOptions: activeStatus?.id
        ? [
            {
              field: 'status.id',
              value: activeStatus?.id || '',
              operator: CondOperator['EQUALS'],
            },
          ]
        : [],
    },
  });

  const componentToPrintRef = useRef<HTMLDivElement>();

  const handlePrint = useReactToPrint({
    content: () => componentToPrintRef?.current || null,
  });

  const { voucherChecked, onRemoveVoucher, setVoucherChecked, newTotalDiscountValue, totalDiscountValue } =
    useCouponApply(defaultCouponApply);

  const [isOpenModalDiscount, setOpenModalDiscount] = useState<boolean>(false);
  const isCreateMode = (type === 'create' && isEmpty(invoices)) || (type === 'update' && invoices?.length === 0);
  const onApplyCoupon = async (couponCodes: { id?: string; couponCode: string; couponType: ECouponType }[]) => {
    setCouponError(undefined);
    const orderIds = orders?.map(i => ({ id: i.id || '' })) || [];
    const invoiceOrderIds = invoices?.[0]?.orders?.map(i => ({ id: i.id || '' })) || [];
    if (isEmpty(orderIds) && isEmpty(invoiceOrderIds)) return showError({ title: 'Empty Orders' });

    // const isCouponTypeExist = voucherChecked.voucherChoose.some(v => v.couponType === couponType);
    // const newValuesCouponChoose = isCouponTypeExist
    //   ? voucherChecked.voucherChoose.map(v => {
    //       if (v.couponType === couponType)
    //         return {
    //           ...v,
    //           couponCode,
    //         };
    //       return v;
    //     })
    //   : [...voucherChecked.voucherChoose, { couponCode, couponType }];

    const newValuesCouponChoose = [...voucherChecked.voucherChoose, ...couponCodes];

    const couponCheckList = newValuesCouponChoose?.map(voucher => ({
      id: voucher?.id || '',
      type: voucher.couponType,
      value: Number(voucher.couponCode),
      code: voucher.couponCode,
    }));

    const response = await checkCouponCode<TCouponCheckRes[]>({
      orders: !isEmpty(orderIds) ? orderIds : invoiceOrderIds,
      coupons: couponCheckList,
    });
    if (response.status === 'error') {
      if (typeof response?.message?.error === 'string') {
        return setCouponError((response?.message?.error || 'Coupon code is invalid') as string);
      }
      if (Array.isArray(response.message.error)) {
        return setCouponError('Coupon code is invalid');
      }
      const apiError = response.message.error as TApiError;
      return setCouponError((apiError?.message || 'Coupon code is invalid') as string);
    }
    setVoucherChecked({
      voucherChoose: newValuesCouponChoose,
      voucherResponse: response?.data?.map(dataItem => ({
        id: dataItem?.id || '',
        ...dataItem,
      })),
    });
    const totalDiscountTemp = response?.data?.reduce((pre, cur) => pre + Number(cur.discountMoney), 0) || 0;
    if ((paymentMethods?.length || 0) > 0) {
      onClearPaymentMethod();
      setInvoiceNewPaid(prev => ({
        totalBeforeTax: (Number(prev?.subTotal) || 0) - totalDiscountTemp,
        subTotal: prev?.subTotal || 0,
        totalPaid: prev?.totalPaid || 0,
        isEdited: prev?.isEdited || false,
        invoiceCoupon: [...(prev?.invoiceCoupon || [])],
      }));
    } else {
      const totalCreditAmount =
        paymentMethods
          ?.filter(
            item =>
              item?.paymentMethod?.code === ECreditMethods.NEW_CREDIT ||
              item?.paymentMethod?.code === ECreditMethods.OLD_CREDIT
          )
          ?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0) || 0;
      setInvoiceNewPaid(prev => ({
        totalBeforeTax: (Number(prev?.subTotal) || 0) - totalCreditAmount - totalDiscountTemp,
        subTotal: prev?.subTotal || 0,
        totalPaid: prev?.totalPaid || 0,
        isEdited: prev?.isEdited || false,
        invoiceCoupon: [...(prev?.invoiceCoupon || [])],
      }));
    }

    setOpenModalDiscount(false);
  };

  const onKeyboardPress = ({ value, type }: { value: string | number; type: 'default' | 'clear' | 'decimal' }) => {
    switch (type) {
      case 'clear': {
        setValueInput('0');
        break;
      }
      case 'decimal': {
        if (!valueInput.includes('.')) {
          setValueInput(pre => `${pre}.`);
        }
        break;
      }
      default: {
        setValueInput(pre => `${pre}${value}`);
      }
    }
  };

  const totaInvoiceUnpaid: number = useMemo(
    () => invoices?.reduce((pre, cur) => pre + (cur?.unPaid || 0), 0) || 0,
    [invoices]
  );
  // const totalUnTaxed =
  //   paymentMethods?.reduce(
  //     (pre, cur) =>
  //       pre +
  //       (cur?.paymentMethod?.code === ECreditMethods.NEW_CREDIT ||
  //       cur?.paymentMethod?.code === ECreditMethods?.OLD_CREDIT
  //         ? cur?.paid ?? 0
  //         : 0),
  //     0
  //   ) || 0;
  const taxCaculated = useMemo(() => {
    const totalTax = calculateTax(invoiceNewPaid?.totalBeforeTax || 0, Number(TAX || 0));
    return parseFloat(totalTax.toFixed(2)) >= 0 ? parseFloat(totalTax.toFixed(2)) : 0;
  }, [invoiceNewPaid?.totalBeforeTax]);
  // const taxCaculated = useMemo(() => {
  //   if ((type === 'create' && isEmpty(invoices)) || (type === 'update' && invoices?.length === 0)) {
  //     return calculateTax(totalOrderWithDiscount, Number(TAX || 0));
  //   }
  //   const totalPaid = new Decimal(invoices?.[0]?.newPaid?.totalPaid || 0);
  //   const totalBeforeTax = new Decimal(invoices?.[0]?.newPaid?.totalBeforeTax || 0);
  //   const newTax = new Decimal(calculateTax(newTotalDiscountValue, Number(TAX || 0)));
  //   const totalTax = totalPaid.minus(totalBeforeTax).minus(newTax);

  //   return parseFloat(totalTax.toFixed(2));
  // }, [totalOrderWithDiscount, type, invoices, newTotalDiscountValue]);

  const { remainNewCreditAmount, remainOldCreditAmount, oldCredit, newCredit } = useMemo(() => {
    const { newCredit, oldCredit } = getCredits(currentCustomer?.credits || []);
    let newCreditBalance = Number(newCredit?.balance || 0);
    let oldCreditBalance = Number(oldCredit?.balance || 0);
    if (type === 'update') {
      newCreditBalance += Number(
        defaultPaymentMethods
          ?.filter(({ paymentMethod }) => paymentMethod?.code === ECreditMethods.NEW_CREDIT)
          ?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0) || 0
      );
      oldCreditBalance += Number(
        defaultPaymentMethods
          ?.filter(({ paymentMethod }) => paymentMethod?.code === ECreditMethods.OLD_CREDIT)
          ?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0) || 0
      );
    }
    const remainNewCreditAmount: number =
      newCreditBalance -
      Number(
        paymentMethods
          ?.filter(({ paymentMethod }) => paymentMethod?.code === ECreditMethods.NEW_CREDIT)
          ?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0) || 0
      );
    const remainOldCreditAmount: number =
      oldCreditBalance -
      Number(
        paymentMethods
          ?.filter(({ paymentMethod }) => paymentMethod?.code === ECreditMethods.OLD_CREDIT)
          ?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0) || 0
      );
    const hasNewCreditMethod = invoiceDetail?.invoicePayments?.find(
      invoicePayment => invoicePayment?.paymentMethod?.code === ECreditMethods.NEW_CREDIT
    );

    const hasOldCreditMethod = invoiceDetail?.invoicePayments?.find(
      invoicePayment => invoicePayment?.paymentMethod?.code === ECreditMethods.OLD_CREDIT
    );
    return {
      remainNewCreditAmount,
      remainOldCreditAmount,
      newCredit,
      oldCredit,
      hasNewCreditMethod,
      hasOldCreditMethod,
    };
  }, [JSON.stringify(currentCustomer), paymentMethods, defaultPaymentMethods, type]);

  let total =
    (invoiceNewPaid?.subTotal || 0) +
    calculateTax(invoiceNewPaid?.totalBeforeTax || 0, Number(TAX || 0)) -
    totalDiscountValue;
  total = total >= 0 ? total : 0;

  const balance = Number(total - totalPayment).toFixed(2);
  const payValue = valueInput === '0' ? balance : valueInput;

  const invoiceOrders = useMemo(() => {
    const temp: TInvoiceOrder[] = [];
    invoiceDetail?.orders
      ?.filter(order => !isEmpty(order?.items))
      ?.forEach(order => {
        temp.push({
          items:
            order?.items?.map(item => ({
              id: item?.id || '',
              name: item?.product?.name || 'N/A',
              quantity: item?.quantity || 1,
              price: item?.couponCode ? 0 : item?.product?.price || 0,
              note: item?.note || '',
              couponCode: item?.couponCode || '',
            })) || [],
          totalBeforeTax: order?.totalBeforeTax || 0,
          subTotal: order?.subTotal || 0,
          code: order?.code || '',
          transferBy: order?.transferBy,
        });
      });
    return temp;
  }, [JSON.stringify(invoiceDetail?.orders)]);

  const onCompletePayment = async () => {
    try {
      // Get all order

      let response = null;
      //
      if (type === 'create' && isEmpty(invoices)) {
        const orderCreate = orders?.map<TOrder>(order => ({
          id: order.id,
          // items: order.items,
          orderType: order.orderType,
        }));
        response = await onCreateInvoice({
          orders: orderCreate,
          status: EInvoiceStatus.PAID,
          appointment: { id: appointmentId },
          invoicePayments: paymentMethods,
          discounts: voucherChecked.voucherResponse,
          childInvoices: invoices?.map(i => ({ id: i?.id })),
          tax: TAX,
          note: openRemarkModal.note || orderDetail?.note || remarkTotalNote?.note,
        });
      } else {
        const orderUpdate = (invoices?.[0]?.orders || [])?.map<TOrder>(order => ({
          id: order.id,
          // items: order.items,
          orderType: order.orderType,
        }));
        response = await onUpdateInvoice({
          id: invoices?.[0]?.id || '',
          orders: orderUpdate,
          status: EInvoiceStatus.PAID,
          appointment: { id: appointmentId },
          invoicePayments: paymentMethods,
          discounts: voucherChecked.voucherResponse,
          childInvoices: [],
          tax: TAX,
          note: openRemarkModal.note || orderDetail?.note || remarkTotalNote?.note,
        });
      }
      setRemarkTotalNote({ note: '' });
      if (response.status === 'error') {
        if (typeof response?.message?.error === 'string') {
          showError({ title: response?.message?.error });
          return;
        }
        if (Array.isArray(response.message.error)) {
          showError({ title: 'Update failed' });
          return;
        }
        // It's a TApiError
        const apiError = response.message.error as TApiError;
        showError({ title: apiError?.message });
        return;
      }
      await refetchCurrentCustomer();
      showSuccess({ title: 'Payment successful!' });
      setInvoiceDetail(response?.data);
      setValueInput('0');
      if (!isPrintReceipt) return onComplete();
    } catch (e) {
      console.log(1, e);
    }
  };

  const onSelectPaymentMethod = (method: TPaymentMethod) => {
    // using credit
    let newValue = Number(balance) > Number(payValue) ? Number(payValue) : Number(balance);
    if (method.code === ECreditMethods.NEW_CREDIT || method.code === ECreditMethods.OLD_CREDIT) {
      let newTotalBeforeTax = (Number(invoiceNewPaid?.totalBeforeTax) || 0) - (Number(newValue || 0) || 0);
      let newSubtotal = total - taxCaculated + calculateTax(newTotalBeforeTax, Number(TAX || 0));
      if (totalPayment + newValue >= newSubtotal) {
        newValue = Number((newValue / 1.09).toFixed(2));
        newTotalBeforeTax = (Number(invoiceNewPaid?.totalBeforeTax) || 0) - (Number(newValue || 0) || 0);
        newSubtotal = total - taxCaculated + calculateTax(newTotalBeforeTax, Number(TAX || 0));
        if (totalPayment + newValue > newSubtotal) {
          newValue = Number((newSubtotal - totalPayment).toFixed(2));
        }
      }
      if (method.code === ECreditMethods.NEW_CREDIT && remainNewCreditAmount < Number(newValue)) {
        newValue = Number(payValue);
        showError({ title: 'Not enough credit!' });
        return;
      }
      if (method.code === ECreditMethods.OLD_CREDIT && remainOldCreditAmount < Number(newValue)) {
        newValue = Number(payValue);
        showError({ title: 'Not enough credit!' });
        return;
      }
      setInvoiceNewPaid(prev => ({
        totalBeforeTax: (Number(prev?.totalBeforeTax) || 0) - (Number(newValue) || 0),
        subTotal: prev?.subTotal || 0,
        totalPaid: prev?.totalPaid || 0,
        isEdited: prev?.isEdited || false,
        invoiceCoupon: [...(prev?.invoiceCoupon || [])],
      }));
    }
    if (method?.isOptionBillCode) {
      setOpenShopfiCodeModal(prev => ({
        ...prev,
        shopifyCode: '',
        paymentMethod: method,
        paid: Number(balance) > Number(payValue) ? Number(payValue) : Number(balance),
        isOpen: true,
      }));
    }
    if (!method?.isOptionBillCode) {
      const roundNumber = Number((newValue - roundToNearestHalf(newValue)).toFixed(2));
      onAddPaymentMethod({
        method,
        paid: method.code === ECreditMethods.CASH ? roundToNearestHalf(newValue) : newValue,
        roundNumber: method.code === ECreditMethods.CASH ? roundNumber : 0,
      });
    }
  };

  useEffect(() => {
    if (invoiceDetail && isPrintReceipt) {
      handlePrint();
      onComplete();
      setRemarkTotalNote({ note: '' }); // Reset remark after creating invoice
      setInvoiceDetail(undefined);
    }
  }, [invoiceDetail, isPrintReceipt]);

  useEffect(() => {
    if (invoices?.[0]?.newPaid) {
      setInvoiceNewPaid(invoices?.[0]?.newPaid);
    }
  }, [invoices?.[0]]);

  useEffect(() => {
    if (isCreateMode) {
      const totalOrders = orders?.reduce((pre, cur) => pre + (cur?.subTotal ?? 0), 0);
      const totalOrderWithDiscount: number = Math.max(totalOrders ?? 0, 0);
      setInvoiceNewPaid({
        invoiceCoupon: [],
        totalBeforeTax: totaInvoiceUnpaid + totalOrderWithDiscount,
        totalPaid: 0,
        subTotal: totaInvoiceUnpaid + totalOrderWithDiscount,
        isEdited: false,
      });
    }
  }, [totaInvoiceUnpaid, isCreateMode]);

  useEffect(() => {
    if (invoices?.[0]?.note) {
      setOpenRemarkModal(prev => ({ ...prev, note: invoices?.[0]?.note || '' }));
    }
  }, [invoices?.[0]?.note]);

  const hanleRemovePaymentMethod = (paymentMethodId: string) => {
    const temp = [...(paymentMethods || [])];
    const foundMethod = temp.find(method => method?.paymentMethod?.id === paymentMethodId);
    if (
      foundMethod?.paymentMethod?.code === ECreditMethods.NEW_CREDIT ||
      foundMethod?.paymentMethod?.code === ECreditMethods.OLD_CREDIT
    ) {
      const totalCreditAmount =
        paymentMethods
          ?.filter(
            item =>
              item?.paymentMethod?.code === ECreditMethods.NEW_CREDIT ||
              item?.paymentMethod?.code === ECreditMethods.OLD_CREDIT
          )
          ?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0) || 0;
      const totalCreditRefund = Number(foundMethod?.paid || 0);
      setInvoiceNewPaid(prev => ({
        totalBeforeTax: (prev?.subTotal || 0) - totalDiscountValue - totalCreditAmount + totalCreditRefund,
        subTotal: prev?.subTotal || 0,
        totalPaid: prev?.totalPaid || 0,
        isEdited: prev?.isEdited || false,
        invoiceCoupon: [...(prev?.invoiceCoupon || [])],
      }));
    }
    onRemovePaymentMethod(paymentMethodId);
  };
  const handleOnRemoveVoucher = (type: ECouponType, couponCode?: string, index?: number) => {
    const newTotalDiscount =
      voucherChecked.voucherResponse
        .filter((v, i) => index !== i)
        ?.reduce((pre, cur) => pre + Number(cur?.discountMoney ?? 0), 0) || 0;
    const totalCreditAmount =
      paymentMethods
        ?.filter(
          item =>
            item?.paymentMethod?.code === ECreditMethods.NEW_CREDIT ||
            item?.paymentMethod?.code === ECreditMethods.OLD_CREDIT
        )
        ?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0) || 0;
    setInvoiceNewPaid(prev => ({
      totalBeforeTax: (prev?.subTotal || 0) - newTotalDiscount - totalCreditAmount,
      subTotal: prev?.subTotal || 0,
      totalPaid: prev?.totalPaid || 0,
      isEdited: prev?.isEdited || false,
      invoiceCoupon: [...(prev?.invoiceCoupon || [])],
    }));
    onRemoveVoucher(type, couponCode, index);
  };

  // console.log('aaaa', {
  //   openRemarkModal: openRemarkModal.note,
  //   orderDetail: orderDetail?.note,
  //   remarkTotalNote: remarkTotalNote?.note,
  //   invoiceDetail: invoiceDetail?.note,
  // });

  return (
    <>
      <StyledContainer>
        <Stack flex="1 1 40%" gap={1.5}>
          <CashierCustomerInfo
            name={concatenateNames(customerInfo?.firstName || '', customerInfo?.lastName || '')}
            phone={customerInfo?.phone || ''}
            rfid={rfid || ''}
            credit={remainNewCreditAmount}
            oldCredit={remainOldCreditAmount}
            avatar={customerInfo?.avatar?.url || ''}
            isCreditNewExpired={newCredit.isExpired}
            isCreditOldExpired={oldCredit.isExpired}
            isCreditNewShow={newCredit?.isShow}
            isCreditOldShow={oldCredit?.isShow}
            passportExpiryDate={customerInfo?.expiryDate}
          />
          <Stack height="100%">
            <CashierResultCaculator
              type={type}
              paymentMethods={paymentMethods?.filter(pay => pay?.paid > 0)}
              onRemovePaymentMethod={hanleRemovePaymentMethod}
              onComplete={onCompletePayment}
              invoices={invoices}
              orders={orders}
              taxCalculated={taxCaculated}
              total={total}
              balance={balance}
              isPrintReceipt={isPrintReceipt}
              setIsPrintReceipt={setIsPrintReceipt}
              openModalDiscount={() => setOpenModalDiscount(true)}
              openRemarkModal={() =>
                setOpenRemarkModal(prev => ({
                  ...prev,
                  isOpen: true,
                  note: openRemarkModal.note || orderDetail?.note || remarkTotalNote?.note || invoiceDetail?.note || '',
                }))
              }
              onRemoveDiscount={handleOnRemoveVoucher}
              discounts={voucherChecked}
              note={openRemarkModal.note || orderDetail?.note || remarkTotalNote?.note || invoiceDetail?.note || ''}
            />
          </Stack>
        </Stack>
        <Stack width="100%" flex="1 1 40%" gap={1.5}>
          <CashierPayDisplay value={payValue} isLoading={isCreating || isUpdating} />
          <Stack>
            <CashierPaymentNumberBoard onNumberPress={onKeyboardPress} />
            <StyledPaymentContainer>
              {TOTAL_PAYMENT_METHOD?.map(method => (
                <StyledPaymentItem flex="1 1 25%" key={method?.id} onClick={() => onSelectPaymentMethod(method)}>
                  <StyledPaymentText variant="heading-medium-700">{method?.name}</StyledPaymentText>
                </StyledPaymentItem>
              ))}
            </StyledPaymentContainer>
          </Stack>
        </Stack>
      </StyledContainer>
      {invoiceDetail && (
        <Box position="absolute" zIndex={-1100} ref={componentToPrintRef}>
          <InvoiceOrderView
            variant="invoice"
            orders={invoiceOrders}
            createdDate={invoiceDetail?.created || 'N/A'}
            customer={{
              id: invoiceDetail?.customer?.code || 'N/A',
              name: concatenateNames(invoiceDetail?.customer?.firstName || '', invoiceDetail?.customer?.lastName || ''),
              phone: invoiceDetail?.customer?.phone || 'N/A',
              rfid: invoiceDetail?.appointment?.rfid || 'N/A',
            }}
            employee={{
              name:
                invoiceDetail?.referral?.displayName ||
                invoiceDetail?.referral?.fullName ||
                invoiceDetail?.referral?.username ||
                '',
            }}
            paymentMethods={
              invoiceDetail?.invoicePayments?.map(({ paid, paymentMethod, roundNumber, billCode }) => ({
                name: paymentMethod?.name || '',
                amount: paid ?? 0,
                roundNumber: roundNumber ?? 0,
                billCode: billCode || '',
              })) || []
            }
            note={invoiceDetail?.note || remarkTotalNote?.note || ''}
            orderInvoiceId={(invoiceDetail?.code || '')?.slice(0, 8)}
            branch={{
              address: invoiceDetail?.branch?.address || '',
              phone:
                invoiceDetail?.branch?.phones
                  ?.map((phone: string) => {
                    const temp = parseDialCodeAndPhoneNumber(phone);
                    return `${temp?.dialCode ? `(${temp?.dialCode})` : ''} ${temp?.phoneNumber}`;
                  })
                  ?.join(' - ') || '',
            }}
            total={invoiceDetail?.invoicePayments?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0) || 0}
            totalRounding={invoiceDetail?.invoicePayments?.reduce((pre, cur) => pre + (cur?.roundNumber ?? 0), 0) || 0}
            taxPercent={invoiceDetail?.tax || 0}
            subTotal={invoiceDetail?.subTotal || 0}
            totalBeforeTax={invoiceDetail?.totalBeforeTax || 0}
            coupons={
              invoiceDetail?.invoiceCoupon
                ?.sort((a, b) => (a?.order || 0) - (b?.order || 0))
                ?.map(item => ({
                  couponCode: item?.couponCode || '',
                  couponName: item?.couponName || '',
                  discountMoney: item?.discountValue || 0,
                  discountProductId: '',
                })) || []
            }
            unPaid={invoiceDetail?.unPaid || 0}
            newCredit={{
              balance: newCredit?.balance || 0,
              expiry: newCredit?.expiredDate || '',
            }}
            oldCredit={{
              balance: oldCredit?.balance || 0,
              expiry: oldCredit?.rawExpiredDate || '',
            }}
            childInvoices={invoiceDetail?.childInvoices}
          />
        </Box>
      )}
      <SelectCouponModal
        isOpen={isOpenModalDiscount}
        onClose={() => setOpenModalDiscount(false)}
        usedCoupons={voucherChecked?.voucherChoose?.map(voucher => voucher?.couponCode || '') || []}
        onApply={onApplyCoupon}
        errorText={couponError}
        setCouponError={setCouponError}
        customerId={customerInfo?.id || ''}
      />
      <ShopifyCodeModal
        isOpen={openShopifyCodeModal.isOpen}
        handleClose={() => setOpenShopfiCodeModal(prev => ({ ...prev, isOpen: false }))}
        defaultValues={{ billCode: openShopifyCodeModal?.shopifyCode || '' }}
        handleConfirm={value => {
          setOpenShopfiCodeModal(prev => ({ ...prev, shopifyCode: value?.billCode || '', isOpen: false }));
          if (openShopifyCodeModal?.paymentMethod) {
            const roundNumber = Number(
              (openShopifyCodeModal.paid - roundToNearestHalf(openShopifyCodeModal.paid)).toFixed(2)
            );
            onAddPaymentMethod({
              method: openShopifyCodeModal?.paymentMethod,
              billCode: value?.billCode || '',
              paid:
                openShopifyCodeModal?.paymentMethod?.code === ECreditMethods.CASH
                  ? roundToNearestHalf(openShopifyCodeModal.paid)
                  : openShopifyCodeModal.paid,
              roundNumber: openShopifyCodeModal?.paymentMethod?.code === ECreditMethods.CASH ? roundNumber : 0,
            });
          }
        }}
      />
      <ModalRemark
        isOpen={openRemarkModal.isOpen}
        handleClose={() => setOpenRemarkModal({ note: '', isOpen: false })}
        defaultValues={{ remark: openRemarkModal?.note || '' }}
        handleConfirm={({ remark }: { remark: string }) => {
          setOpenRemarkModal({
            note: remark,
            isOpen: false,
          });
        }}
      />
    </>
  );
};
