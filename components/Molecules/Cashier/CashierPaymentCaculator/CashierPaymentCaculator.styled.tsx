import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  gap: theme.spacing(1.5),
  flexWrap: 'nowrap',
  background: 'white',
  borderRadius: '10px',
  flexDirection: 'row',
  justifyContent: 'space-between',
  backgroundColor: theme.palette.neutrals.N100.main,
}));

export const StyledCustomerInfo = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(1.5),
  alignItems: 'center',
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  minWidth: theme.spacing(6.25),
}));

export const StyledCustomerInfoContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(2.25),
  alignItems: 'center',
  padding: theme.spacing(1.5),
  borderRadius: theme.spacing(1.25),
  background: 'white',
  border: `1px solid ${theme.palette.neutrals.N180.main}`,
}));

export const StyledPaymentItem = styled(Stack)(({ theme }) => ({
  padding: theme.spacing(1.5),
  borderRadius: theme.spacing(1.25),
  background: theme.palette.neutrals.N10.main,
  color: theme.palette.primary.main,
  textAlign: 'center',
  maxHeight: theme.spacing(5),
  cursor: 'pointer',
}));

export const StyledPaymentContainer = styled(Stack)(({ theme }) => ({
  paddingTop: theme.spacing(3),
  flexDirection: 'row',
  flexWrap: 'wrap',
  gap: theme.spacing(1.5),
}));

export const StyledPaymentText = styled(Typography)(({ theme }) => ({}));
