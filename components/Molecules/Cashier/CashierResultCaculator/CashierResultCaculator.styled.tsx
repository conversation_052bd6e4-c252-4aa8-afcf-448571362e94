import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Typography from '@/components/Atoms/Typography';
import Button from '@/components/Atoms/Button';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  gap: theme.spacing(1.5),
  padding: theme.spacing(1.5),
  borderRadius: theme.spacing(1.25),
  background: 'white',
  border: `1px solid ${theme.palette.neutrals.N180.main}`,
  height: '100%',
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  width: '60%',
  color: theme.palette.neutrals.N190.main,
}));

export const StyledCount = styled(Typography)(({ theme }) => ({
  width: '10%',
  color: theme.palette.neutrals.N190.main,
}));

export const StyledButton = styled(Button)<{ canClick?: boolean }>(({ theme, canClick }) => ({
  background: `${canClick ? theme.palette.success.main : theme.palette.neutrals.N210.main} !important`,
  borderRadius: `${theme.spacing(1.25)} !important`,
  border: `none !important`,
  color: `${canClick ? theme.palette.common.white : theme.palette.primary.main} !important`,
}));
