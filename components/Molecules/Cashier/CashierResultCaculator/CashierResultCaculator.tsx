import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import { useMemo, useContext } from 'react';
import Button from '@/components/Atoms/Button';
import Checkbox from '@/components/Atoms/Checkbox';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import { TAX } from '@/lib/constants/tax';
import { TVoucherChecked } from '@/lib/hooks/utils/useCouponApply';
import { TPaymentState } from '@/lib/hooks/utils/usePaymentCaculator';
import { TInvoice } from '@/lib/types/entities/invoice';
import { TOrder, TOrderItem } from '@/lib/types/entities/order';
import { ECouponType } from '@/lib/types/enum/coupon';
import { Price } from '../../Price/Price';
import { StyledButton, StyledContainer, StyledCount, StyledTitle } from './CashierResultCaculator.styled';
import { EOrderType } from '@/lib/types/enum/order';
import { EInvoiceStatus } from '@/lib/types/enum/invoice';
import { ECreditMethods } from '@/lib/types/enum/paymentMethod';
import { customPalettes } from '@/theme/customPalettes';
import { roundToNearestHalf } from '@/lib/utils/string';
import { CashierMenuContext } from '@/lib/context/CashierMenuContext';

type TCashierResultCaculator = {
  invoices?: TInvoice[];
  orders?: TOrder[];
  paymentMethods?: TPaymentState;
  taxCalculated: number;
  total: number;
  balance: number | string;
  isPrintReceipt: boolean;
  discounts: TVoucherChecked;
  type: 'create' | 'update';
  note: string;
  setIsPrintReceipt: (value: boolean) => void;
  onRemovePaymentMethod: (paymentMethodId: string) => void;
  onRemoveDiscount: (type: ECouponType, couponCode?: string, index?: number) => void;
  onComplete: () => void;
  openModalDiscount: () => void;
  openRemarkModal: () => void;
};

export const CashierResultCaculator: React.FC<TCashierResultCaculator> = ({
  invoices,
  orders,
  paymentMethods,
  total,
  balance,
  isPrintReceipt,
  setIsPrintReceipt,
  onComplete,
  onRemovePaymentMethod,
  openModalDiscount,
  discounts,
  onRemoveDiscount,
  taxCalculated = 0,
  type = 'create',
  openRemarkModal,
  note,
}) => {
  const isInvoiceChanged = invoices?.at(0)?.newPaid?.isEdited;

  // const { ShowInvoiceChanged }: { ShowInvoiceChanged: React.ReactNode } = useMemo(() => {
  //   const newInvoice = invoices?.at(0);
  //   if (!isInvoiceChanged) return { ShowInvoiceChanged: <></> };
  //   const newPaid = newInvoice?.newPaid;

  //   const component = (
  //     <>
  //       <Stack flexDirection="row" justifyContent="space-between">
  //         <StyledTitle variant="heading-medium-700">{`Changed #${newInvoice?.code?.slice(0, 8)}`}</StyledTitle>
  //         <Price amount={newPaid?.totalBeforeTax} typographyProps={{ variant: 'heading-medium-700' }} />
  //       </Stack>
  //       <Stack flexDirection="row" justifyContent="space-between">
  //         <StyledTitle variant="heading-medium-700">{`Tax ${TAX}%`}</StyledTitle>
  //         <Price
  //           amount={(newPaid?.totalPaid || 0) - (newPaid?.totalBeforeTax || 0)}
  //           typographyProps={{ variant: 'heading-medium-700' }}
  //         />
  //       </Stack>
  //     </>
  //   );
  //   return { ShowInvoiceChanged: component };
  // }, [invoices]);

  const { ShowInvoicePartPaid }: { ShowInvoicePartPaid: React.ReactNode } = useMemo(() => {
    const component = (
      <>
        {invoices
          ?.filter(invoice => invoice?.status === EInvoiceStatus['PART_PAID'])
          ?.map(
            invoice =>
              // <Stack key={invoice?.id} flexDirection="row" justifyContent="space-between">
              //   <StyledTitle variant="heading-medium-700">{`#${invoice?.code?.slice(0, 8)}`}</StyledTitle>
              //   <Price amount={invoice?.newPaid?.subTotal} typographyProps={{ variant: 'heading-medium-700' }} />
              // </Stack>
              invoice?.orders
                ?.reduce((pre, cur) => [...pre, ...(cur?.items || [])], [] as TOrderItem[])
                ?.map(item => (
                  <>
                    <Stack key={item?.id} flexDirection="row" justifyContent="space-between" gap={0.5}>
                      <StyledTitle variant="heading-medium-700">{item?.product?.name}</StyledTitle>
                      <StyledCount variant="heading-medium-700">x{item?.quantity}</StyledCount>
                      <Price
                        amount={item?.price}
                        typographyProps={{ variant: 'heading-medium-700' }}
                        containerProps={{ sx: { width: '20%', textAlign: 'right' } }}
                        showTwoDecimalPlace
                      />
                    </Stack>
                  </>
                ))
          )}
        {discounts?.voucherResponse?.map((d, indx) => {
          const discountName = {
            // [ECouponType.PERCENTAGE]: `${d.couponType}(${d?.percent}%)`,
            [ECouponType.PERCENTAGE]: `Discount (${d?.percent}%)`,
            [ECouponType.ID_CODE]: `${d.couponName}`,
            [ECouponType.MONEY]: `${d.couponType}`,
          }[d.couponType];

          return (
            <Stack
              key={`${d?.couponType}-${d.discountMoney}-${d?.id}-${indx.toString()}`}
              flexDirection="row"
              justifyContent="space-between"
            >
              <StyledTitle variant="heading-medium-700">
                {discountName}
                {d?.couponCode ? `(${d?.couponCode})` : ''}
              </StyledTitle>
              <Stack flexDirection="row" alignItems="center" gap={0.5}>
                <Price
                  amount={Number(d.discountMoney) * -1}
                  typographyProps={{ variant: 'heading-medium-700' }}
                  showTwoDecimalPlace
                />

                {!d?.id && (
                  <IconButton
                    sx={{ width: '12px', height: '12px', background: '#F5F4F3' }}
                    onClick={() => onRemoveDiscount(d.couponType, d?.couponCode, indx)}
                  >
                    <Icon variant="close" size={1.5} />
                  </IconButton>
                )}
              </Stack>
            </Stack>
          );
        })}
        <Stack key={invoices?.at(0)?.id} flexDirection="row" justifyContent="space-between">
          <StyledTitle variant="heading-medium-700">{`Tax ${TAX}%`}</StyledTitle>
          <Price amount={taxCalculated} typographyProps={{ variant: 'heading-medium-700' }} showTwoDecimalPlace />
        </Stack>
      </>
    );
    return { ShowInvoicePartPaid: component };
  }, [invoices, discounts?.voucherResponse]);

  const { ShowOldInvoicePayment }: { ShowOldInvoicePayment: React.ReactNode } = useMemo(() => {
    const invoice = invoices?.at(0);
    const component = (
      <>
        {/* <Stack key={invoice?.id} flexDirection="row" justifyContent="space-between">
          <StyledTitle variant="heading-medium-700">{`#${invoice?.code?.slice(0, 8)}`}</StyledTitle>
          <Price amount={invoice?.newPaid?.subTotal} typographyProps={{ variant: 'heading-medium-700' }} />
        </Stack> */}
        {invoice?.orders
          ?.reduce((pre, cur) => [...pre, ...(cur?.items || [])], [] as TOrderItem[])
          ?.map(item => (
            <>
              <Stack key={item?.id} flexDirection="row" justifyContent="space-between" gap={0.5}>
                <StyledTitle variant="heading-medium-700">{item?.product?.name}</StyledTitle>
                <StyledCount variant="heading-medium-700">x{item?.quantity}</StyledCount>
                <Price
                  amount={item?.price}
                  typographyProps={{ variant: 'heading-medium-700' }}
                  containerProps={{ sx: { width: '20%', textAlign: 'right' } }}
                  showTwoDecimalPlace
                />
              </Stack>
            </>
          ))}
        {discounts?.voucherResponse?.map((d, indx) => {
          const discountName = {
            // [ECouponType.PERCENTAGE]: `${d.couponType}(${d?.percent}%)`,
            [ECouponType.PERCENTAGE]: `Discount (${d?.percent}%)`,
            [ECouponType.ID_CODE]: `${d.couponName}`,
            [ECouponType.MONEY]: `${d.couponType}`,
          }[d.couponType];

          return (
            <Stack
              key={`${d?.couponType}-${d.discountMoney}-${d?.id}-${indx.toString()}`}
              flexDirection="row"
              justifyContent="space-between"
              alignItems="flex-start"
            >
              <StyledTitle variant="heading-medium-700">
                {discountName}
                {d?.couponCode ? `(${d?.couponCode})` : ''}
              </StyledTitle>
              <Stack flexDirection="row" alignItems="center" gap={0.5}>
                <Price
                  amount={Number(d.discountMoney) * -1}
                  typographyProps={{ variant: 'heading-medium-700' }}
                  showTwoDecimalPlace
                />

                {!d?.id && (
                  <IconButton
                    sx={{ width: '12px', height: '12px', background: '#F5F4F3' }}
                    onClick={() => onRemoveDiscount(d.couponType, d?.couponCode, indx)}
                  >
                    <Icon variant="close" size={1.5} />
                  </IconButton>
                )}
              </Stack>
            </Stack>
          );
        })}
        <Stack key={invoice?.id} flexDirection="row" justifyContent="space-between">
          <StyledTitle variant="heading-medium-700">{`Tax ${TAX}%`}</StyledTitle>
          <Price amount={taxCalculated} typographyProps={{ variant: 'heading-medium-700' }} showTwoDecimalPlace />
        </Stack>
      </>
    );
    return { ShowOldInvoicePayment: component };
  }, [invoices, discounts?.voucherResponse, taxCalculated, total]);

  const { ShowOrder }: { ShowOrder: React.ReactNode } = useMemo(() => {
    const orderDisplay: Record<EOrderType, string> = {
      others: 'OTHER',
      'food-beverage': 'F&B',
      membership: 'MEMBERSHIP',
      transfer: 'TRANSFER',
    };

    const component = (
      <>
        {/* {orders?.map(order => (
          <Stack key={order?.id} flexDirection="row" justifyContent="space-between">
            <StyledTitle variant="heading-medium-700">
              {order.orderType ? `${orderDisplay[order.orderType]}` : ''}
            </StyledTitle>
            <Price amount={order?.subTotal} typographyProps={{ variant: 'heading-medium-700' }} />
          </Stack>
        ))} */}
        {orders
          ?.reduce((pre, cur) => [...pre, ...(cur?.items || [])], [] as TOrderItem[])
          ?.map(item => (
            <>
              <Stack key={item?.id} flexDirection="row" justifyContent="space-between" gap={0.5}>
                <StyledTitle variant="heading-medium-700">{item?.product?.name}</StyledTitle>
                <StyledCount variant="heading-medium-700">x{item?.quantity}</StyledCount>
                <Price
                  amount={item?.price}
                  typographyProps={{ variant: 'heading-medium-700' }}
                  containerProps={{ sx: { width: '20%', textAlign: 'right' } }}
                  showTwoDecimalPlace
                />
              </Stack>
            </>
          ))}
        {discounts?.voucherResponse?.map((d, indx) => {
          const discountName = {
            // [ECouponType.PERCENTAGE]: `${d.couponType}(${d?.percent}%)`,
            [ECouponType.PERCENTAGE]: `Discount (${d?.percent}%)`,
            [ECouponType.ID_CODE]: `${d.couponName}`,
            [ECouponType.MONEY]: `${d.couponType}`,
          }[d.couponType];

          return (
            <Stack
              key={`${d?.couponType}-${d.discountMoney}-${indx.toString()}`}
              flexDirection="row"
              justifyContent="space-between"
              alignItems="flex-start"
            >
              <StyledTitle variant="heading-medium-700">
                {discountName}
                {d?.couponCode ? ` (${d?.couponCode})` : ''}
              </StyledTitle>
              <Stack flexDirection="row" alignItems="center" gap={0.5}>
                <Price
                  amount={Number(d.discountMoney) * -1}
                  typographyProps={{ variant: 'heading-medium-700' }}
                  showTwoDecimalPlace
                />

                {!d?.id && (
                  <IconButton
                    sx={{ width: '12px', height: '12px', background: '#F5F4F3' }}
                    onClick={() => onRemoveDiscount(d.couponType, d?.couponCode, indx)}
                  >
                    <Icon variant="close" size={1.5} />
                  </IconButton>
                )}
              </Stack>
            </Stack>
          );
        })}

        <Stack flexDirection="row" justifyContent="space-between">
          <StyledTitle variant="heading-medium-700">{`Tax ${TAX}%`}</StyledTitle>
          <Price amount={taxCalculated} typographyProps={{ variant: 'heading-medium-700' }} showTwoDecimalPlace />
        </Stack>
      </>
    );
    return { ShowOrder: component };
  }, [orders, discounts, taxCalculated, total]);

  const { ShowPaymentMethods }: { ShowPaymentMethods: React.ReactNode } = useMemo(() => {
    const component = paymentMethods?.map(item =>
      (item?.roundNumber || 0) > 0 ? (
        <Stack gap={1.5}>
          <Stack flexDirection="row" key={`method-${item?.paymentMethod?.id}`} justifyContent="space-between">
            <Typography variant="heading-medium-700">
              {`${item?.paymentMethod?.name}`}
              {item?.paymentMethod?.isOptionBillCode && item?.billCode ? `(${item?.billCode})` : ''}
            </Typography>
            <Stack flexDirection="row" alignItems="center" gap={0.5}>
              <Stack flexDirection="row" gap={0.25}>
                <Price
                  amount={
                    item?.paymentMethod?.code === ECreditMethods.CASH ? roundToNearestHalf(item?.paid) : item?.paid
                  }
                  typographyProps={{ variant: 'heading-medium-700' }}
                  showTwoDecimalPlace
                />
              </Stack>
              <IconButton
                sx={{ width: '12px', height: '12px', background: '#F5F4F3' }}
                onClick={() => onRemovePaymentMethod(item?.paymentMethod?.id)}
              >
                <Icon variant="close" size={1.5} />
              </IconButton>
            </Stack>
          </Stack>
          <Stack flexDirection="row" key={`method-${item?.paymentMethod?.id}`} justifyContent="space-between">
            <Typography variant="heading-medium-700" sx={theme => ({ color: theme.palette.neutrals.N190.main })}>
              Rounding adjustment
            </Typography>
            <Price
              amount={item?.roundNumber}
              typographyProps={{
                variant: 'heading-medium-700',
                sx: theme => ({ color: theme.palette.neutrals.N190.main }),
              }}
              showTwoDecimalPlace
            />
          </Stack>
        </Stack>
      ) : (
        <>
          {' '}
          <Stack flexDirection="row" key={`method-${item?.paymentMethod?.id}`} justifyContent="space-between">
            <Typography variant="heading-medium-700">
              {`${item?.paymentMethod?.name}`}
              {item?.paymentMethod?.isOptionBillCode && item?.billCode ? `(${item?.billCode})` : ''}
            </Typography>
            <Stack flexDirection="row" alignItems="center" gap={0.5}>
              <Stack flexDirection="row" gap={0.25}>
                <Price
                  amount={
                    item?.paymentMethod?.code === ECreditMethods.CASH ? roundToNearestHalf(item?.paid) : item?.paid
                  }
                  typographyProps={{ variant: 'heading-medium-700' }}
                  showTwoDecimalPlace
                />
                {item?.paymentMethod?.code === ECreditMethods.CASH && (item?.roundNumber || 0) > 0 ? (
                  <Typography variant="heading-medium-700">({item?.roundNumber})</Typography>
                ) : (
                  <></>
                )}
              </Stack>
              <IconButton
                sx={{ width: '12px', height: '12px', background: '#F5F4F3' }}
                onClick={() => onRemovePaymentMethod(item?.paymentMethod?.id)}
              >
                <Icon variant="close" size={1.5} />
              </IconButton>
            </Stack>
          </Stack>
        </>
      )
    );
    return { ShowPaymentMethods: component };
  }, [invoices, orders, paymentMethods]);

  return (
    <>
      <StyledContainer>
        {(invoices?.length || 0) > 0 && (
          <>
            <Stack gap={1.5}>
              {invoices?.[0]?.status !== EInvoiceStatus['PART_PAID'] || isInvoiceChanged
                ? ShowOldInvoicePayment
                : ShowInvoicePartPaid}
            </Stack>
            {(isInvoiceChanged ||
              !!invoices?.find(invoice => (invoice?.unPaid || 0) > 0) ||
              invoices?.[0]?.status !== EInvoiceStatus['PART_PAID']) && <Divider />}
          </>
        )}
        {(orders?.length || 0) > 0 && (
          <>
            <Stack gap={1.5}>{ShowOrder}</Stack>
            <Divider />
          </>
        )}
        {/* {(invoices?.length || 0) > 0 && isInvoiceChanged && (
          <>
            <Stack gap={1.5}>{ShowInvoiceChanged}</Stack>
            <Divider />
          </>
        )} */}
        <Stack gap={1.5}>
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="heading-medium-700">Total </Typography>
            <Price amount={total} typographyProps={{ variant: 'heading-medium-700' }} showTwoDecimalPlace />
          </Stack>
          {ShowPaymentMethods}
        </Stack>
        <Divider />
        <Stack flexDirection="row" justifyContent="space-between">
          <Typography variant="heading-medium-700">Balance</Typography>
          <Price
            amount={Number(balance) > 0 ? Number(balance) : 0}
            typographyProps={{ variant: 'heading-medium-700' }}
            showTwoDecimalPlace
          />
        </Stack>
        {Number(balance) < 0 && (
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="heading-medium-700">Refund</Typography>
            <Price
              amount={Math.abs(Number(balance))}
              typographyProps={{ variant: 'heading-medium-700' }}
              showTwoDecimalPlace
            />
          </Stack>
        )}
        {note && (
          <Typography variant="body-xlarge-400" color={customPalettes?.primary?.main}>
            {note}
          </Typography>
        )}
        <Stack flex={1} justifyContent="flex-end" gap={3}>
          <Stack flexDirection="row" justifyContent="space-between" alignItems="center">
            <Checkbox label="Print receipt" checked={isPrintReceipt} onChange={check => setIsPrintReceipt(check)} />
            <Typography
              variant="heading-medium-700"
              color={customPalettes?.info?.main}
              sx={{
                cursor: 'pointer',
              }}
              onClick={openRemarkModal}
            >
              Remark
            </Typography>
          </Stack>
          <Stack flexDirection="row" justifyContent="space-between" gap={1.5}>
            <Button variant="contained" label="Apply Discounts" onClick={openModalDiscount} />
            <StyledButton
              variant="contained"
              label={type === 'create' ? 'Complete' : 'Update'}
              color="success"
              onClick={() => Number(balance) <= 0 && onComplete()}
              canClick={Number(balance) <= 0}
            />
          </Stack>
        </Stack>
      </StyledContainer>
    </>
  );
};
