import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import { CashierCheckoutListing } from './CashierCheckoutListing';

export default {
  title: 'Molecules/CashierCheckoutListing',
  component: CashierCheckoutListing,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof CashierCheckoutListing>;

export const Default: Story = {
  args: {},
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <CashierCheckoutListing {...args} />
    </Box>
  ),
};
