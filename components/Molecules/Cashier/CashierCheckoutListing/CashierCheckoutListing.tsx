import { Icon<PERSON><PERSON>on, Stack } from '@mui/material';
import dayjs from 'dayjs';
import React from 'react';
import Icon from '@/components/Atoms/Icon';
import { TCustomer } from '@/lib/types/entities/customer';
import { TInvoice } from '@/lib/types/entities/invoice';
import { TOrder } from '@/lib/types/entities/order';
import { concatenateNames } from '@/lib/utils/string';
import { StyledAvatar } from '../../AppointmentCalendar/AppointmentEventDetail/AppointmentEventDetail.styled';
import { CheckoutInvoiceListing } from '../CheckoutInvoiceListing/CheckoutInvoiceListing';
import {
  StyledFooterButton,
  StyledButtonWrapper,
  StyledCardHeader,
  StyledContainer,
  StyledCustomerName,
} from './CashierCheckoutListing.styled';
import Typography from '@/components/Atoms/Typography';
import { formatLockerNumber } from '@/lib/constants/utils';
import { customPalettes } from '@/theme/customPalettes';

export type TCashierCheckoutListing = {
  customer?: TCustomer;
  invoices?: TInvoice[];
  orders?: TOrder[];
  rfid?: string;
  checkInTime?: string;
  onCloseModal: () => void;
  onNextStep: () => void;
  lockerNumber: number;
  onPayment: (values: { orderIds: string[]; invoiceIds?: string[] }) => void;
  onCheckout: () => void;
  onContinueOrder: () => void;
};

export const CashierCheckoutListing: React.FC<TCashierCheckoutListing> = ({
  customer,
  invoices = [],
  orders = [],
  checkInTime,
  onCloseModal,
  lockerNumber,
  onPayment,
  onCheckout,
  onContinueOrder,
}) => {
  const customerName = concatenateNames(customer?.firstName || '', customer?.lastName || '');
  const isEmptyOrder = orders?.length === 0 && invoices?.length === 0;

  return (
    <>
      <StyledContainer>
        <StyledCardHeader>
          <StyledAvatar src={customer?.avatar?.url}>{customerName?.[0]}</StyledAvatar>
          <Stack gap="4px">
            <StyledCustomerName variant="heading-xmedium-700">{customerName}</StyledCustomerName>

            <Stack flexDirection="row" gap={1.5}>
              <Typography variant="heading-small-700" minWidth="100px">
                Gender
              </Typography>
              <Typography variant="body-large-400">{`${customer?.gender?.name || ''}`}</Typography>
            </Stack>

            <Stack justifyContent="space-between" alignItems="center" flexDirection="row" gap={1.5}>
              <Typography variant="heading-small-700" minWidth="100px">
                Locker number
              </Typography>
              <Typography variant="body-large-400">{formatLockerNumber.format(lockerNumber)}</Typography>
              <Typography variant="body-large-400" minWidth="50px" color="#B1A7A1">
                {`${dayjs(checkInTime).format('HH:mm A')}`}
              </Typography>
            </Stack>
          </Stack>
          <Stack sx={{ flexGrow: 1, justifyContent: 'flex-end', alignItems: 'flex-end' }}>
            <IconButton onClick={() => onCloseModal()}>
              <Icon size={3} variant="close" />
            </IconButton>
          </Stack>
        </StyledCardHeader>
        <CheckoutInvoiceListing onCheckout={onCheckout} invoices={invoices} orders={orders} onPayment={onPayment} />
      </StyledContainer>
      <StyledButtonWrapper>
        <StyledFooterButton
          variant="outlined"
          label="Cancel"
          size="large"
          loadingText="Cancel"
          onClick={() => onCloseModal()}
        />
        <StyledFooterButton
          variant="contained"
          type="submit"
          label="Continue to order"
          size="large"
          onClick={() => onContinueOrder()}
        />

        {isEmptyOrder && (
          <StyledFooterButton
            sx={{
              '&&&': {
                border: `1px solid ${customPalettes.neutrals?.N220.main}`,
                background: customPalettes.neutrals?.N220.main,
              },
            }}
            variant="contained"
            label="Checkout"
            size="large"
            onClick={onCheckout}
          />
        )}
      </StyledButtonWrapper>
    </>
  );
};
