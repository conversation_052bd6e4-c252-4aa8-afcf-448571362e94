import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Avatar, { avatarClasses } from '@mui/material/Avatar';
import Button from '@/components/Atoms/Button';
import Typography from '@/components/Atoms/Typography';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  borderRadius: theme.spacing(1.25),
  width: theme.spacing(748 / 8),
  gap: theme.spacing(3),
}));

export const StyledCardHeader = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(2.25),
  alignItems: 'flex-start',
  justifyContent: 'space-between',
}));

export const StyledCustomerName = styled(Typography)(({ theme }) => ({
  color: theme.palette.neutrals.N50.main,
  textTransform: 'uppercase',
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  color: `${theme.palette.neutrals.N50.main} !important`,
  backgroundColor: 'white !important',
  border: 'none !important',
}));

export const StyledQRContainer = styled(Stack)(({ theme }) => ({
  paddingTop: theme.spacing(6),
  gap: theme.spacing(1.5),
  justifyContent: 'center',
  alignItems: 'center',
  paddingInline: theme.spacing(4),
}));
export const StyledAvatar = styled(Avatar)(({ theme }) => ({
  [`&.${avatarClasses.root}`]: {
    width: theme.spacing(8),
    height: theme.spacing(8),
    textTransform: 'uppercase',
    color: theme.palette.common.white,
    backgroundColor: theme.palette.neutrals.N50.main,
    ...theme.typography['heading-xxlarge-700'],
  },
}));

export const StyledButtonWrapper = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  spacing: theme.spacing(2),
  justifyContent: 'center',
  padding: theme.spacing(3),
  paddingBottom: 0,
  gap: theme.spacing(1.5),
  alignItems: 'center',
}));

export const StyledFooterButton = styled(Button)(({ theme }) => ({
  maxWidth: theme.spacing(200 / 8),
}));
