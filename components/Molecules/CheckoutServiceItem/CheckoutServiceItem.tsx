import { IconButton } from '@mui/material';
import Stack from '@mui/material/Stack';
import React, { useState } from 'react';
import ConfirmModal from '@/components/Atoms/ConfirmModal';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import { TOrderItem } from '@/lib/types/entities/order';
import { Price } from '../Price/Price';
import { StyledContainer } from './CheckoutServiceItem.styled';

type TCheckoutServiceItem = {
  orderItem: TOrderItem;
  order: number;
  onRemoveSerivceItem: (id: string) => void;
};

export const CheckoutServiceItem: React.FC<TCheckoutServiceItem> = ({ orderItem, order, onRemoveSerivceItem }) => {
  const [open, setOpen] = useState<boolean>(false);
  const onDelete = async (id: string) => {
    setOpen(true);
  };

  const onConfirmDelete = async () => {
    onRemoveSerivceItem(orderItem?.id || '');
  };

  const onCancelDelete = () => {
    setOpen(false);
  };
  return (
    <>
      <StyledContainer>
        <Typography variant="heading-large-700" width="40px" overflow="hidden">
          {order}
        </Typography>
        <Stack gap={1.5} flex={1}>
          <Stack width="100%" flexDirection="row">
            <Stack flex="1 1 40%" gap={0.5}>
              <Typography variant="heading-medium-700">{orderItem?.product?.name}</Typography>
              <Typography variant="body-xlarge-400">{orderItem?.duration?.name}</Typography>
            </Stack>
            <Stack flex="1 1 20%" gap={0.5}>
              <Typography variant="heading-medium-700">Employee</Typography>
              <Typography variant="body-xlarge-400">
                {orderItem?.employees?.map(e => e?.displayName)?.join(' - ')}
              </Typography>
            </Stack>
            <Stack flex="1 1 20%" gap={0.5}>
              <Typography variant="heading-medium-700">Price</Typography>
              <Price amount={orderItem?.product?.price} typographyProps={{ variant: 'heading-medium-700' }} />
            </Stack>
            <IconButton
              onClick={() => setOpen(true)}
              sx={theme => ({ position: 'absolute', right: theme.spacing(1.5), top: theme.spacing(1.5), padding: 0 })}
            >
              <Icon variant="close" />
            </IconButton>
          </Stack>
          {!!orderItem?.note && (
            <Stack flex="1 1 40%" gap={0.5}>
              <Typography variant="heading-medium-700">Notes</Typography>
              <Typography variant="body-xlarge-400">{orderItem?.note}</Typography>
            </Stack>
          )}
        </Stack>
      </StyledContainer>
      <ConfirmModal
        isOpen={open}
        title="Confirm delete item"
        bodyContent="Do you want to delete this item?"
        onCancel={() => onCancelDelete()}
        onClose={() => onCancelDelete()}
        onConfirm={() => onConfirmDelete()}
      />
    </>
  );
};
