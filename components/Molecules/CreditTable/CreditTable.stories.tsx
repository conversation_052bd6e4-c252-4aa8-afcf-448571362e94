import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import dayjs from 'dayjs';
import { concatenateNames } from '@/lib/utils/string';
import { TCredit } from '@/lib/types/entities/credit';
import CreditTable, { ICreditColumn } from '.';
import Price from '../Price';

export default {
  title: 'Molecules/CreditTable',
  component: CreditTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof CreditTable>;

const SAMPLE_ROWS: TCredit[] = [];
const SAMPLE_COLUMNS: ICreditColumn[] = [
  {
    name: 'customer',
    label: 'CUSTOMER',
    render: (row: TCredit) => concatenateNames(row?.customer?.firstName || '', row?.customer?.lastName || ''),
  },
  {
    name: 'customer',
    label: 'PHONE',
    render: (row: TCredit) => row?.customer?.phone,
  },
  {
    name: 'issueDate',
    label: 'ISSUE DATE',
  },
  {
    name: 'expiryDate',
    label: 'EXPIRY DATE',
    render: (row: TCredit) => dayjs(row?.expiryDate).format('DD MMM, YYYY'),
  },
  {
    name: 'total',
    label: 'CREDIT',
  },
  {
    name: 'creditBalance',
    label: 'USED',
    render: (row: TCredit) => (
      <Price amount={(row?.total || 0) - (row?.creditBalance || 0)} typographyProps={{ variant: 'body-xlarge-400' }} />
    ),
  },
  // {
  //   name: 'status',
  //   label: 'STATUS',
  // },
];

export const Default: Story = {
  args: { rows: SAMPLE_ROWS, columns: SAMPLE_COLUMNS },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <CreditTable {...args} />
    </Box>
  ),
};
