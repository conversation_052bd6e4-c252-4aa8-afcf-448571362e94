import styled from '@emotion/styled';
import { Stack } from '@mui/material';
import Typography from '@/components/Atoms/Typography';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'column',
  gap: theme.spacing(3),
}));

export const StyledHeaderContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  flexWrap: 'nowrap',
  gap: theme.spacing(5),
}));

export const StyledInfoContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(1.5),
  alignItems: 'center',
}));

export const StyledBackToList = styled(Stack)(({ theme }) => ({
  color: theme.palette.info.main,
}));

export const StyledTotalItem = styled(Typography)(({ theme }) => ({
  color: theme.palette.neutrals.N170.main,
}));
