import { Divider, IconButton } from '@mui/material';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import Icon, { IconVariantTypes } from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import { TAX } from '@/lib/constants/tax';
import { TPaymentState } from '@/lib/hooks/utils/usePaymentCaculator';
import { TAppointment } from '@/lib/types/entities/appointment';
import { TOrderItem } from '@/lib/types/entities/order';
import { customPalettes } from '@/theme/customPalettes';
import { CheckoutServiceItem } from '../../CheckoutServiceItem/CheckoutServiceItem';
import { Price } from '../../Price/Price';
import {
  StyledBackToList,
  StyledContainer,
  StyledHeaderContainer,
  StyledInfoContainer,
  StyledTotalItem,
} from './DesktopCheckoutItems.styled';
import { TCouponCheckRes } from '@/lib/types/entities/coupon';
import { ECouponType } from '@/lib/types/enum/coupon';

type TDesktopCheckoutItems = {
  appointment?: TAppointment;
  subTotal: number;
  taxCalculated: number;
  balance: number;
  paymentState?: TPaymentState;
  onRemovePaymentMethod: (value: string) => void;
  onRemoveSerivceItem: (id: string) => void;
  onBackToList: () => void;
  onRemoveCoupon: (type: ECouponType, couponCode?: string) => void;
  discounts: TCouponCheckRes[];
};

export const DesktopCheckoutItems: React.FC<TDesktopCheckoutItems> = ({
  appointment,
  onRemoveSerivceItem,
  onBackToList,
  subTotal,
  taxCalculated,
  balance,
  paymentState,
  onRemovePaymentMethod,
  discounts = [],
  onRemoveCoupon,
}) => {
  const showHeader: { icon: IconVariantTypes; label: string }[] = [
    { icon: 'calendar_black', label: dayjs(appointment?.startTime).format('ddd D MMM, YYYY') },
    { icon: 'clock', label: dayjs(appointment?.startTime).format('HH:mm') },
    { icon: 'location', label: appointment?.branch?.address || 'N/A' },
  ];

  const serviceListing = useMemo(() => {
    const result = appointment?.orders?.reduce((pre, cur) => [...pre, ...(cur?.items || [])], [] as TOrderItem[]);
    return result;
  }, [appointment]);

  const { ShowPaymentMethods }: { ShowPaymentMethods: React.ReactNode } = useMemo(() => {
    const component = paymentState?.map(item => (
      <Stack flexDirection="row" key={item?.paymentMethod?.id} justifyContent="space-between">
        <Typography variant="heading-medium-700">{`${item?.paymentMethod?.name}`}</Typography>
        <Stack flexDirection="row" alignItems="center">
          <Price amount={item?.paid} typographyProps={{ variant: 'heading-medium-700' }} />
          <IconButton
            sx={{ width: '12px', height: '12px' }}
            onClick={() => onRemovePaymentMethod(item?.paymentMethod?.id)}
          >
            <Icon variant="close" />
          </IconButton>
        </Stack>
      </Stack>
    ));
    return { ShowPaymentMethods: component };
  }, [paymentState]);

  return (
    <StyledContainer>
      <StyledHeaderContainer>
        {showHeader?.map(({ icon, label }) => (
          <StyledInfoContainer key={icon}>
            <Icon variant={icon} />
            <Typography variant="heading-medium-700">{label}</Typography>
          </StyledInfoContainer>
        ))}
      </StyledHeaderContainer>
      <Stack gap={3}>
        {serviceListing?.map((service, index) => (
          <CheckoutServiceItem
            key={service?.id}
            order={index + 1}
            orderItem={service}
            onRemoveSerivceItem={onRemoveSerivceItem}
          />
        ))}
      </Stack>
      <Stack gap={2} flexDirection="row" alignItems="flex-start">
        <StyledBackToList
          flexDirection="row"
          gap={1}
          flex="1 1 40%"
          alignItems="center"
          sx={{ cursor: 'pointer' }}
          onClick={onBackToList}
        >
          <Icon variant="arrow_left_blue" />
          <Typography variant="heading-medium-700">Back to list</Typography>
        </StyledBackToList>
        <Stack flex="1 1 40%" gap={1.5}>
          <Stack flexDirection="row" justifyContent="space-between">
            <StyledTotalItem variant="heading-medium-700">Subtotal</StyledTotalItem>
            <Price amount={subTotal} typographyProps={{ variant: 'heading-medium-700' }} />
          </Stack>
          {discounts?.map(d => (
            <Stack key={`${d?.couponType}-${d.discountMoney}`} flexDirection="row" justifyContent="space-between">
              <StyledTotalItem variant="heading-medium-700">
                {d.couponType === ECouponType.ID_CODE ? d.couponName : d.couponType}
              </StyledTotalItem>

              <Stack direction="row" alignItems="center">
                <Price
                  amount={Number(d?.discountMoney || 0) * -1}
                  typographyProps={{ variant: 'heading-medium-700' }}
                />
                <Stack sx={{ flexGrow: 1, justifyContent: 'flex-end', alignItems: 'flex-end', cursor: 'pointer' }}>
                  <Icon variant="close" onClick={() => onRemoveCoupon(d.couponType, d.couponCode)} />
                </Stack>
              </Stack>
            </Stack>
          ))}
          <Stack flexDirection="row" justifyContent="space-between">
            <StyledTotalItem variant="heading-medium-700">Tax({TAX}%)</StyledTotalItem>

            <Price
              amount={taxCalculated}
              typographyProps={{ variant: 'heading-medium-700', color: customPalettes?.palette?.neutrals?.N170?.main }}
            />
          </Stack>

          {ShowPaymentMethods}
          <Divider />
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="heading-large-700">Balance</Typography>
            <Price amount={balance} typographyProps={{ variant: 'heading-large-700' }} />
          </Stack>
        </Stack>
      </Stack>
    </StyledContainer>
  );
};
