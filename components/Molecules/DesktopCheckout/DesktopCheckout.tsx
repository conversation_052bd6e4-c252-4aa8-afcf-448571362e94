'use client';

import { Stack } from '@mui/material';
import dayjs from 'dayjs';
import isEmpty from 'lodash/isEmpty';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import { CustomerModalTemplate } from '@/components/Templates/CustomerModalTemplate';
import { PATH_NAME } from '@/lib/constants/pathname';
import { TAX } from '@/lib/constants/tax';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { useMutationInvoiceCreate } from '@/lib/hooks/mutation/invoice';
import { useMutationOrderDetailDelete } from '@/lib/hooks/mutation/orderDetail';
import { useAppointmentById } from '@/lib/hooks/queries/appointment';
import { useCustomerById } from '@/lib/hooks/queries/customer';
import { useCouponApply } from '@/lib/hooks/utils/useCouponApply';
import { usePaymentCaculator } from '@/lib/hooks/utils/usePaymentCaculator';
import { checkCouponCode } from '@/lib/services/coupon';
import { TCouponCheckRes } from '@/lib/types/entities/coupon';
import { TOrder, TOrderItem } from '@/lib/types/entities/order';
import { TPaymentMethod } from '@/lib/types/entities/paymentMethod';
import { EAppointmentRoute } from '@/lib/types/enum/appointment';
import { ECouponType } from '@/lib/types/enum/coupon';
import { EInvoiceStatus } from '@/lib/types/enum/invoice';
import { ECreditMethods } from '@/lib/types/enum/paymentMethod';
import { getCredits } from '@/lib/utils/credit';
import { TApiError } from '@/lib/utils/transformResponse';
import SelectCouponModal from '../SelectCouponModal';
import { StyledContainer } from './DesktopCheckout.styled';
import { DesktopCheckoutItems } from './DesktopCheckoutItems/DesktopCheckoutItems';
import { DesktopCheckoutResult } from './DesktopCheckoutResult/DesktopCheckoutResult';
import { EOrderType } from '@/lib/types/enum/order';

type TDesktopCheckout = {
  appointmentId: string;
  callbackUrl: string;
  isOpen: boolean;
};

export type TCouponApplied = {
  couponCode: string;
  discountProductId: string;
  discountMoney: number;
  couponName: string;
};

export type TCheckoutType = 'unpaid' | 'part_paid' | 'paid';

export const DesktopCheckout: React.FC<TDesktopCheckout> = ({ callbackUrl, appointmentId, isOpen = true }) => {
  const router = useRouter();

  const { data: appointment, mutate } = useAppointmentById(appointmentId);
  const { data: customerInfo } = useCustomerById(appointment?.customer?.id || '');
  const { onDeleteOrderDetail } = useMutationOrderDetailDelete();
  const { onCreateInvoice, isMutating: isCreatingInvoice } = useMutationInvoiceCreate();

  const { showError, showSuccess } = useAlert();

  const { paymentMethods, onAddPaymentMethod, onRemovePaymentMethod, totalPayment } = usePaymentCaculator();

  const [isOpenModalDiscount, setOpenModalDiscount] = useState<boolean>(false);
  const [couponError, setCouponError] = useState<string>();

  const { voucherChecked, onRemoveVoucher, setVoucherChecked, totalDiscountValue } = useCouponApply();

  const onApplyCoupon = async (couponCodes: { couponCode: string; couponType: ECouponType }[]) => {
    setCouponError(undefined);
    const orderIds = appointment?.orders?.map(i => ({ id: i.id || '' })) || [];
    if (isEmpty(orderIds)) return showError({ title: 'Empty Orders' });

    // const isCouponTypeExist = voucherChecked.voucherChoose.some(v => v.couponType === couponType);
    // const newValuesCouponChoose = isCouponTypeExist
    //   ? voucherChecked.voucherChoose.map(v => {
    //       if (v.couponType === couponType)
    //         return {
    //           ...v,
    //           couponCode,
    //         };
    //       return v;
    //     })
    //   : [...voucherChecked.voucherChoose, { couponCode, couponType }];

    const newValuesCouponChoose = [...voucherChecked.voucherChoose, ...couponCodes];

    const couponCheckList = newValuesCouponChoose?.map(voucher => ({
      type: voucher.couponType,
      value: Number(voucher.couponCode),
      code: voucher.couponCode,
    }));
    const response = await checkCouponCode<TCouponCheckRes[]>({ orders: orderIds, coupons: couponCheckList });
    if (response.status === 'error') {
      if (typeof response?.message?.error === 'string') {
        return setCouponError((response?.message?.error || 'Coupon code is invalid') as string);
      }
      if (Array.isArray(response.message.error)) {
        return setCouponError('Coupon code is invalid');
      }
      const apiError = response.message.error as TApiError;
      return setCouponError((apiError?.message || 'Coupon code is invalid') as string);
    }
    setVoucherChecked({ voucherChoose: newValuesCouponChoose, voucherResponse: response.data });
    setOpenModalDiscount(false);
  };

  const subTotal =
    appointment?.orders
      ?.reduce((pre, cur) => [...pre, ...(cur?.items || [])], [] as TOrderItem[])
      .map(item => Number(item?.product?.price))
      ?.reduce((accumulator, currentValue) => accumulator + currentValue, 0) || 0;

  const taxCalculated: number | string = useMemo(
    () => ((subTotal - (totalDiscountValue || 0)) / 100) * TAX,
    [subTotal, totalDiscountValue]
  );

  const balanceCaculated = subTotal - totalPayment + taxCalculated - (totalDiscountValue || 0);

  const balance = balanceCaculated < 0 ? 0 : balanceCaculated;

  const { remainNewCreditAmount, remainOldCreditAmount, newCredit, oldCredit } = useMemo(() => {
    const { newCredit, oldCredit } = getCredits(customerInfo?.credits || []);
    const remainNewCreditAmount: number =
      Number(newCredit?.balance || 0) -
      Number(
        paymentMethods
          ?.filter(({ paymentMethod }) => paymentMethod?.code === ECreditMethods.NEW_CREDIT)
          ?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0)
      );
    const remainOldCreditAmount: number =
      Number(oldCredit?.balance || 0) -
      Number(
        paymentMethods
          ?.filter(({ paymentMethod }) => paymentMethod?.code === ECreditMethods.OLD_CREDIT)
          ?.reduce((pre, cur) => pre + (cur?.paid ?? 0), 0)
      );

    return {
      remainNewCreditAmount,
      remainOldCreditAmount,
      newCredit,
      oldCredit,
    };
  }, [JSON.stringify(customerInfo?.credits), JSON.stringify(paymentMethods)]);

  const onBack = () => {
    router.push(`${EAppointmentRoute.HOME}?type=detail&appointment_id=${appointmentId}&modalType=edit`);
  };

  const onRemoveSerivceItem = async (id: string) => {
    const response = await onDeleteOrderDetail(id);
    if (response.status === 'error') return showError({ title: 'Error' });
    mutate();
    showSuccess({ title: 'Deleted!' });
  };

  const onSave = async (status: EInvoiceStatus, note: string) => {
    if (!appointment?.orders || appointment?.orders?.length === 0) return showError({ title: 'Empty orders!' });
    const orderIds = appointment?.orders?.map<TOrder>(o => ({ id: o?.id || '', orderType: EOrderType.OTHERS })) || [];

    const response = await onCreateInvoice({
      orders: orderIds,
      status,
      appointment: { id: appointmentId },
      invoicePayments: paymentMethods,
      discounts: voucherChecked.voucherResponse,
      note,
      tax: TAX,
    });
    if (response.status === 'error') {
      if (typeof response?.message?.error === 'string') {
        return showError({ title: response?.message?.error || 'Created failed' });
      }
      if (Array.isArray(response.message.error)) {
        return showError({ title: 'Created failed' });
      }
      // It's a TApiError
      const apiError = response.message.error as TApiError;
      return showError({ title: apiError?.message });
    }
    showSuccess({ title: 'Checkout success !!!' });

    router.push(PATH_NAME.INVOICE.detail({ id: response?.data?.id || '', tab: 'invoice-list' }));
  };

  const onPay = (method: TPaymentMethod, amount: number) => {
    switch (method.code) {
      case ECreditMethods.NEW_CREDIT:
        if (remainNewCreditAmount < Number(amount)) {
          showError({ title: 'Not enough credit!' });
          return;
        }
        break;
      case ECreditMethods.OLD_CREDIT:
        if (remainOldCreditAmount < Number(amount)) {
          showError({ title: 'Not enough old credit!' });
          return;
        }
        break;
      default:
        break;
    }
    const newValue = Number(balance) > Number(amount) ? Number(amount) : Number(balance);
    onAddPaymentMethod({ method, paid: newValue });
  };

  return (
    <CustomerModalTemplate isOpen handleClose={onBack} title="CHECK OUT">
      <StyledContainer height="100%">
        <Stack p={3} flex="1 1 70%" overflow="auto">
          <DesktopCheckoutItems
            discounts={voucherChecked.voucherResponse}
            paymentState={paymentMethods}
            appointment={appointment}
            onRemoveSerivceItem={onRemoveSerivceItem}
            onBackToList={onBack}
            subTotal={subTotal}
            balance={balance}
            taxCalculated={taxCalculated}
            onRemovePaymentMethod={onRemovePaymentMethod}
            onRemoveCoupon={onRemoveVoucher}
          />
        </Stack>
        <Stack flex="1 1 30%">
          <DesktopCheckoutResult
            onPay={onPay}
            onSave={onSave}
            customerInfo={appointment?.customer}
            balance={balance}
            totalBill={subTotal + taxCalculated}
            isSaving={isCreatingInvoice}
            newCreditAmount={remainNewCreditAmount}
            oldCreditAmount={remainOldCreditAmount}
            newCreditExpiry={
              dayjs(newCredit?.expiredDate || '').isValid() ? dayjs(newCredit?.expiredDate).format('MM/DD/YYYY') : ''
            }
            oldCreditExpiry={
              dayjs(oldCredit?.expiredDate || '').isValid() ? dayjs(oldCredit?.expiredDate).format('MM/DD/YYYY') : ''
            }
            openModalDiscount={() => setOpenModalDiscount(true)}
          />
        </Stack>
      </StyledContainer>
      <SelectCouponModal
        isOpen={isOpenModalDiscount}
        usedCoupons={voucherChecked?.voucherChoose?.map(voucher => voucher?.couponCode || '') || []}
        onClose={() => setOpenModalDiscount(false)}
        onApply={onApplyCoupon}
        errorText={couponError}
        setCouponError={setCouponError}
        customerId={appointment?.customer?.id || ''}
      />
    </CustomerModalTemplate>
  );
};
