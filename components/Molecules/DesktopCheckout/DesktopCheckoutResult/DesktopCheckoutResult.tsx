import { dialogClasses } from '@mui/material/Dialog';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import InputAdornment from '@mui/material/InputAdornment';
import Stack from '@mui/material/Stack';
import { useMemo, useState } from 'react';
import { CondOperator } from '@nestjsx/crud-request';
import Button from '@/components/Atoms/Button';
import Icon from '@/components/Atoms/Icon';
import Modal from '@/components/Atoms/Modal';
import Typography from '@/components/Atoms/Typography';
import { usePaymentMethods } from '@/lib/hooks/queries/paymentMethod';
import { TCustomer } from '@/lib/types/entities/customer';
import { TPaymentMethod } from '@/lib/types/entities/paymentMethod';
import { EInvoiceStatus } from '@/lib/types/enum/invoice';
import { concatenateNames, round2decimal } from '@/lib/utils/string';
import { customPalettes } from '@/theme/customPalettes';
import Price from '../../Price';
import {
  StyledAvatar,
  StyledButton,
  StyledCustomerInfo,
  StyledCustomerInfoContainer,
  StyledInPut,
  StyledTitle,
} from './DesktopCheckoutResult.styled';
import { InvoiceDetailForm } from './InvoiceDetailForm/InvoiceDetailForm';
import { useSetting } from '@/lib/hooks/queries/setting';

type TDesktopCheckoutResult = {
  customerInfo?: Partial<TCustomer>;
  newCreditAmount: number;
  oldCreditAmount: number;
  newCreditExpiry: string;
  oldCreditExpiry: string;
  totalBill: number;
  balance: number;
  isSaving: boolean;
  onPay: (paymentMethod: TPaymentMethod, amount: number) => void;
  openModalDiscount: () => void;
  onSave: (status: EInvoiceStatus, note: string) => void;
};

export const DesktopCheckoutResult: React.FC<TDesktopCheckoutResult> = ({
  customerInfo,
  balance,
  totalBill,
  newCreditAmount,
  oldCreditAmount,
  newCreditExpiry,
  oldCreditExpiry,
  isSaving,
  onPay,
  onSave,
  openModalDiscount,
}) => {
  const [payAmount, setPayAmount] = useState<number>(0);
  const [isOpenInvoiceDetailModal, setIsOpenInvoiceDetailModal] = useState<boolean>(false);
  const [showPayment, setShowPayment] = useState<boolean>(true);
  const [note, setNote] = useState<string>('');
  const { data: OPTION_BY_KEY } = useSetting(['status']);
  const activeStatus = OPTION_BY_KEY?.['status']?.find(option => option?.name === 'Active');
  const { data: TOTAL_PAYMENT_METHOD } = usePaymentMethods({
    query: {
      filterOptions: activeStatus?.id
        ? [
            {
              field: 'status.id',
              value: activeStatus?.id || '',
              operator: CondOperator['EQUALS'],
            },
          ]
        : [],
    },
  });

  const status: EInvoiceStatus = useMemo(() => {
    if (Math.round(balance) === 0) return EInvoiceStatus['PAID'];
    return EInvoiceStatus.PART_PAID;
  }, [balance]);

  const saveTitle = status === EInvoiceStatus['PAID'] ? 'Save paid' : 'Save part paid';

  return (
    <>
      <Stack p={3} bgcolor="white" gap={3} height="calc(100vh - 50px)" position="relative">
        <StyledCustomerInfoContainer>
          <StyledAvatar src={customerInfo?.avatar?.url}>{customerInfo?.firstName?.[0]?.toUpperCase()}</StyledAvatar>
          <Stack gap={1.5} width="100%">
            <Stack gap={0.5}>
              <Typography variant="heading-xmedium-700" textTransform="uppercase">
                {concatenateNames(customerInfo?.firstName || '', customerInfo?.lastName || '')}
              </Typography>
              <StyledCustomerInfo>
                <StyledTitle variant="heading-small-700" minWidth="50px">
                  Phone
                </StyledTitle>
                <StyledTitle variant="heading-small-700">{customerInfo?.phone || ''}</StyledTitle>
              </StyledCustomerInfo>
              <StyledCustomerInfo>
                <StyledTitle variant="heading-small-700" minWidth="50px">
                  Verification
                </StyledTitle>
                <StyledTitle variant="heading-small-700">(NRIC) {customerInfo?.nric || ''}</StyledTitle>
              </StyledCustomerInfo>
            </Stack>
            <Divider
              sx={theme => ({
                borderBottomWidth: theme.spacing(1 / 16),
                borderColor: theme.palette.neutrals.N180.main,
              })}
            />
            <Stack gap={0.5}>
              <StyledCustomerInfo>
                <StyledTitle variant="heading-small-700" minWidth="50px">
                  Credit
                </StyledTitle>
                <StyledTitle variant="heading-small-700">
                  <StyledTitle variant="heading-small-700">{`$ ${round2decimal(newCreditAmount)}`}</StyledTitle>
                </StyledTitle>
              </StyledCustomerInfo>
              <StyledCustomerInfo>
                <StyledTitle variant="heading-small-700" minWidth="50px">
                  Expiry
                </StyledTitle>
                <StyledTitle variant="heading-small-700">{newCreditExpiry}</StyledTitle>
              </StyledCustomerInfo>
            </Stack>
            <Stack gap={0.5}>
              <StyledCustomerInfo>
                <StyledTitle variant="heading-small-700" minWidth="50px">
                  Old Credit
                </StyledTitle>
                <StyledTitle variant="heading-small-700">
                  <StyledTitle variant="heading-small-700">{`$ ${round2decimal(oldCreditAmount)}`}</StyledTitle>
                </StyledTitle>
              </StyledCustomerInfo>
              <StyledCustomerInfo>
                <StyledTitle variant="heading-small-700" minWidth="50px">
                  Expiry
                </StyledTitle>
                <StyledTitle variant="heading-small-700">{oldCreditExpiry}</StyledTitle>
              </StyledCustomerInfo>
            </Stack>
          </Stack>
        </StyledCustomerInfoContainer>
        <Divider
          sx={theme => ({
            borderBottomWidth: theme.spacing(1 / 16),
            borderColor: theme.palette.neutrals.N180.main,
          })}
        />
        {showPayment ? (
          <>
            <Stack bgcolor="white" gap={1.5}>
              <Typography textAlign="center" variant="body-xxlarge-400">
                Pay
              </Typography>
              <StyledInPut
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Typography variant="heading-xxlarge-700">$</Typography>
                    </InputAdornment>
                  ),
                }}
                type="number"
                value={payAmount}
                onChange={e => setPayAmount(Number(e?.target?.value))}
              />
            </Stack>
            <Grid
              container
              spacing={1.5}
              height="calc(100vh - 580px)"
              overflow="auto"
              sx={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
              }}
            >
              {TOTAL_PAYMENT_METHOD?.map(method => (
                <Grid item md={4}>
                  <Stack
                    key={method?.id}
                    onClick={() => onPay(method, payAmount)}
                    sx={theme => ({
                      width: '100%',
                      height: '100%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      flexDirection: 'row',
                      padding: theme.spacing(1.5),
                      background: theme.palette.neutrals.N10.main,
                      borderRadius: theme.spacing(1.25),
                      cursor: 'pointer',
                    })}
                  >
                    <Typography component="p" variant="heading-large-700" textAlign="center" textTransform="capitalize">
                      {method?.name || ''}
                    </Typography>
                  </Stack>
                </Grid>
              ))}
            </Grid>
          </>
        ) : (
          <>
            {balance > 0 ? (
              <Stack width="100%" alignItems="center" gap={3} height="calc(100vh - 590px)" justifyContent="center">
                <Icon variant="invoice" size={8} />
                <Typography
                  component="p"
                  variant="body-xlarge-400"
                  color={customPalettes?.neutrals?.N50?.main}
                  textAlign="center"
                >
                  You still have an outstanding balance of
                  <Price amount={balance} typographyProps={{ variant: 'body-xlarge-400' }} />
                </Typography>
              </Stack>
            ) : (
              <Stack width="100%" alignItems="center" gap={3} height="calc(100vh - 590px)" justifyContent="center">
                <Icon variant="invoice" size={8} />
                <Typography
                  maxWidth="288px"
                  variant="body-xlarge-400"
                  textAlign="center"
                  color={customPalettes?.neutrals?.N50?.main}
                >
                  Full payment has been added
                </Typography>
              </Stack>
            )}
          </>
        )}
        <Stack
          sx={theme => ({
            width: '100%',
            position: 'absolute',
            left: 0,
            bottom: 0,
            padding: theme.spacing(3, 2.25),
            gap: theme.spacing(1.5),
            borderTop: `1px solid ${theme.palette.neutrals.N180.main}`,
            marginTop: theme.spacing(3),
          })}
        >
          {showPayment ? (
            <>
              <StyledButton
                label="Apply Discounts"
                variant="contained"
                onClick={openModalDiscount}
                disabled={isSaving}
              />
              <Stack flexDirection="row" gap={1.25}>
                <StyledButton
                  label="Invoice Details"
                  variant="contained"
                  onClick={() => setIsOpenInvoiceDetailModal(true)}
                  disabled={isSaving}
                />
                <StyledButton
                  label={saveTitle}
                  variant="contained"
                  isLoading={isSaving}
                  loadingText={saveTitle.replace('ave', 'aving')}
                  disabled={balance === totalBill}
                  onClick={() => {
                    setShowPayment(false);
                  }}
                />
              </Stack>
            </>
          ) : (
            <Stack flexDirection="row" gap={1.25}>
              <Button
                label="Back to payment"
                variant="outlined"
                onClick={() => setShowPayment(true)}
                disabled={isSaving}
              />
              <Button
                label="Complete sale"
                variant="contained"
                isLoading={isSaving}
                loadingText="Completing sale"
                onClick={() => {
                  onSave(status, note);
                  setPayAmount(0);
                }}
              />
            </Stack>
          )}
        </Stack>
      </Stack>
      <Modal
        isOpen={isOpenInvoiceDetailModal}
        handleClose={() => setIsOpenInvoiceDetailModal(false)}
        title="INVOICE DETAILS"
        titleProps={{
          sx: {
            margin: '0 auto',
          },
        }}
        dialogProps={{
          sx: theme => ({
            [`& .${dialogClasses.paper}`]: {
              height: 'fit-content',
              padding: theme.spacing(3),
              margin: theme.spacing(3),
              maxHeight: 'calc(100% - 48px)',
              borderRadius: theme.spacing(1.25),
              backgroundColor: theme.palette.neutrals.N100.main,
              [theme.breakpoints.down('md')]: {
                padding: theme.spacing(2),
              },
            },
          }),
        }}
      >
        <InvoiceDetailForm
          note={note}
          onCancel={() => setIsOpenInvoiceDetailModal(false)}
          onSave={(value: string) => {
            setNote(value);
            setIsOpenInvoiceDetailModal(false);
          }}
        />
      </Modal>
    </>
  );
};
