'use client';

import Stack from '@mui/material/Stack';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import RHFTextField from '@/components/Molecules/RHFItems/RHFTextField';
import { Styled<PERSON><PERSON>on, StyledB<PERSON>onWrapper, StyledContainer, StyledFormContainer } from './InvoiceDetailForm.styled';

type TFormValues = { note: string };

export type TInvoiceDetailForm = {
  note: string;
  onSave: (note: string) => void;
  onCancel: () => void;
};

export const InvoiceDetailForm: React.FC<TInvoiceDetailForm> = ({ note, onSave, onCancel }) => {
  const initValues = {
    note,
  };

  const methods = useForm<TFormValues>({
    defaultValues: initValues,
  });

  const { reset } = methods;

  const handleSubmit = (values: TFormValues) => {
    onSave(values?.note || '');
  };

  const handleCancel = () => {
    if (onCancel) onCancel();
  };

  useEffect(() => {
    reset(initValues);
    return () => reset({});
  }, [note]);

  return (
    <StyledContainer>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handleSubmit)}>
          <Stack width="100%">
            <StyledFormContainer>
              <RHFTextField
                name="note"
                multiline
                rows={3}
                label="Note"
                formControlProps={{
                  sx: {
                    width: '100%',
                  },
                }}
              />
            </StyledFormContainer>

            <StyledButtonWrapper>
              <StyledButton
                variant="outlined"
                label="Cancel"
                size="large"
                loadingText="Cancel"
                onClick={handleCancel}
              />
              <StyledButton variant="contained" type="submit" label="Save" size="large" />
            </StyledButtonWrapper>
          </Stack>
        </form>
      </FormProvider>
    </StyledContainer>
  );
};
