import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Button from '@/components/Atoms/Button';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: theme.spacing(550 / 8),
}));

export const StyledFormContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  gap: theme.spacing(1.5),
  // maxHeight: 'calc(100vh - 96px - 50px - 48px)',
  // minHeight: 'calc(100vh - 96px - 50px - 48px)',
  overflow: 'auto',
  background: theme.palette.neutrals.N100.main,
  flexDirection: 'row',
  flexWrap: 'wrap',
}));

export const StyledButtonWrapper = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  spacing: theme.spacing(2),
  justifyContent: 'center',
  gap: theme.spacing(1.5),
  alignItems: 'center',
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  maxWidth: theme.spacing(134 / 8),
}));
