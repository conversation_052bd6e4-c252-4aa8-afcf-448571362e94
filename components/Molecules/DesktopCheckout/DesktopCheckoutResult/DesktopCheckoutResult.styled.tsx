import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Avatar, { avatarClasses } from '@mui/material/Avatar';
import { TextField } from '@/components/Atoms/TextField/TextField';
import Button from '@/components/Atoms/Button';
import Typography from '@/components/Atoms/Typography';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  gap: theme.spacing(1.5),
  flexWrap: 'nowrap',
  background: 'white',
  padding: theme.spacing(2),
  borderRadius: '10px',
  flexDirection: 'row',
  justifyContent: 'space-between',
  backgroundColor: theme.palette.neutrals.N100.main,
}));

export const StyledCustomerInfo = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(1.5),
  alignItems: 'center',
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  minWidth: theme.spacing(9),
}));

export const StyledCustomerInfoContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.spacing(2.25),
  alignItems: 'top',
  background: 'white',
  width: '100%',
}));

export const StyledInPut = styled(TextField)(({ theme }) => ({
  [`.MuiInputBase-root`]: {
    height: theme.spacing(10),
  },
  [`.MuiInputBase-input`]: {
    ...theme.typography['heading-xxlarge-700'],
  },
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  background: `${theme.palette.neutrals.N210.main} !important`,
  borderRadius: `${theme.spacing(1.25)} !important`,
  border: `none !important`,
  color: `${theme.palette.primary.main} !important`,
}));

export const StyledAvatar = styled(Avatar)(({ theme }) => ({
  [`&.${avatarClasses.root}`]: {
    width: theme.spacing(8),
    height: theme.spacing(8),
    textTransform: 'uppercase',
    color: theme.palette.common.white,
    backgroundColor: theme.palette.neutrals.N50.main,
    ...theme.typography['heading-xlarge-700'],
  },
}));
