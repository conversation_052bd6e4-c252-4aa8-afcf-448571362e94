import Stack, { StackProps } from '@mui/material/Stack';
import { useSession } from 'next-auth/react';
import React from 'react';
import { TBranch } from '@/lib/types/entities/branch';
import Typography, { ITypographyProps } from '@/components/Atoms/Typography';

export type TCurrencyProps = {
  typographyProps?: ITypographyProps;
  containerProps?: StackProps;
  branch?: TBranch;
};

export const Currency: React.FC<TCurrencyProps> = ({ typographyProps, containerProps, branch }) => {
  const { data } = useSession();
  const branchUsing = branch || data?.user?.branchChosen;
  const currency = branchUsing?.currency;
  const symbol = currency?.symbol || '$';
  return (
    <Stack {...containerProps}>
      <Typography {...typographyProps}>{`${symbol}`}</Typography>
    </Stack>
  );
};
