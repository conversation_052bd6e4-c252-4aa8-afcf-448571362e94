import { Stack, Theme, dialogClasses } from '@mui/material';
import { isEmpty } from 'lodash';
import Modal from '@/components/Atoms/Modal';
import Typography from '@/components/Atoms/Typography';
import { TVoucherChecked } from '@/lib/hooks/utils/useCouponApply';
import { TCustomer } from '@/lib/types/entities/customer';
import { TInvoice } from '@/lib/types/entities/invoice';
import { ECouponType } from '@/lib/types/enum/coupon';
import { CashierPaymentCaculator } from '../Cashier/CashierPaymentCaculator/CashierPaymentCaculator';
import { TOrder } from '@/lib/types/entities/order';

type TModalPaymentCaculator = {
  isOpen: boolean;
  handleClose: () => void;
  invoiceDetail?: TInvoice;
  orders: TOrder[];
  customerInfo?: TCustomer;
  appointmentId: string;
  type: 'create' | 'update';
};

export const ModalPaymentCaculator: React.FC<TModalPaymentCaculator> = ({
  isOpen,
  handleClose,
  invoiceDetail,
  orders,
  appointmentId,
  type = 'create',
}) => {
  const couponRes: TVoucherChecked = {
    voucherChoose:
      invoiceDetail?.newPaid?.invoiceCoupon
        ?.map(res => ({
          id: res.id,
          couponType: res?.couponType || ECouponType.MONEY,
          couponCode: `${
            res?.couponType === ECouponType.PERCENTAGE
              ? res?.percent || ''
              : res?.couponCode || res?.discountValue || ''
          }`,
        }))
        ?.filter(res => !isEmpty(res?.couponCode)) || [],
    voucherResponse:
      invoiceDetail?.newPaid?.invoiceCoupon?.map(res => ({
        id: res.id,
        discountProductId: res.id,
        discountMoney: res.discountValue || 0,
        couponType: res?.couponType || ECouponType.MONEY,
        couponCode: res?.couponCode,
        couponName: res?.couponName,
        percent: res?.percent,
      })) || [],
  };
  return (
    <Modal
      isOpen={isOpen}
      title={
        <Typography
          variant="heading-xmedium-700"
          sx={{ width: '100%', display: 'inline-flex', alignItems: 'center', gap: '12px' }}
        >
          PAYMENT
        </Typography>
      }
      handleClose={() => handleClose()}
      dialogProps={{
        sx: (theme: Theme) => ({
          [`& .${dialogClasses.paper}`]: {
            maxWidth: theme.spacing(126.5),
            height: 'fit-content',
            padding: theme.spacing(3),
            maxHeight: 'calc(100% - 48px)',
            backgroundColor: theme.palette.neutrals.N100.main,
            borderRadius: theme.spacing(1.25),
            [theme.breakpoints.down('md')]: {
              maxWidth: 'auto',
              padding: theme.spacing(2),
            },
          },
        }),
      }}
    >
      <Stack width="800px" gap={3}>
        <CashierPaymentCaculator
          defaultCouponApply={couponRes}
          defaultPaymentMethods={invoiceDetail?.invoicePayments}
          type={type}
          customerInfo={invoiceDetail?.customer || orders?.at(0)?.appointment?.customer}
          orders={orders}
          invoices={invoiceDetail ? [invoiceDetail] : undefined}
          appointmentId={appointmentId}
          onComplete={handleClose}
        />
      </Stack>
    </Modal>
  );
};
