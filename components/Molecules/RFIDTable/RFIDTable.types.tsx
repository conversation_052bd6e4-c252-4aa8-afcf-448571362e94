import { TRFID } from '@/lib/types/entities/RFID';
import { TCustomer } from '@/lib/types/entities/customer';
import { RFID_GROUP } from '@/lib/types/enum/rfid';

export type TRFIDTableProps = {
  rfids?: TRFID[];
  group: RFID_GROUP;
  empty?: number;
  total?: number;
  used?: number;
  lockerNumber?: number;
  onEdit: (id: string) => void;
  onDetail: (val: { rfid: string; lockerNumber: number; customer: TCustomer }) => void;
  canEdit: boolean;
  hasPermissionViewDetailRFID: boolean;
};
