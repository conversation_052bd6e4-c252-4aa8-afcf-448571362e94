import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import isEmpty from 'lodash/isEmpty';
import React from 'react';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import { formatLockerNumber } from '@/lib/constants/utils';
import { TRFID } from '@/lib/types/entities/RFID';
import { RFID_GROUP } from '@/lib/types/enum/rfid';
import RFIDLocker from '../RFIDLocker';
import { LOCKER_STATUS, LOCKER_VARIANT } from '../RFIDLocker/RFIDLocker.types';
import { TRFIDTableProps } from './RFIDTable.types';

const checkStatusLocker = (data: Partial<TRFID>): LOCKER_STATUS => {
  if (data?.status?.name === 'Inactive') return LOCKER_STATUS.INACTIVE;
  if (!isEmpty(data?.customer)) return LOCKER_STATUS.USED;
  return LOCKER_STATUS.EMPTY;
};

export const RFIDTable: React.FC<TRFIDTableProps> = ({
  empty = 0,
  total = 0,
  used = 0,
  rfids,
  onEdit,
  group,
  lockerNumber,
  onDetail,
  canEdit,
  hasPermissionViewDetailRFID,
}) => (
  <Stack
    sx={theme => ({
      padding: theme.spacing(1.5),
      backgroundColor: theme.palette.neutrals.N100.main,
      borderRadius: theme.spacing(1.25),
      gap: theme.spacing(1.5),
    })}
  >
    <Stack
      sx={theme => ({
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: 'row',
        padding: theme.spacing(1.5, 2.25),
        backgroundColor: theme.palette.common.white,
        borderRadius: theme.spacing(1.25),
        gap: theme.spacing(1.5),
      })}
    >
      <Stack
        sx={theme => ({
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row',
          gap: theme.spacing(1.5),
          color: theme.palette.neutrals.N50.main,
        })}
      >
        <Stack
          sx={theme => ({
            width: theme.spacing(6),
            height: theme.spacing(6),
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: group === RFID_GROUP['MALE'] ? theme.palette.info.main : theme.palette.neutrals.N320.main,
            borderRadius: '100%',
          })}
        >
          <Icon variant={group} size={25 / 8} />
        </Stack>
        <Typography variant="heading-xmedium-700" textTransform="capitalize">
          {group} Group
        </Typography>
      </Stack>
      <Stack
        sx={theme => ({
          flexDirection: 'row',
          gap: theme.spacing(1.5),
          color: theme.palette.neutrals.N50.main,
        })}
      >
        <Typography variant="heading-xmedium-700">Total</Typography>
        <Typography variant="heading-xmedium-700">{total}</Typography>
      </Stack>
      <Stack
        sx={theme => ({
          flexDirection: 'row',
          gap: theme.spacing(1.5),
          color: theme.palette.neutrals.N160.main,
        })}
      >
        <Typography variant="heading-xmedium-700">Used</Typography>
        <Typography variant="heading-xmedium-700">{used}</Typography>
      </Stack>
      <Stack
        sx={theme => ({
          flexDirection: 'row',
          gap: theme.spacing(1.5),
          color: group === RFID_GROUP['MALE'] ? theme.palette.info.main : theme.palette.neutrals.N320.main,
        })}
      >
        <Typography variant="heading-xmedium-700">Empty</Typography>
        <Typography variant="heading-xmedium-700">{empty}</Typography>
      </Stack>
    </Stack>
    <Grid container rowSpacing={1.5} columnSpacing={1.5}>
      {rfids?.map(({ id, serialCode, lockerNumber = 0, ...restData }) => {
        const status = checkStatusLocker(restData);

        return (
          <Grid item xs={3} md={12 / 5} xl={12 / 6}>
            <RFIDLocker
              text={formatLockerNumber.format(lockerNumber)}
              status={status}
              variant={group as string as LOCKER_VARIANT}
              onDetail={() => {
                if (!hasPermissionViewDetailRFID) return;
                return restData?.customer && onDetail({ customer: restData.customer, lockerNumber, rfid: serialCode });
              }}
              onEdit={() => onEdit(id)}
              canEdit={canEdit}
              hasPermissionViewDetailRFID={hasPermissionViewDetailRFID}
            />
          </Grid>
        );
      })}
    </Grid>
  </Stack>
);
