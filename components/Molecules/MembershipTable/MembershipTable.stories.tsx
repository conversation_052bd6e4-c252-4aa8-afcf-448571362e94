import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import MembershipTable from '.';
import { IMembershipColumn } from './MembershipTable.types';
import { TMembership } from '@/lib/types/entities/membership';
import Typography from '@/components/Atoms/Typography';
import Price from '../Price';

export default {
  title: 'Molecules/MembershipTable',
  component: MembershipTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof MembershipTable>;

const SAMPLE_ROWS: TMembership[] = [];

const SAMPLE_COLUMNS: IMembershipColumn[] = [
  {
    name: 'name',
    label: 'MEMBERSHIP NAME',
  },
  {
    name: 'category',
    label: 'CATEGORY',
    render: (row: TMembership) => <Typography variant="body-xlarge-400">{row?.category?.name}</Typography>,
  },
  {
    name: 'branches',
    label: 'BRANCH',
    render: (row: TMembership) => (
      <Typography variant="body-xlarge-400">{row?.branches?.map(branch => branch.name).join(', ')}</Typography>
    ),
  },
  {
    name: 'credit',
    label: 'CREDIT',
    render: (row: TMembership) => <Price amount={row?.credit || 0} typographyProps={{ variant: 'body-xlarge-400' }} />,
  },
  {
    name: 'price',
    label: 'PRICE',
    render: (row: TMembership) => <Price amount={row?.price || 0} typographyProps={{ variant: 'body-xlarge-400' }} />,
  },
  {
    name: 'period',
    label: 'PERIOD',
    render: (row: TMembership) => (
      <Typography variant="body-xlarge-400">{`${row?.period} ${row.periodUnit}`}</Typography>
    ),
  },
];

export const Default: Story = {
  args: {
    loading: false,
    rows: SAMPLE_ROWS,
    columns: SAMPLE_COLUMNS,
  },
  render: (args: any) => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <MembershipTable {...args} />
    </Box>
  ),
};
