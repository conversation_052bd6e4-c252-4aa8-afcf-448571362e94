import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Typography from '@/components/Atoms/Typography';

export const StyledWrapper = styled(Stack)(({ theme }) => ({
  width: '100%',
  position: 'relative',
  justifyContent: 'space-between',
  alignItems: 'center',
  flexDirection: 'row',
  padding: theme.spacing(0.5),
  marginBottom: theme.spacing(2.25),
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
  ...theme.typography['heading-xxxsmall-700'],
  textOverflow: 'ellipsis',
}));

export const StyledTotalText = styled(Typography)(({ theme }) => ({
  width: theme.spacing(14 / 8),
  height: 'fit-content',
  textAlign: 'center',
  borderRadius: theme.spacing(0.75),
  ...theme.typography['heading-xxxsmall-700'],
  lineHeight: theme.spacing(11 / 8),
}));
