import React from 'react';
import Stack from '@mui/material/Stack';
import Typography from '@/components/Atoms/Typography';
import { StyledWrapper, StyledTotalText } from './CustomResourceHeader.styled';
import { WAITING_LIST_RESOURCE } from '@/lib/constants/dateType';
import { customPalettes } from '@/theme/customPalettes';

export const CustomResourceHeader: React.FC = (props: any) => (
  <StyledWrapper bgcolor={props?.resource?.id === WAITING_LIST_RESOURCE?.id ? props?.resource?.color : 'white'}>
    <Stack gap={0.5}>
      <StyledTotalText
        color={props?.resource?.id === WAITING_LIST_RESOURCE?.id ? customPalettes?.neutrals?.N50?.main : 'white'}
        bgcolor={props?.resource?.id === WAITING_LIST_RESOURCE?.id ? 'white' : customPalettes?.neutrals?.N50?.main}
      >
        {props?.resource?.total}
      </StyledTotalText>
      <Typography
        variant="heading-xxxsmall-700"
        sx={theme => ({
          width: theme.spacing(45 / 8),
          overflow: 'hidden',
          textAlign: 'left',
          color: props?.resource?.id === WAITING_LIST_RESOURCE?.id ? 'white' : customPalettes?.neutrals?.N50?.main,
          textOverflow: 'ellipsis',
          [theme.breakpoints.up('xl')]: {
            width: theme.spacing(90 / 8),
          },
        })}
      >
        {props?.label}
      </Typography>
    </Stack>
  </StyledWrapper>
);
