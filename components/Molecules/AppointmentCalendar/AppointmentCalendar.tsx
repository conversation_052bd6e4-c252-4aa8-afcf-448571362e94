import Box from '@mui/material/Box';
import dayjs from 'dayjs';
import { useSession } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { Calendar, DateLocalizer, Views, dayjsLocalizer, stringOrDate } from 'react-big-calendar';
import withDragAndDrop from 'react-big-calendar/lib/addons/dragAndDrop';
import { useAlert } from '@/lib/hooks/context/useAlert';
import {
  useMutationAppointmentEventUpdate,
  useMutationAppointmentUpdateBreakOrBlockTime,
} from '@/lib/hooks/mutation/appointment';
import { useBreakAndBlockTime } from '@/lib/hooks/queries/appointment';
import { useEmloyeeGetByServices } from '@/lib/hooks/queries/employee';
import { useServices } from '@/lib/hooks/queries/service';
import { TAppointmentEventUpdateReq } from '@/lib/types/entities/appointment';
import { TApiError } from '@/lib/utils/transformResponse';
import 'react-big-calendar/lib/addons/dragAndDrop/styles.css';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { AppointmentCalendarContext } from './AppointmentCalendar.context';
import { StyledWrapper } from './AppointmentCalendar.styled';
import { TAppointmentCalendarProps, TAppointmentEvent, TEventResource } from './AppointmentCalendar.types';
import AppointmentEvent from './AppointmentEvent';
import CustomResourceHeader from './CustomResourceHeader';
import TimeSlotWrapper from './TimeSlotWrapper';

const localizer: DateLocalizer = dayjsLocalizer(dayjs);
const DnDCalendar = withDragAndDrop<TAppointmentEvent, TEventResource>(Calendar);
const CustomToolbar = () => <></>;
const CustomTimeGutterHeader = () => <Box minWidth="54px" />;

export const AppointmentCalendar: React.FC<TAppointmentCalendarProps> = ({
  appointmentEvents: initialAppointmentEvents,
  eventResources,
  currentDate,
  onRefetch,
}) => {
  const { data: serviceData } = useServices();
  const { onUpdateAppointmentEvent } = useMutationAppointmentEventUpdate();
  const { showError, showSuccess } = useAlert();
  const [appointmentEvents, setAppointmentEvents] = useState<TAppointmentEvent[]>([]);
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const filterDateString = searchParams.get('date') || dayjs().format('YYYY-MM-DD');
  const currentBranchId = session?.user?.branchChosen?.id || '';
  const { mutate: revalidateBreakAndBlockTime } = useBreakAndBlockTime({
    startTime: dayjs(filterDateString).startOf('day').toDate().toISOString(),
    endTime: dayjs(filterDateString).endOf('day').toDate().toISOString(),
    branchId: currentBranchId,
  });
  const { data: allEmployeeByServiceId } = useEmloyeeGetByServices({
    startDate: filterDateString,
    endDate: filterDateString,
    branchId: currentBranchId,
  });
  const { trigger: triggerUpdateBreakOrBlock } = useMutationAppointmentUpdateBreakOrBlockTime();

  useEffect(() => {
    setAppointmentEvents([...initialAppointmentEvents]);
  }, [initialAppointmentEvents]);

  // useEffect(() => {
  //   setTimeout(() => {
  //     const element = document.getElementById(`time-slot-9`);
  //     if (element) {
  //       element.scrollIntoView({ behavior: 'smooth', block: 'start' });
  //     }
  //   }, 1000);
  // }, []);

  // eslint-disable-next-line react/jsx-no-constructed-context-values
  const memoizedResources = {
    appointmentEvents: [...appointmentEvents],
    eventResources: [...eventResources],
    currentDate,
    onRefetch,
  };

  const onChangeEventTime = async ({
    event,
    start,
    end,
    resourceId,
  }: {
    event: TAppointmentEvent;
    start: stringOrDate;
    end: stringOrDate;
    isAllDay?: boolean;
    resourceId?: string | number;
  }) => {
    if (!event.isClickAble) return;
    /**
     * resourceId = new employeeId
     * event.resourceId = current employeeId
     * resourceId = 1 => waiting
     *
     * 1. new employee off return
     * 2. new employee does not serve current service return
     * 3. allow move to waiting
     */

    const isBreakOrBlockTime = !!event?.type;
    const waitingValue = '1';
    if (isBreakOrBlockTime) {
      const data = {
        branchId: currentBranchId,
        employeeId: resourceId === waitingValue ? '' : String(resourceId || ''),
        startTime: dayjs(start).toDate().toISOString(),
        endTime: dayjs(end).toDate().toISOString(),
        id: event?.id,
      };

      setAppointmentEvents(prev => [
        ...(prev || [])?.map(event => (event?.id === data?.id ? { ...event, ...data } : event)),
      ]);

      await triggerUpdateBreakOrBlock(data);
      revalidateBreakAndBlockTime();
      onRefetch();
      return;
    }

    const isNewTargetResourceOff = memoizedResources.appointmentEvents?.some(
      ev => ev?.resourceId === resourceId && (ev?.isOffRange || ev?.isTodayOff)
    );

    if (
      isNewTargetResourceOff ||
      typeof resourceId !== 'string' ||
      (event?.resourceId === String(resourceId) && dayjs(start).isSame(event.start) && dayjs(end).isSame(event.end)) ||
      resourceId === waitingValue
    )
      return;

    const serviceId = event?.serviceId;
    const listEmployeeServeCurrentService = allEmployeeByServiceId?.find(item => item?.serviceId === serviceId)
      ?.employees;

    const isNewEmployeeServeCurrentSerivce = listEmployeeServeCurrentService?.some(i => i.id === resourceId);

    if (!isNewEmployeeServeCurrentSerivce && resourceId !== waitingValue) return;

    // TODO call api check duplicate
    const currProduct = serviceData?.find(service => service?.id === event?.serviceId);
    const prevAppointmentEvents = [...appointmentEvents];
    const prevEmployeeIds = event.employeesJoinService?.map(item => ({ id: item.id })) || [];
    const prevAppointment = prevAppointmentEvents.find(item => item?.appointmentId === event?.appointmentId);
    const isChangeDate = event?.resourceId === resourceId;
    const isChangeEmployee = prevEmployeeIds?.some(item => item?.id !== resourceId);
    const isChangeDuplicate =
      !isChangeDate && !isChangeEmployee && prevAppointment?.employeesJoinService?.some(item => item.id === resourceId);

    if (isChangeDuplicate) {
      return showError({ title: 'Duplicate appointment' });
    }
    const newEmployeeIds = isChangeEmployee
      ? prevEmployeeIds.map(e => {
          if (e?.id === event.resourceId) {
            return { id: resourceId || '' };
          }
          return e;
        })
      : prevEmployeeIds;

    const dataToUpdateEvent: TAppointmentEventUpdateReq = {
      employeeId: resourceId || '',
      startTime: dayjs(start).toDate().toISOString(),
      endTime: dayjs(end).toDate().toISOString(),
      id: event?.orderItemId || '',
      product: {
        id: currProduct?.id || '',
      },
      duration: {
        id: currProduct?.duration?.id || '',
      },
      note: event?.remark || '',
      employees: [...newEmployeeIds],
    };

    const newAppointments = (prevAppointmentEvents || [])?.map(appointment => {
      if (event?.id !== appointment?.id) return appointment;
      const isSameEmployee = appointment.resourceId === event.resourceId;
      const isPreIncludeEmployee = appointment?.employeesJoinService?.some(e => e?.id === event.resourceId);
      if (!isSameEmployee || !isPreIncludeEmployee) return appointment;
      return {
        ...appointment,
        ...dataToUpdateEvent,
        start,
        end,
        employeesJoinService: appointment?.employeesJoinService?.map(e => {
          if (e?.id === event?.resourceId) {
            return { id: resourceId };
          }
          return e;
        }),
        resourceId: isPreIncludeEmployee ? resourceId : appointment?.resourceId,
      };
    });

    setAppointmentEvents([...newAppointments]);

    const updateAppointmentResponse = await onUpdateAppointmentEvent(dataToUpdateEvent);
    if (updateAppointmentResponse.status === 'error') {
      setAppointmentEvents([...prevAppointmentEvents]);
      if (typeof updateAppointmentResponse?.message?.error === 'string') {
        return showError({ title: updateAppointmentResponse?.message?.error || 'Updated failed' });
      }
      if (Array.isArray(updateAppointmentResponse.message.error)) {
        return showError({ title: 'Updated failed' });
      }
      const apiError = updateAppointmentResponse.message.error as TApiError;
      return showError({ title: apiError?.message });
    }
    showSuccess({ title: 'Updated !' });
    onRefetch();
  };

  return (
    <AppointmentCalendarContext.Provider value={memoizedResources}>
      <StyledWrapper>
        {eventResources.length > 1 && (
          <DnDCalendar
            defaultDate={memoizedResources.currentDate}
            defaultView={Views.DAY}
            events={memoizedResources.appointmentEvents}
            localizer={localizer}
            resourceIdAccessor="id"
            resources={memoizedResources.eventResources}
            resourceTitleAccessor="title"
            dayLayoutAlgorithm="no-overlap"
            step={15}
            timeslots={4}
            views={{ day: true }}
            formats={{ timeGutterFormat: 'HH:mm' }}
            min={dayjs(currentDate).startOf('day').add(9, 'hours').toDate()}
            tooltipAccessor={null}
            components={{
              toolbar: CustomToolbar,
              event: AppointmentEvent,
              resourceHeader: CustomResourceHeader,
              timeGutterHeader: CustomTimeGutterHeader,
              timeSlotWrapper: TimeSlotWrapper,
            }}
            onEventDrop={onChangeEventTime}
            resizableAccessor={() => false}
            scrollToTime={dayjs().startOf('day').add(9, 'hours').toDate()}
          />
        )}
      </StyledWrapper>
    </AppointmentCalendarContext.Provider>
  );
};
