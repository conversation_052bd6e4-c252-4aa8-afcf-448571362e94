import Popover from '@mui/material/Popover';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import React, { useMemo, useState } from 'react';
import { EventProps } from 'react-big-calendar';
import Typography from '@/components/Atoms/Typography';
import { APPOINTMENT_STATUS_COLORS } from '@/lib/constants/status';
import { useMe } from '@/lib/hooks/queries/me';
import { EAppointmentStatus } from '@/lib/types/enum/appointment';
import { EPermission, EResourceAccess } from '@/lib/types/enum/role';
import { hasPermission } from '@/lib/utils/role';
import { TAppointmentEvent } from '../AppointmentCalendar.types';
import AppointmentEventDetail from '../AppointmentEventDetail';
import AppointmentEventForm from '../AppointmentEventForm';
import AddTimeForm from '../TimeSlotWrapper/AddTimeForm';
import { StyledTooltip, StyledWrapper } from './AppointmentEvent.styled';

export const AppointmentEvent: React.FC<EventProps<TAppointmentEvent>> = props => {
  const isClickAble = props?.event?.isClickAble;
  const isBlockOrBreakTimeType = props?.event?.type;
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const handleClose = () => {
    setAnchorEl(null);
  };

  const { data: meData } = useMe();
  const resource = meData?.resource || [];
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!isClickAble) return;
    if (hasPermission(resource, EResourceAccess['APPOINTMENT'], EPermission['UPDATE'])) {
      setAnchorEl(event.currentTarget);
    }
  };
  const open = Boolean(anchorEl);
  const id = open ? `appointment-event-${props?.event?.id}` : undefined;

  const title = useMemo(() => {
    if (!isClickAble || isBlockOrBreakTimeType) return <></>;
    return <AppointmentEventDetail eventAppointment={props?.event} />;
  }, [props?.event, isClickAble]);
  if (!props?.event) return <></>;

  return (
    <>
      <StyledTooltip followCursor placement="bottom-start" title={title}>
        <StyledWrapper
          variant="contained"
          disableRipple
          disableFocusRipple
          disableTouchRipple
          onClick={handleClick}
          bgColor={props?.event?.color}
          $textColor={props?.event?.textColor}
          $isClickAble={isClickAble}
          $isBlockOrBreakTimeType={isBlockOrBreakTimeType}
          $isOffRange={!!props?.event?.isOffRange}
        >
          <Stack width="100%" height="100%" justifyContent="flex-start" alignItems="flex-end" gap={0.5}>
            {/* <Icon
              variant={APPOINTMENT_STATUS_COLORS[props?.event?.status || EAppointmentStatus.REQUEST]?.icon}
              size={1}
            /> */}
            <Stack gap={0.5} width="100%">
              {isClickAble && !isBlockOrBreakTimeType && (
                <Typography
                  variant="heading-xxxsmall-700"
                  component="p"
                  width="100%"
                  overflow="hidden"
                  textOverflow="ellipsis"
                  whiteSpace="pre-wrap"
                >
                  {dayjs(props?.event?.start).format('HH:mm')} to {dayjs(props?.event?.end).format('HH:mm')}
                </Typography>
              )}
              <Typography
                variant="heading-xxxsmall-700"
                textTransform="capitalize"
                component="p"
                width="100%"
                overflow="hidden"
                textOverflow="ellipsis"
                whiteSpace="pre-wrap"
              >
                {props?.title}
              </Typography>
            </Stack>
          </Stack>
        </StyledWrapper>
      </StyledTooltip>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={theme => ({
          marginLeft: theme.spacing(0.35),
        })}
        disableRestoreFocus
      >
        {isBlockOrBreakTimeType ? (
          <AddTimeForm
            appointmentType={isBlockOrBreakTimeType}
            title={isBlockOrBreakTimeType === 'blocktime' ? 'Block Time' : 'Break Time'}
            defaultValues={{
              startTime: dayjs(props?.event?.start || '')
                .toDate()
                .toISOString(),
              endTime: dayjs(props?.event?.end || '')
                .toDate()
                .toISOString(),
              id: props?.event?.id,
            }}
            handleClose={handleClose}
            employeeId={props?.event?.resourceId}
            type="edit"
          />
        ) : (
          <AppointmentEventForm
            orderItemId={props?.event?.orderItemId}
            employeeId={props?.event?.resourceId}
            appointmentId={props?.event?.appointmentId}
            checkoutTime={props?.event?.checkout}
            defaultValues={{
              customer: {
                id: props?.event?.customerId || '',
                name: props?.event?.customerName || '',
              },
              status: {
                id: props?.event?.status || EAppointmentStatus.REQUEST,
                name: APPOINTMENT_STATUS_COLORS[props?.event?.status || EAppointmentStatus.REQUEST]?.label,
              },
              employee:
                props?.event?.employeesJoinService?.map(e => ({
                  id: e?.id,
                  name: e?.displayName,
                })) || [],
              startTime: dayjs(props?.event?.start)
                .toDate()
                .toUTCString(),
              endTime: dayjs(props?.event?.end)
                .toDate()
                .toUTCString(),
              phone: props?.event?.customerPhone,
              product: {
                id: props?.event?.serviceId || '',
                name: props?.event?.serviceName || '',
              },
              remark: props?.event?.remark || '',
            }}
            type="edit"
            onClose={handleClose}
          />
        )}
      </Popover>
    </>
  );
};
