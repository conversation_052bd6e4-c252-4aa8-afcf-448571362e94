import styled from '@emotion/styled';
import Tooltip, { tooltipClasses, TooltipProps } from '@mui/material/Tooltip';
import Button from '@mui/material/Button';
import { alpha } from '@mui/material';
import Typography from '@/components/Atoms/Typography';

export const StyledWrapper = styled(Button)<{
  bgColor?: string;
  $isClickAble?: boolean;
  $isOffRange?: boolean;
  $textColor?: string;
  $isBlockOrBreakTimeType?: 'breaktime' | 'blocktime';
}>(({ theme, bgColor, $isClickAble, $isOffRange, $isBlockOrBreakTimeType, $textColor }) => ({
  width: '100%',
  minWidth: 'auto',
  height: 'calc(100% - 2px)',
  position: 'relative',
  padding: theme.spacing(0.5, 0.25),
  color: theme.palette.neutrals.N50.main,
  borderRadius: theme.spacing(5 / 8),
  ...($isClickAble && !!$isBlockOrBreakTimeType
    ? {}
    : { cursor: 'auto', color: $isOffRange ? 'white' : '#3C2313', borderRadius: 0 }),

  ...($textColor ? { cursor: 'pointer', color: $textColor, borderRadius: 0 } : {}),

  backgroundColor: !$isClickAble || $textColor ? bgColor : alpha(bgColor || theme.palette.neutrals.N250.main, 0.4),
  textTransform: 'none',
  overflow: 'hidden',
  textAlign: 'left',
  boxShadow: 'none',
  zIndex: 3,
  '&:hover': {
    boxShadow: 'none',
  },
}));

export const StyledDurationText = styled(Typography)(({ theme }) => ({
  position: 'relative',
  [`&::before`]: {
    position: 'absolute',
    content: "''",
    left: theme.spacing(-2),
    top: '50%',
    transform: 'translateY(-50%)',
    width: theme.spacing(3 / 8),
    height: theme.spacing(3 / 8),
    borderRadius: '50%',
    backgroundColor: theme.palette.neutrals.N50.main,
  },
}));

export const StyledTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    padding: 0,
    margin: 0,
    minWidth: theme.spacing(236 / 8),
    backgroundColor: 'transparent',
  },
}));
