import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import { AppointmentCalendar } from './AppointmentCalendar';
import { APPOINTMENT_EVENTS, EVENT_RESOURCES } from '@/lib/mockData/appointmentCalendar';

const meta: Meta = {
  title: 'Molecules/AppointmentCalendar',
  component: AppointmentCalendar,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof AppointmentCalendar>;

export const Default: Story = {
  args: {
    appointmentEvents: APPOINTMENT_EVENTS,
    eventResources: EVENT_RESOURCES,
  },
  render: args => (
    <Box width="100%">
      <AppointmentCalendar {...args} />
    </Box>
  ),
};
