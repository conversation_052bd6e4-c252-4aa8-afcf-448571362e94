import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Avatar, { avatarClasses } from '@mui/material/Avatar';

export const StyledWrapper = styled(Stack)(({ theme }) => ({
  width: theme.spacing(240 / 8),
  position: 'relative',
  padding: theme.spacing(1),
  gap: theme.spacing(20 / 8),
  backgroundColor: theme.palette.common.white,
  color: theme.palette.neutrals.N50.main,
  borderRadius: theme.spacing(5 / 8),
  overflow: 'hidden',
  boxShadow: '4px 4px 4px 0px rgba(0, 0, 0, 0.05)',
}));

export const StyledAvatar = styled(Avatar)(({ theme }) => ({
  [`&.${avatarClasses.root}`]: {
    width: theme.spacing(8),
    height: theme.spacing(8),
    textTransform: 'uppercase',
    color: theme.palette.common.white,
    backgroundColor: theme.palette.neutrals.N50.main,
    ...theme.typography['heading-xxlarge-700'],
  },
}));
