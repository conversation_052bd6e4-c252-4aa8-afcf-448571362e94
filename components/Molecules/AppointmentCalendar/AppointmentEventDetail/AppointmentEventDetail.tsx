import { Divider, Stack, Theme } from '@mui/material';
import dayjs from 'dayjs';
import React from 'react';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import { StyledAvatar } from '@/components/Organisms/Appointment/AppointmentForm/SidePanel/SidePanel.styled';
import { calculateDuration } from '@/lib/utils/date';
import { customPalettes } from '@/theme/customPalettes';
import Price from '../../Price';
import { TAppointmentEvent } from '../AppointmentCalendar.types';
import { StyledWrapper } from './AppointmentEventDetail.styled';

type TAppointmentEventDetailProps = {
  eventAppointment?: TAppointmentEvent;
};
export const AppointmentEventDetail: React.FC<TAppointmentEventDetailProps> = ({ eventAppointment }) => (
  <StyledWrapper>
    <Stack direction="row" gap={1.25}>
      <StyledAvatar>{eventAppointment?.customerName?.[0]}</StyledAvatar>
      <Stack gap={1 / 4}>
        <Typography variant="heading-xsmall-700">{eventAppointment?.customerName}</Typography>
        <Typography variant="body-medium-400">#{eventAppointment?.customerCode}</Typography>
        <Stack direction="row" gap={1} alignItems="center">
          <Icon variant="phone" size={1.5} />
          <Typography variant="body-medium-400">{eventAppointment?.customerPhone}</Typography>
        </Stack>
      </Stack>
    </Stack>
    <Divider
      sx={(theme: Theme) => ({
        borderBottomWidth: theme.spacing(1 / 16),
        borderColor: theme.palette.neutrals.N180.main,
      })}
    />
    <Stack gap={0.5}>
      <Stack direction="row" justifyContent="space-between">
        <Stack direction="row" gap={1.25} alignItems="center">
          <Typography variant="heading-xsmall-700" color={customPalettes?.neutrals?.N180?.main}>
            {dayjs(eventAppointment?.start).format('HH:mm')} - {dayjs(eventAppointment?.end).format('HH:mm')}
          </Typography>
          <Typography variant="heading-xsmall-700" color={customPalettes?.neutrals?.N180?.main}>
            {calculateDuration(eventAppointment?.start || '', eventAppointment?.end || '')}
          </Typography>
        </Stack>
      </Stack>
      <Stack direction="row" justifyContent="space-between">
        <Typography variant="heading-xsmall-700">{eventAppointment?.serviceName}</Typography>
        <Price amount={eventAppointment?.totalPrice} typographyProps={{ variant: 'heading-xsmall-700' }} />
      </Stack>
    </Stack>
  </StyledWrapper>
);
