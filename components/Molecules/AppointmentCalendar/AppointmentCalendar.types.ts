import { TBlockAndBreakAppoiment } from '@/lib/types/entities/appointment';
import { TEmployee } from '@/lib/types/entities/employee';
import { TShiftTime } from '@/lib/types/entities/workingHour';
import { EAppointmentStatus } from '@/lib/types/enum/appointment';

export type TEventResource = {
  id: string;
  title: string;
  branch: string;
  total: number;
  color: string;
  workingHours: TShiftTime[];
  isOff?: boolean;
  isOffTo?: boolean;
  offTo?: string | null;
};
export type TAppointmentEventStatus = 'new' | 'confirm' | 'started' | 'cancel' | 'completed';
export type TAppointmentEvent = {
  id: string;
  appointmentId: string;
  title: string;
  start: string | Date;
  end: string | Date;
  color: string;
  resourceId: string;
  customerId: string;
  customerCode: string;
  customerPhone: string;
  customerName: string;
  checkout?: string;
  status: EAppointmentStatus;
  totalPrice: number;
  serviceId: string;
  serviceName: string;
  orderItemId: string;
  isDraggable?: boolean;
  isResizable?: boolean;
  remark: string;
  isClickAble: boolean;
  isTodayOff: boolean;
  isOffRange: boolean;
  employeesJoinService?: Partial<TEmployee>[];
  type?: 'blocktime' | 'breaktime';
  textColor?: string;
};

export type TAppointmentCalendarProps = {
  appointmentEvents: TAppointmentEvent[];
  eventResources: TEventResource[];
  currentDate: Date;
  onRefetch: () => void;
};
