import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';

export const StyledWrapper = styled(Stack)(({ theme }) => ({
  '.rbc-calendar': {
    width: '100%',
    overflowX: 'hidden',
    overflow: 'visible',
    position: 'relative',
  },
  '.rbc-event': {
    padding: '0px !important',
    border: 'none !important',
    backgroundColor: 'transparent !important',
  },

  '.rbc-time-slot': {
    height: '100%',
    backgroundColor: theme.palette.neutrals.N260.main,
    color: theme.palette.neutrals.N90.main,
    ...theme.typography['heading-xsmall-700'],
  },

  '.rbc-event-label': {
    display: 'none !important',
  },

  '.rbc-timeslot-group': {
    position: 'relative',
    height: `${theme.spacing(93 / 8)} !important`,
    borderBottom: 'none',
  },

  '.rbc-time-gutter .rbc-timeslot-group .rbc-time-slot': {
    // minWidth: theme.spacing(7),
    // padding: theme.spacing(0, 1.5, 0, 0.5),
    backgroundColor: 'unset',
    textAlign: 'center',
    cursor: 'pointer',
  },
  '.rbc-time-gutter .rbc-timeslot-group .rbc-time-slot .rbc-label': { padding: 0 },

  '.draggable.dragging': {
    opacity: 1,
  },
  '.rbc-time-content > * + * > *': {
    borderLeft: `1px solid ${theme.palette.neutrals.N40.main}`,
  },

  '.rbc-time-content > .rbc-time-column:nth-child(2) .rbc-timeslot-group': {
    borderLeft: `none`,
  },

  '.rbc-day-slot .rbc-time-slot': {
    borderTop: `0.2px solid ${theme.palette.neutrals.N180.main}`,
  },

  '.rbc-day-slot .rbc-timeslot-group:not(:first-child) .MuiButton-root:first-child .rbc-time-slot': {
    borderTop: `1px solid ${theme.palette.neutrals.N180.main}`,
  },

  '.rbc-day-slot .rbc-timeslot-group:first-child .MuiButton-root:first-child .rbc-time-slot': {
    borderTop: `none`,
  },

  '.rbc-time-content': {
    borderTop: 'none',
    overflow: 'visible',
  },
  '.rbc-time-content > .rbc-day-slot': {
    width: 'auto',
  },
  '.rbc-time-view .rbc-row': {
    minHeight: '0px',
  },
  '.rbc-time-header-content': {
    // minWidth: 'fit-content',
    borderLeft: 'none',
    backgroundColor: 'transparent',
  },
  '.rbc-header': {
    borderBottom: 'none',
    padding: 0,
    overflow: 'visible',
  },
  // '.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event': {
  //   padding: `${theme.spacing(0.5, 1)} !important`,
  //   borderRadius: '5px !important',
  //   backgroundColor: `${theme.palette.neutrals.N20.main} !important`,
  // },
  '.rbc-time-header-content > .rbc-row.rbc-row-resource': {
    width: '100%',
    borderBottom: 'none',
  },
  '.rbc-time-header-content > .rbc-row.rbc-row-resource .rbc-header': {
    width: '100%',
  },
  '.rbc-time-view': {
    width: '100%',
    height: 'fit-content',
    minWidth: '100%',
    border: 'none',
    position: 'relative',
  },
  '.rbc-time-view-resources .rbc-time-gutter, .rbc-time-view-resources .rbc-time-header-gutter': {
    marginRight: theme.spacing(1.5),
    borderRight: 'none',
    backgroundColor: theme.palette.neutrals.N10.main,
  },
  '.rbc-day-slot .rbc-events-container': {
    margin: theme.spacing(1 / 8, 1 / 8, -1 / 8, 2 / 8),
    borderLeft: 'none',
    boxSizing: 'border-box',
  },
  '.rbc-time-header.rbc-overflowing': {
    borderRight: 'none',
  },
  '.rbc-day-slot': {
    flexGrow: 1,
    minWidth: 'auto',
  },
  '.rbc-time-view-resources .rbc-time-header': {
    width: '100%',
    // minWidth: 'fit-content',
    overflow: 'hidden',
    position: 'sticky',
    left: 0,
    top: 0,
    zIndex: 10,
    background: theme.palette.neutrals.N10.main,
  },
  '.rbc-current-time-indicator': {
    display: 'none',
  },
  '.rbc-addons-dnd-resizable': {
    width: '100%',
  },
  '.rbc-addons-dnd-is-dragging .rbc-event:not(.rbc-addons-dnd-dragged-event):not(.rbc-addons-dnd-drag-preview)': {
    opacity: 1,
  },
  '.rbc-time-view-resources .rbc-header, .rbc-time-view-resources .rbc-day-bg': {
    width: 'auto',
    padding: theme.spacing(0, 2.5 / 8),
  },
  '.rbc-time-view-resources .rbc-time-header-content': {
    minWidth: '0',
    flex: '1 1',
  },
  '.rbc-today': {
    backgroundColor: 'transparent',
  },
}));
