export type TFormValues = {
  status?: {
    id: string;
    name: string;
  };
  product?: {
    id: string;
    name: string;
  };
  customer?: {
    id: string;
    name: string;
  };
  employee?: {
    id?: string;
    name?: string;
  }[];
  phone?: string;
  startTime?: string;
  endTime?: string;
  price?: number;
  remark?: string;
};

export type TAppointmentEventForm = {
  orderItemId?: string;
  appointmentId?: string;
  employeeId?: string;
  checkoutTime?: string;
  defaultValues: TFormValues;
  type: 'edit' | 'create';
  onClose: () => void;
};
