'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Divider, IconButton, Theme } from '@mui/material';
import Stack from '@mui/material/Stack';
import { CondOperator } from '@nestjsx/crud-request';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import ConfirmModal from '@/components/Atoms/ConfirmModal';
import Icon from '@/components/Atoms/Icon';
import Typography from '@/components/Atoms/Typography';
import { DEFAULT_DURATION, FORMAT_TIME_FULL } from '@/lib/constants/dateTime';
import { WAITING_LIST_RESOURCE } from '@/lib/constants/dateType';
import { PATH_NAME } from '@/lib/constants/pathname';
import { OPTIONS_APPOINTMENT_DEFAULT } from '@/lib/constants/status';
import { SWRKey } from '@/lib/constants/SWRKey';
import { useAlert } from '@/lib/hooks/context/useAlert';
import {
  useMutationAppointmentCreate,
  useMutationAppointmentEventDelete,
  useMutationAppointmentEventUpdate,
  useMutationAppointmentUpdate,
  useMutationSingleOrderAppointmentDetail,
} from '@/lib/hooks/mutation/appointment';
import { useMutationOrderDetailDelete } from '@/lib/hooks/mutation/orderDetail';
import { useCustomer } from '@/lib/hooks/queries/customer';
import { useEmloyeeGetByServices, useEmployees } from '@/lib/hooks/queries/employee';
import { useServices } from '@/lib/hooks/queries/service';
import useDebounce from '@/lib/hooks/utils/useDebounce';
import { checkDuplicateAppointment } from '@/lib/services/appointment';
import {
  TAppointmentCreateReq,
  TAppointmentEventUpdateReq,
  TAppointmentUpdateReq,
} from '@/lib/types/entities/appointment';
import { TCustomer } from '@/lib/types/entities/customer';
import { EAppointmentStatus } from '@/lib/types/enum/appointment';
import { getCredits } from '@/lib/utils/credit';
import { getErrorMessage } from '@/lib/utils/error';
import { concatenateNames } from '@/lib/utils/string';
import { TTransformPaginationResponse } from '@/lib/utils/transformResponse';
import { customPalettes } from '@/theme/customPalettes';
import WarningModal from '../../WarningModal';
import { AppointmentCalendarContext } from '../AppointmentCalendar.context';
import { transformCommonValueSubmit } from './AppointmentEventForm.helper';
import { StyledButton, StyledLink, StyledWrapper } from './AppointmentEventForm.styled';
import { TAppointmentEventForm, TFormValues } from './AppointmentEventForm.types';
import { AppointmentEventFormBody } from './AppointmentEventFormBody';
import { validationSchema } from './validation';
import { useAppointments } from '@/lib/hooks/queries/appointment';
import { EOrderType } from '@/lib/types/enum/order';
import { useMe } from '@/lib/hooks/queries/me';
import { hasPermission } from '@/lib/utils/role';
import { EPermission, EResourceAccess } from '@/lib/types/enum/role';
// TODO refactor handle load more
export const AppointmentEventForm: React.FC<TAppointmentEventForm> = ({
  orderItemId,
  employeeId,
  appointmentId,
  defaultValues,
  type,
  checkoutTime,
  onClose,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const filterDateString = searchParams.get('date') || dayjs().format('YYYY-MM-DD');
  const currentBranchId = session?.user?.branchChosen?.id || '';

  const { data: allEmployeeByServiceId } = useEmloyeeGetByServices({
    startDate: filterDateString,
    endDate: filterDateString,
    branchId: currentBranchId,
  });
  const selectedDate = dayjs(searchParams.get('date')).isValid()
    ? dayjs(searchParams.get('date')).toDate()
    : new Date();
  const startOfSelectDate = dayjs(selectedDate).startOf('day').format(FORMAT_TIME_FULL);
  const endOfSelectDate = dayjs(selectedDate).endOf('day').format(FORMAT_TIME_FULL);

  const randomNumber = Number.parseInt(searchParams.get('random') || '1', 10) || 1;
  const { mutate: revalidateAppoinment } = useAppointments({
    startTime: startOfSelectDate,
    endTime: endOfSelectDate,
    orderType: EOrderType['OTHERS'],
    random: randomNumber,
  });
  const { data: meData } = useMe();
  const resource = meData?.resource || [];
  const isShowDeleteAppointment = hasPermission(resource, EResourceAccess['APPOINTMENT'], EPermission['DELETE']);

  const { onRefetch } = useContext(AppointmentCalendarContext);
  const [open, setOpen] = useState<boolean>(false);
  const [customerKeySearch, setCustomerKeySearch] = useState<string>('');
  const [serviceKeySearch, setServiceKeySearch] = useState<string>('');
  const debouncedSearch = useDebounce(customerKeySearch, 300);
  const [showWarningModal, setShowWarningModal] = useState<boolean>(false);

  const { data: employeeData } = useEmployees({});

  const [pagination, setPagination] = useState(1);

  // const { data: singleOrder, mutate } = useAppointmentSingleOrderById(singleOrderId);

  const { data: customerResponse } = useCustomer<TTransformPaginationResponse<TCustomer>>({
    keySearch: debouncedSearch,
    query: {
      pagination: {
        page: pagination,
        limit: SWRKey.CUSTOMER.LIMIT,
      },
    },
  });
  const customerData = customerResponse?.data;
  const maxPage = customerResponse?.pageCount || 2;

  const { onCreateAppointment } = useMutationAppointmentCreate();
  const { onUpdateAppointment } = useMutationAppointmentUpdate();
  const { onDeleteAppointmentEvent } = useMutationAppointmentEventDelete();
  const { onDeleteOrderDetail } = useMutationOrderDetailDelete();
  const { onUpdateAppointmentEvent } = useMutationAppointmentEventUpdate();
  const { onUpdateSingleOrder } = useMutationSingleOrderAppointmentDetail();

  const initValues: TFormValues = {
    status: OPTIONS_APPOINTMENT_DEFAULT,
    ...defaultValues,
  };

  const methods = useForm<TFormValues>({
    defaultValues: initValues,
    resolver: zodResolver(validationSchema),
  });

  const selectedCustomer = customerResponse?.data?.find(
    customerOption => customerOption?.id === methods?.watch('customer.id')
  );

  const product = methods?.watch('product');

  const credits = getCredits(selectedCustomer?.credits || []);

  const { data: serviceData } = useServices(serviceKeySearch, {
    filterOptions: [
      {
        field: 'isMember',
        value:
          (credits?.newCredit?.rawNewCredit && !credits?.newCredit?.isExpired) ||
          credits?.newCredit?.balance > 0 ||
          (credits?.oldCredit?.rawOldCredit && !credits?.newCredit?.isExpired) ||
          credits?.oldCredit?.balance > 0
            ? 'true'
            : 'false',
        operator: CondOperator.EQUALS,
      },
    ],
  });

  const { showError, showSuccess } = useAlert();

  const onSuccess = ({ successMessage }: { successMessage: string }) => {
    showSuccess({ title: successMessage });
    onRefetch();
  };

  const onCreate = async (data: TAppointmentCreateReq) => {
    const response = await onCreateAppointment(data);
    if (response.status === 'error') {
      const message = getErrorMessage(response?.message?.error);
      return showError({
        title: message,
      });
    }

    onSuccess({ successMessage: 'Created !' });
    onClose();
  };

  const onUpdate = async (
    { duration, ...dataToUpdateEvent }: TAppointmentEventUpdateReq,
    dataToUpdateAppointment: TAppointmentUpdateReq
  ) => {
    try {
      const promise = onUpdateSingleOrder({
        ...dataToUpdateEvent,
        status: dataToUpdateAppointment?.status,
      });

      await promise;
      await revalidateAppoinment();
      // const updateEventResponse = onUpdateAppointmentEvent(dataToUpdateEvent);
      // const updateAppointmentResponse = onUpdateAppointment(dataToUpdateAppointment);
      // const response = await Promise.all([updateEventResponse, updateAppointmentResponse]);
      // const hasError = !!response?.find(res => res.status === 'error');
      // if (hasError) {
      //   response.forEach(res => {
      //     if (res.status === 'error') {
      //       const message = getErrorMessage(res?.message?.error);
      //       return showError({ title: message });
      //     }
      //   });
      // } else {
      //   onSuccess({ successMessage: 'Updated !' });
      // }
      onClose();
    } catch (e) {
      console.log('AppointmentEventForm->onUpdate', e);
    }
  };
  const handleSubmit = async (values: TFormValues) => {
    try {
      const findService = serviceData?.find(service => service?.id === values?.product?.id);
      if (!findService) return showError({ title: 'Missing service' });
      // TODO: handle employee
      const formData = transformCommonValueSubmit({
        values: { ...values, service: findService },
        type,
      });
      if (type === 'create' && formData?.dataToCreate) {
        onCreate(formData?.dataToCreate);
        return;
      }

      if (
        type === 'edit' &&
        isEmpty(checkoutTime) &&
        orderItemId &&
        appointmentId &&
        employeeId &&
        formData?.dataToUpdateEvent
      ) {
        const dataToUpdateEvent: TAppointmentEventUpdateReq = {
          id: orderItemId,
          employeeId,
          ...formData?.dataToUpdateEvent,
        };

        const dataToUpdateAppointment: TAppointmentUpdateReq = {
          id: appointmentId,
          ...formData?.dataToUpdateAppointment,
        };

        onUpdate(dataToUpdateEvent, dataToUpdateAppointment);
      }
    } catch (e) {
      console.log('appointmentEventForm->handleSubmit', e);
    }
  };
  const handleConfirmModal = () => {
    methods.handleSubmit(handleSubmit)();
  };

  const onSave = async (values: TFormValues) => {
    try {
      const findService = serviceData?.find(service => service?.id === values?.product?.id);
      if (!findService) return showError({ title: 'Missing service' });
      const formData = transformCommonValueSubmit({
        values: { ...values, service: findService },
        type,
      });
      const checkDuplicateResponse = await checkDuplicateAppointment({
        startTime:
          (type === 'create' ? formData?.dataToCreate?.startTime : formData?.dataToUpdateEvent?.startTime) || '',
        endTime: (type === 'create' ? formData?.dataToCreate?.endTime : formData?.dataToUpdateEvent?.endTime) || '',
        customerId: formData?.dataToCreate?.customer?.id || formData?.dataToUpdateAppointment?.customer?.id || '',
        ...(appointmentId ? { appointmentId } : {}),
      });
      if (checkDuplicateResponse.status === 'success') {
        if (checkDuplicateResponse.data.isDuplicate) {
          setShowWarningModal(true);
          return;
        }
        methods.handleSubmit(handleSubmit)();
      }
    } catch (e) {
      console.log('appointmentEventForm->handleSubmit', e);
    }
  };

  const onDelete = () => {
    setOpen(true);
  };

  const onConfirmDelete = async () => {
    if (!orderItemId || type !== 'edit') return;
    if (defaultValues?.employee?.[0]?.id === WAITING_LIST_RESOURCE?.id) {
      const response = await onDeleteOrderDetail(orderItemId);
      if (response.status === 'error') return showError({ title: 'Error' });
    } else {
      const response = await onDeleteAppointmentEvent({
        employeeId: defaultValues?.employee?.[0]?.id || '',
        orderItemId,
      });
      if (response.status === 'error') return showError({ title: 'Error' });
    }
    onSuccess({ successMessage: 'Deleted !' });
    setOpen(false);
  };

  const onCancelDelete = () => {
    setOpen(false);
  };

  const phoneValue: string = useMemo(() => methods.watch('phone') as string, [methods.watch('phone')]);

  const { employeeOptions, customerOptions, serviceOptions } = useMemo(() => {
    const shouldAppend =
      type === 'edit' && customerData?.findIndex(item => item?.id === defaultValues?.customer?.id) === -1;
    const customerOptionsTemp =
      customerData?.map(customer => ({
        id: customer?.id,
        name: `${concatenateNames(customer?.firstName, customer?.lastName)} (${customer?.phone})`,
      })) || [];
    return {
      employeeOptions: [
        { id: WAITING_LIST_RESOURCE.id, name: WAITING_LIST_RESOURCE.title },
        ...(allEmployeeByServiceId?.find(i => i.serviceId === product?.id)?.employees || []),
      ],
      customerOptions: shouldAppend
        ? [...customerOptionsTemp, { id: defaultValues?.customer?.id || '', name: defaultValues?.customer?.name || '' }]
        : [...customerOptionsTemp],

      serviceOptions:
        serviceData
          ?.filter(service => (Number.parseInt(service?.duration?.name || '', 10) || 0) > 0)
          ?.map(service => ({
            id: service?.id,
            name: service?.name,
          })) || [],
    };
  }, [employeeData, customerData, serviceData, product, allEmployeeByServiceId]);

  useEffect(() => {
    const subscription = methods.watch((value, { name }) => {
      if (name === 'customer') {
        methods.setValue('phone', customerData?.find(customer => customer.id === value?.customer?.id)?.phone || '');
      }
      if (name === 'product') {
        const currProduct = serviceData?.find(service => service?.id === value?.product?.id);
        const startTime = dayjs(value.startTime || '').toISOString();
        const durationInMinutes = Number.parseInt(currProduct?.duration?.name || '', 10) || DEFAULT_DURATION;
        const endTime = dayjs(startTime).add(durationInMinutes, 'minutes').toISOString();
        methods.setValue('endTime', endTime);
      }
    });
    return () => subscription.unsubscribe();
  }, [methods.watch, JSON.stringify(customerData)]);

  useEffect(() => {
    methods.reset(initValues);
    return () => methods.reset({});
  }, [JSON.stringify(initValues)]);

  useEffect(() => {
    const productId = product?.id;
    if (productId !== initValues.product?.id && initValues.product?.id && productId) {
      methods.setValue('employee', []);
    }
  }, [product]);

  const onLoadMoreCustomer = () => {
    setPagination(pre => {
      const newPage = pre + 1;
      if (newPage > maxPage) return pre;
      return newPage;
    });
  };

  const onSearchCustomer = (event: any) => {
    setCustomerKeySearch(event?.target.value);
    setPagination(1);
  };

  const onSearchService = (event: any) => {
    setServiceKeySearch(event?.target.value);
  };

  return (
    <>
      <StyledWrapper>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography component="h5" variant="heading-xsmall-700" color={customPalettes?.neutrals?.N50?.main}>
            {type === 'create' ? 'New Appointment' : 'Appointment Info'}
          </Typography>
          <IconButton onClick={onClose} disableRipple sx={{ padding: 0 }}>
            <Icon variant="close" size={2} />
          </IconButton>
        </Stack>
        <Divider
          sx={(theme: Theme) => ({
            borderBottomWidth: theme.spacing(1 / 16),
            borderColor: theme.palette.neutrals.N180.main,
          })}
        />
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSave)}>
            <Stack gap={1.25}>
              {/* TODO Appointment Info */}
              <AppointmentEventFormBody
                type={type}
                phoneValues={phoneValue}
                status={(defaultValues?.status?.id || EAppointmentStatus.REQUEST) as EAppointmentStatus}
                page={pagination}
                onSearchCustomer={onSearchCustomer}
                onSearchService={onSearchService}
                onLoadMoreCustomer={onLoadMoreCustomer}
                options={{
                  employeeOptions,
                  customerOptions,
                  serviceOptions,
                }}
              />
              <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1}>
                <StyledButton variant="outlined" color="primary" label="Cancel" onClick={onClose} />
                <StyledButton
                  type="submit"
                  variant="contained"
                  color="primary"
                  label="Save"
                  disabled={!isEmpty(checkoutTime) && type === 'edit'}
                />
                {type === 'edit' && (
                  <>
                    {isShowDeleteAppointment && (
                      <IconButton disableRipple sx={{ padding: 0 }} onClick={() => onDelete()}>
                        <Icon variant="red_delete" size={2} />
                      </IconButton>
                    )}
                    <IconButton
                      disableRipple
                      sx={{ padding: 0 }}
                      onClick={() =>
                        router.push(
                          `${PATH_NAME.APPOINTMENT.detail({ id: appointmentId || '' })}&date=${dayjs(
                            defaultValues?.startTime
                          ).format('YYYY-MM-DD')}`
                        )
                      }
                    >
                      <Icon variant="bold_edit" size={2} />
                    </IconButton>
                  </>
                )}
                {type === 'create' && <StyledLink href={PATH_NAME.APPOINTMENT.create}>Advanced</StyledLink>}
              </Stack>
            </Stack>
          </form>
        </FormProvider>
      </StyledWrapper>
      {type === 'edit' && (
        <ConfirmModal
          isOpen={open}
          title="Confirm delete event"
          bodyContent="Do you want to delete this event?"
          onCancel={() => onCancelDelete()}
          onClose={() => onCancelDelete()}
          onConfirm={() => onConfirmDelete()}
        />
      )}
      <WarningModal
        isOpen={showWarningModal}
        title="Duplicate appointments"
        bodyContent={`The above appointment information has been created previously. Do you want to continue ${
          type === 'create' ? 'creating' : 'editing'
        }?`}
        onClose={() => setShowWarningModal(false)}
        onCancel={() => setShowWarningModal(false)}
        onConfirm={handleConfirmModal}
      />
    </>
  );
};
