import dayjs, { Dayjs } from 'dayjs';
import * as z from 'zod';

// Entire form validation schema using Zod
export const validationSchema = z.object({
  status: z
    .object({
      id: z.string().min(1),
      name: z.string().min(1),
    })
    .required(),
  product: z
    .object({
      id: z.string().min(1),
      name: z.string().min(1),
    })
    .required(),
  customer: z
    .object({
      id: z.string().min(1),
      name: z.string().min(1),
    })
    .required(),
  employee: z
    .any()
    // .object({
    //   id: z.string().min(1),
    //   name: z.string().min(1),
    // })
    .optional(),
  startTime: z
    .custom<Dayjs>(val => val instanceof dayjs, 'Invalid date')
    .or(z.string())
    .refine(data => dayjs(data).isValid(), { message: 'Invalid date' }),
  endTime: z.string(),
  phone: z.string(),
  remark: z.string(),
  // Other fields in your form...
});
