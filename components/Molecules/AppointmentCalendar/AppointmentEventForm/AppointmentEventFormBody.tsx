'use client';

import { Divider, Theme, autocompleteClasses, outlinedInputClasses } from '@mui/material';
import { selectClasses } from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import Icon from '@/components/Atoms/Icon';
import { IconVariantTypes } from '@/components/Atoms/Icon/Icon';
import { LightChevronDownIcon, SmallTimeIcon } from '@/components/Atoms/IconsComponent';
import Typography from '@/components/Atoms/Typography';
import { OPTIONS_APPOINTMENT } from '@/lib/constants/status';
import { EAppointmentStatus } from '@/lib/types/enum/appointment';
import { RHFAutocomplete } from '../../RHFItems/RHFAutocomplete/RHFAutocomplete';
import RHFSelect from '../../RHFItems/RHFSelect';
import { TSelectOption } from '../../RHFItems/RHFSelect/RHFSelect';
import RHFTextField from '../../RHFItems/RHFTextField';
import { StyledTimePicker } from './AppointmentEventForm.styled';

type TAppointmentEventFormBody = {
  type: 'create' | 'edit';
  status: EAppointmentStatus;
  phoneValues: string;
  onLoadMoreCustomer: () => void;
  onSearchCustomer: (event: any) => void;
  onSearchService: (event: any) => void;
  page: number;
  options: {
    employeeOptions: TSelectOption[];
    customerOptions: TSelectOption[];
    serviceOptions: TSelectOption[];
  };
};

export const AppointmentEventFormBody: React.FC<TAppointmentEventFormBody> = ({
  phoneValues,
  onSearchCustomer,
  onSearchService,
  page,
  options,
}) => {
  const { setValue, watch, getValues } = useFormContext();
  const { employeeOptions, customerOptions, serviceOptions } = options;

  const data = watch();
  const product = data?.product;
  const customer = data?.customer;
  const isDisableTherapist = !product?.id;
  const isDisableProduct = !customer?.id;

  const isSelectWaitingRef = useRef(false);

  const textWithIcon = ({ text, icon }: { text?: string; icon?: IconVariantTypes }) => (
    <>
      {icon && <Icon variant={icon} size={1.5} />}
      {text && (
        <Typography variant="body-medium-400" whiteSpace="nowrap" textOverflow="ellipsis">
          {text}
        </Typography>
      )}
    </>
  );

  return (
    <>
      <Stack gap={1}>
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
          {textWithIcon({ text: 'Status' })}
          <RHFSelect
            page={page}
            name="status"
            options={OPTIONS_APPOINTMENT}
            sx={{
              minWidth: '100%',
            }}
            showHelperText={false}
            formControlProps={{
              sx: (theme: Theme) => ({
                flex: 1,
                [`.${selectClasses.select}`]: {
                  padding: `${theme.spacing(0.5, 1.5)} !important`,
                },
                [`.${outlinedInputClasses.root} .${outlinedInputClasses.notchedOutline}`]: {
                  borderWidth: theme.spacing(1 / 8),
                  borderColor: theme.palette.neutrals.N260.main,
                },
                [`& .${outlinedInputClasses.input}`]: {
                  ...theme.typography['body-medium-400'],
                },
              }),
            }}
            dropdownIcon={LightChevronDownIcon}
          />
        </Stack>
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
          <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1}>
            {textWithIcon({ text: 'Start time', icon: 'clock' })}
          </Stack>
          <StyledTimePicker
            name="startTime"
            showHelperText={false}
            minTime={dayjs().startOf('day').add(9, 'hours')}
            slots={{
              openPickerIcon: SmallTimeIcon,
            }}
          />
        </Stack>
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
          <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1}>
            {textWithIcon({ text: 'Therapist', icon: 'profile' })}
          </Stack>
          <RHFSelect
            name="employee"
            // TODO : employeee here
            disabled={isDisableTherapist}
            options={employeeOptions}
            sx={{
              minWidth: '100%',
            }}
            isCustomOnChange
            multiple
            showCheckbox
            showHelperText={false}
            formControlProps={{
              sx: (theme: Theme) => ({
                flex: 1,
                [`.${selectClasses.select}`]: {
                  padding: `${theme.spacing(0.5, 1.5)} !important`,
                },
                [`.${outlinedInputClasses.root} .${outlinedInputClasses.notchedOutline}`]: {
                  borderWidth: theme.spacing(1 / 8),
                  borderColor: theme.palette.neutrals.N260.main,
                },
                [`& .${outlinedInputClasses.input}`]: {
                  ...theme.typography['body-medium-400'],
                },
              }),
            }}
            dropdownIcon={LightChevronDownIcon}
            runEffectMultiple={false}
            customOnChange={(newValues?: string[] | string) => {
              const waitingValue = '1';
              const allValue = 'all';
              const oldValue: string[] = getValues('employee');
              if (Array.isArray(newValues)) {
                const isSelect = newValues?.length > oldValue?.length;
                if (isSelect) {
                  const isSelectAll = newValues?.includes(allValue);
                  if (isSelectAll) {
                    const isDeselectAll = newValues?.length === employeeOptions?.length;
                    if (isDeselectAll) {
                      isSelectWaitingRef.current = true;
                      const newValue = employeeOptions.filter(o => o?.id === waitingValue);
                      return newValue?.map(i => i?.id) || [];
                    }
                    const newValue = employeeOptions.filter(o => o?.id !== waitingValue && o?.id !== allValue);
                    return newValue?.map(i => i?.id) || [];
                  }

                  const isSelectWaiting = newValues?.length === 0 || newValues?.includes(waitingValue);

                  const isSkipSelectWaiting =
                    isSelectWaitingRef.current &&
                    oldValue?.[0] === waitingValue &&
                    newValues?.includes(waitingValue) &&
                    newValues?.length === 2;

                  if (isSelectWaiting && !isSkipSelectWaiting) {
                    isSelectWaitingRef.current = true;
                    const newValue = employeeOptions.filter(o => o?.id === waitingValue);
                    return newValue?.map(i => i?.id) || [];
                  }

                  isSelectWaitingRef.current = false;
                  const newValue = employeeOptions.filter(
                    o => newValues?.includes(o?.id) && o?.id !== waitingValue && o?.id !== allValue
                  );
                  return newValue?.map(i => i?.id) || [];
                }
                const isDeselectWaitingValue = newValues?.length === 0;
                if (isDeselectWaitingValue) {
                  const isOnlyWaitingOptions = employeeOptions?.length === 1;
                  if (isOnlyWaitingOptions) {
                    isSelectWaitingRef.current = false;
                    return [];
                  }
                  isSelectWaitingRef.current = true;
                  const newValue = employeeOptions.filter(o => o?.id === waitingValue);
                  return newValue?.map(i => i?.id) || [];
                }
                isSelectWaitingRef.current = false;
                const newValue = employeeOptions.filter(o => newValues?.includes(o?.id));
                return newValue?.map(i => i?.id) || [];
              }
            }}
          />
        </Stack>
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
          {textWithIcon({ icon: 'customer_black' })}
          <RHFAutocomplete
            name="customer"
            options={customerOptions}
            sx={theme => ({
              minWidth: '100%',
            })}
            textfieldProps={{
              sx: (theme: Theme) => ({
                [`.${outlinedInputClasses.root}`]: {
                  padding: `${theme.spacing(0.5, 4.875, 0.5, 1.5)} !important`,
                },
                [`.${autocompleteClasses.inputRoot} .${autocompleteClasses.input}`]: {
                  width: '100%',
                },
                [`.${outlinedInputClasses.root} .${autocompleteClasses.input}`]: {
                  fontSize: '0.875rem !important',
                  height: 'auto',
                },
                [`.${outlinedInputClasses.root} .${autocompleteClasses.endAdornment}`]: {
                  right: theme.spacing(1.5),
                  top: 'auto',
                  button: {
                    color: theme.palette.neutrals.N50.main,
                  },
                },
              }),
              onChange: onSearchCustomer,
            }}
            disableCloseOnSelect={false}
            freeSolo={false}
            popupIcon={<LightChevronDownIcon />}
            clearIcon={<></>}
            showHelperText={false}
            showCheckbox={false}
            multiple={false}
          />
        </Stack>
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
          {textWithIcon({ icon: 'bookmark' })}
          <RHFAutocomplete
            name="product"
            disabled={isDisableProduct}
            options={watch('customer')?.id ? serviceOptions : []}
            sx={theme => ({
              minWidth: '100%',
            })}
            textfieldProps={{
              sx: (theme: Theme) => ({
                [`.${outlinedInputClasses.root}`]: {
                  padding: `${theme.spacing(0.5, 4.875, 0.5, 1.5)} !important`,
                },
                [`.${autocompleteClasses.inputRoot} .${autocompleteClasses.input}`]: {
                  width: '100%',
                },
                [`.${outlinedInputClasses.root} .${autocompleteClasses.input}`]: {
                  fontSize: '0.875rem !important',
                  height: 'auto',
                },
                [`.${outlinedInputClasses.root} .${autocompleteClasses.endAdornment}`]: {
                  right: theme.spacing(1.5),
                  top: 'auto',
                  button: {
                    color: theme.palette.neutrals.N50.main,
                  },
                },
              }),
              onChange: onSearchService,
            }}
            disableCloseOnSelect={false}
            freeSolo={false}
            popupIcon={<LightChevronDownIcon />}
            clearIcon={<></>}
            showHelperText={false}
            showCheckbox={false}
            multiple={false}
          />
        </Stack>
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1}>
          {textWithIcon({ icon: 'phone', text: phoneValues })}
        </Stack>
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
          {textWithIcon({ text: '', icon: 'edit' })}
          <RHFTextField
            name="remark"
            sx={{
              minWidth: '100%',
            }}
            showHelperText={false}
            multiline
            maxRows={2}
            formControlProps={{
              sx: (theme: Theme) => ({
                flex: 1,
                [`.${selectClasses.select}`]: {
                  padding: `${theme.spacing(0.5, 1.5)} !important`,
                },
                [`.${outlinedInputClasses.root} .${outlinedInputClasses.notchedOutline}`]: {
                  borderWidth: theme.spacing(1 / 8),
                  borderColor: theme.palette.neutrals.N260.main,
                },
                [`& .${outlinedInputClasses.input}`]: {
                  ...theme.typography['body-medium-400'],
                },
              }),
            }}
          />
        </Stack>
      </Stack>
      <Divider
        sx={(theme: Theme) => ({
          borderBottomWidth: theme.spacing(1 / 16),
          borderColor: theme.palette.neutrals.N180.main,
        })}
      />
    </>
  );
};
