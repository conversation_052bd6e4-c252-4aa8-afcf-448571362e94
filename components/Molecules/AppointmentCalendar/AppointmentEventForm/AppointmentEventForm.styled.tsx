import styled from '@emotion/styled';
import { buttonClasses } from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Link from 'next/link';
import { customTypography } from '@/theme/customTypography';
import Button from '@/components/Atoms/Button';
import RHFTimePicker from '../../RHFItems/RHFTimePicker';

export const StyledWrapper = styled(Stack)(({ theme }) => ({
  width: theme.spacing(240 / 8),
  position: 'relative',
  padding: theme.spacing(1),
  gap: theme.spacing(1),
  backgroundColor: theme.palette.common.white,
  color: theme.palette.neutrals.N50.main,
  borderRadius: theme.spacing(5 / 8),
  overflow: 'hidden',
  boxShadow: '4px 4px 4px 0px rgba(0, 0, 0, 0.05)',
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    padding: theme.spacing(0.25, 1.5),
    ...theme.typography['body-small-400'],
  },
}));

export const StyledLink = styled(Link)(({ theme }) => ({
  color: theme.palette.info.main,
  textDecoration: 'none',
  ...theme.typography['body-small-400'],
}));

export const StyledTimePicker = styled(RHFTimePicker)(({ theme }) => ({
  background: theme.palette.common.white,
  borderRadius: theme.spacing(1.25),
  outline: 'none',
  height: theme.spacing(25 / 8),
  '.MuiInputBase-root': {
    borderRadius: theme.spacing(1.25),
    ...customTypography['body-medium-400'],
  },
  '.MuiInputBase-root input': {
    padding: 0,
    paddingLeft: theme.spacing(1.5),
    paddingRight: theme.spacing(1.5),
    height: theme.spacing(25 / 8),
  },
  '.MuiOutlinedInput-notchedOutline': {
    top: theme.spacing(-5 / 8),
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N180.main,
  },
  '.MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline': {
    borderWidth: `${theme.spacing(1 / 16)} !important`,
    borderColor: theme.palette.neutrals.N50.main,
  },
  '.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderWidth: `${theme.spacing(1 / 8)} !important`,
    borderColor: theme.palette.neutrals.N260.main,
  },
}));
