import dayjs from 'dayjs';
import { DEFAULT_DURATION } from '@/lib/constants/dateTime';
import { WAITING_LIST_RESOURCE } from '@/lib/constants/dateType';
import {
  TAppointmentCreateReq,
  TAppointmentEventUpdateReq,
  TAppointmentUpdateReq,
} from '@/lib/types/entities/appointment';
import { TOrderItem } from '@/lib/types/entities/order';
import { TService } from '@/lib/types/entities/service';
import { EAppointmentStatus } from '@/lib/types/enum/appointment';
import { EOrderType } from '@/lib/types/enum/order';
import { TFormValues } from './AppointmentEventForm.types';

export const transformCommonValueSubmit = ({
  values,
  type,
}: {
  values: TFormValues & { service: TService };
  type: 'create' | 'edit';
}) => {
  const { service } = values;
  const durationInMinutes = Number.parseInt(service?.duration?.name || '', 10) || DEFAULT_DURATION;
  const startTime = dayjs(values.startTime || '').toISOString();
  const endTime = dayjs(startTime).add(durationInMinutes, 'minutes').toISOString();

  const employees =
    values?.employee
      ?.filter(item => item !== WAITING_LIST_RESOURCE?.id)
      ?.map(e => ({
        id: typeof e === 'string' ? e : e?.id,
      })) || [];

  const status = values?.status?.id as EAppointmentStatus;

  const duration = {
    id: service?.duration?.id || '',
  };

  const product = {
    id: service?.id || '',
  };

  const customer = {
    id: values?.customer?.id || '',
  };

  if (type === 'create') {
    const ordersCreate: TOrderItem = {
      startTime,
      endTime,
      product,
      duration,
      employees,
      note: values?.remark || '',
      quantity: 1,
      price: service?.price || 0,
    };

    const dataToCreate: TAppointmentCreateReq = {
      startTime,
      endTime,
      customer,
      orders: [
        {
          orderType: EOrderType['OTHERS'],
          items: [ordersCreate],
        },
      ],
      status,
    };
    return { dataToCreate };
  }

  if (type === 'edit') {
    const dataToUpdateEvent: Omit<TAppointmentEventUpdateReq, 'id' | 'employeeId'> = {
      startTime,
      endTime,
      product,
      duration,
      employees,
      note: values?.remark || '',
    };

    const dataToUpdateAppointment: Omit<TAppointmentUpdateReq, 'id'> = {
      status,
      customer,
    };
    return { dataToUpdateEvent, dataToUpdateAppointment };
  }
};
