import styled from '@emotion/styled';
import { alpha } from '@mui/material';
import Button from '@mui/material/Button';
import AtomButton from '@/components/Atoms/Button';

export const StyledWrapper = styled(Button)<{
  isTransparent?: boolean;
  bgColor?: string;
  focused?: boolean;
  canAction?: boolean;
}>(({ theme, isTransparent, bgColor, focused, canAction }) => {
  let backgroundColor = canAction ? theme.palette.common.white : theme.palette.neutrals.N260.main;
  if (isTransparent) {
    backgroundColor = 'transparent';
  } else if (focused && bgColor) {
    backgroundColor = alpha(bgColor, 0.6);
  }

  return {
    width: '100%',
    minWidth: theme.spacing(38 / 8),
    position: 'relative',
    minHeight: `${theme.spacing(15 / 8)} !important`,
    alignItems: 'flex-start',
    borderBottom: 'none',
    borderRadius: 0,
    padding: 0,
    backgroundColor,
    color: theme.palette.neutrals.N90.main,
    textTransform: 'none',
    overflow: 'hidden',
    textAlign: 'left',
    zIndex: 3,
    boxShadow: 'none',
    ...theme.typography['heading-xsmall-700'],
    '&:hover': {
      backgroundColor: bgColor && canAction ? alpha(bgColor, 0.6) : theme.palette.neutrals.N260.main,
    },
    [theme.breakpoints.up('xl')]: {
      width: '100%',
      minWidth: theme.spacing(38 / 8),
    },
  };
});

export const NoBorderButton = styled(AtomButton)(() => ({
  border: 'none !important',
  maxWidth: '206px',
  width: '100%',
  borderRadius: '0',
  justifyContent: 'start',
}));
