import Popover from '@mui/material/Popover';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import React, { useContext, useState } from 'react';
import { IconVariantTypes } from '@/components/Atoms/Icon';
import { WAITING_LIST_RESOURCE } from '@/lib/constants/dateType';
import { useMe } from '@/lib/hooks/queries/me';
import { TEmployee } from '@/lib/types/entities/employee';
import { EPermission, EResourceAccess } from '@/lib/types/enum/role';
import { hasPermission } from '@/lib/utils/role';
import { isInWorkingHours } from '@/lib/utils/workingHour';
import { AppointmentCalendarContext } from '../AppointmentCalendar.context';
import AppointmentEventForm from '../AppointmentEventForm';
import AddTimeForm from './AddTimeForm';
import { NoBorderButton, StyledWrapper } from './TimeSlotWrapper.styled';
import { EAppointmentStatus } from '@/lib/types/enum/appointment';
import { APPOINTMENT_STATUS_COLORS } from '@/lib/constants/status';

type TOption = 'appointment' | 'block' | 'breakTime' | 'menu';

const menuOptions: { icon: IconVariantTypes; label: string; key: TOption }[] = [
  { icon: 'calendar_black', label: 'Add Appointment', key: 'appointment' },
  { icon: 'cup_of_tea', label: 'Add break time', key: 'breakTime' },
  { icon: 'lock_black', label: 'Add block', key: 'block' },
];

const Menu = ({ handleClick }: { handleClick: (key: TOption) => void }) => (
  <Stack>
    {menuOptions?.map(({ label, key, icon }) => (
      <NoBorderButton startIcon={icon} key={key} onClick={() => handleClick(key)} label={label} />
    ))}
  </Stack>
);

const PopoverChildren = ({
  handleClickMenuOption,
  handleClose,
  modalState,
  props,
  employeeId,
  employeesJoinService,
}: any) => {
  switch (modalState.option) {
    case 'menu':
      return <Menu handleClick={handleClickMenuOption} />;
    case 'block':
      return (
        <AddTimeForm
          appointmentType="blocktime"
          title="Block Time"
          defaultValues={modalState?.data}
          handleClose={handleClose}
          employeeId={employeeId}
          type="create"
        />
      );
    case 'breakTime':
      return (
        <AddTimeForm
          appointmentType="breaktime"
          title="Break Time"
          defaultValues={modalState?.data}
          handleClose={handleClose}
          employeeId={employeeId}
          type="create"
        />
      );
    case 'appointment':
      return (
        <AppointmentEventForm
          defaultValues={{
            startTime: dayjs(props?.value)
              .toDate()
              .toISOString(),
            endTime: dayjs(props?.value)
              .add(15, 'minutes')
              .toDate()
              .toISOString(),
            remark: '',
            employee: employeesJoinService?.map((e: TEmployee) => ({
              id: e?.id || '',
              name: e?.displayName || '',
            })),
          }}
          type="create"
          onClose={handleClose}
        />
      );
    default:
      return null;
  }
};

export const TimeSlotWrapper: React.FC = (props: any) => {
  const resourceEventId = props?.resource;
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [modalState, setModalState] = useState<{ option: TOption; data?: { startTime: string; endTime: string } }>({
    option: 'menu',
    data: undefined,
  });
  const { eventResources, appointmentEvents } = useContext(AppointmentCalendarContext);
  const open = Boolean(anchorEl);
  const id = open ? `appointment-event-${Math.round(Math.random() * 100)}` : undefined;
  const resourceInfo = eventResources?.find(eventResource => eventResource.id === resourceEventId);
  const employeesJoinService =
    appointmentEvents.find(ev => ev?.resourceId === resourceEventId)?.employeesJoinService || [];
  const { data: meData } = useMe();
  const resource = meData?.resource || [];
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    const { value: startTimeDate } = props;
    const startTime = dayjs(startTimeDate).toDate().toISOString();
    const endTime = dayjs(startTimeDate).add(15, 'minute').toDate().toISOString();
    if (
      resourceEventId &&
      (isInWorkingHours(new Date(startTimeDate), resourceInfo?.workingHours || []) ||
        resourceEventId === WAITING_LIST_RESOURCE?.id) &&
      hasPermission(resource, EResourceAccess['APPOINTMENT'], EPermission['CREATE'])
    ) {
      setModalState({
        option: 'menu',
        data: {
          startTime,
          endTime,
        },
      });
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const startTimeHour = dayjs(props?.value).hour();

  const handleClickMenuOption = (key: TOption) => setModalState(prev => ({ ...prev, option: key }));

  return (
    <>
      <StyledWrapper
        id={`time-slot-${startTimeHour}`}
        variant="contained"
        disableRipple
        disableTouchRipple
        disableFocusRipple
        disableElevation
        isTransparent={!resourceEventId}
        canAction={
          isInWorkingHours(new Date(props?.value), resourceInfo?.workingHours || []) ||
          resourceEventId === WAITING_LIST_RESOURCE?.id
        }
        bgColor={resourceInfo?.color}
        focused={open}
        onClick={handleClick}
      >
        {props?.children}
      </StyledWrapper>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={theme => ({
          marginLeft: theme.spacing(0.15),
        })}
        disableRestoreFocus
      >
        <PopoverChildren
          modalState={modalState}
          handleClickMenuOption={handleClickMenuOption}
          handleClose={handleClose}
          employeesJoinService={employeesJoinService}
          props={props}
          employeeId={resourceEventId}
        />
      </Popover>
    </>
  );
};
