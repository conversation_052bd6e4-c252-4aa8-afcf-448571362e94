import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import { buttonClasses } from '@mui/material/Button';
import Button from '@/components/Atoms/Button';

export const StyledWrapper = styled(Stack)(({ theme }) => ({
  width: theme.spacing(240 / 8),
  position: 'relative',
  padding: theme.spacing(1),
  gap: theme.spacing(1),
  backgroundColor: theme.palette.common.white,
  color: theme.palette.neutrals.N50.main,
  borderRadius: theme.spacing(5 / 8),
  overflow: 'hidden',
  boxShadow: '4px 4px 4px 0px rgba(0, 0, 0, 0.05)',
}));

export const StyledFormContainer = styled(Stack)(({ theme }) => ({
  gap: theme.spacing(0.5),
  padding: theme.spacing(1),
}));

export const StyledButtonWrapper = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  spacing: theme.spacing(2),
  justifyContent: 'flex-end',
  padding: `8px 0`,
  gap: theme.spacing(1.5),
  borderTop: '1px solid #ccc',
  margin: '0 10px',
  alignItems: 'center',
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    padding: theme.spacing(0.25, 1.5),
    ...theme.typography['body-small-400'],
  },
}));

export const StyledTitle = styled('p')(({ theme }) => ({
  paddingTop: theme.spacing(2),
  paddingBottom: 0,
  borderBottom: '1px solid #ccc',
  margin: '0 10px',
}));
