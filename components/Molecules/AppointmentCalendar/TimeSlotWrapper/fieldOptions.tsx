import { Dayjs } from 'dayjs';
import { TimeView } from '@mui/x-date-pickers';
import * as z from 'zod';
import { Theme } from '@mui/material';
import { TRHFTimepickerProps } from '@/components/Molecules/RHFItems/RHFItems.types';
import { customTypography } from '@/theme/customTypography';
import { SmallTimeIcon } from '@/components/Atoms/IconsComponent';

type TTimeFiels = 'startTime' | 'endTime';

export type TKeyOfForm = TTimeFiels;

type TRHFCalendarOption = Record<TTimeFiels, TRHFTimepickerProps>;

export type TFieldOptions = TRHFCalendarOption;

const shouldDisableTime = (value: Dayjs, view: TimeView) => {
  if (view === 'hours') {
    const hour = value.hour();
    return hour < 9 || hour > 23;
  }
  return false;
};

const containerSx = {
  '> .MuiStack-root': {
    '>.MuiFormControl-root': {
      flexDirection: 'row',
      alignItems: 'center',
      gap: '12px',
      span: {
        minWidth: '100px',
        mb: 0,
      },
      '> .MuiFormControl-root': {
        flex: 1,
      },
    },
    '> .MuiStack-root': {
      minHeight: 'fit-content',
    },
  },
};
const timePickerSx = (theme: Theme) => ({
  background: theme.palette.common.white,
  borderRadius: theme.spacing(1.25),
  outline: 'none',
  height: theme.spacing(25 / 8),
  '.MuiInputBase-root': {
    borderRadius: theme.spacing(1.25),
    ...customTypography['body-medium-400'],
    '.MuiInputAdornment-root button svg': {
      width: '16px',
      height: '16px',
    },
  },
  '.MuiInputBase-root input': {
    padding: 0,
    paddingLeft: theme.spacing(1.5),
    paddingRight: theme.spacing(1.5),
    height: theme.spacing(25 / 8),
  },
  '.MuiOutlinedInput-notchedOutline': {
    top: theme.spacing(-5 / 8),
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N180.main,
  },
  '.MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline': {
    borderWidth: `${theme.spacing(1 / 16)} !important`,
    borderColor: theme.palette.neutrals.N50.main,
  },
  '.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderWidth: `${theme.spacing(1 / 8)} !important`,
    borderColor: theme.palette.neutrals.N260.main,
  },
});

export const fieldOptions: TFieldOptions = {
  startTime: {
    type: 'timepicker',
    containerProps: {
      sx: containerSx,
    },
    attributes: {
      name: 'startTime',
      label: (
        <>
          <SmallTimeIcon style={{ marginRight: '6px' }} /> From
        </>
      ),
      shouldDisableTime,
      sx: timePickerSx,
    },
  },
  endTime: {
    type: 'timepicker',
    containerProps: {
      sx: containerSx,
    },
    attributes: {
      name: 'endTime',
      label: (
        <>
          <SmallTimeIcon style={{ marginRight: '6px' }} /> To
        </>
      ),
      shouldDisableTime,
      sx: timePickerSx,
    },
  },
};

export const addTimeSchema = z
  .object({
    startTime: z.any().refine(time => time.hour() >= 9 && time.hour() <= 24, {
      message: 'Start time must be between 9:00 and 24:00.',
    }),
    endTime: z.any().refine(time => time.hour() >= 9 && time.hour() <= 24, {
      message: 'End time must be between 9:00 and 24:00.',
    }),
  })
  .refine(data => data.endTime.isAfter(data.startTime), {
    message: 'End time must be after start time.',
    path: ['endTime'], // Assign error to the `endTime` field
  });
