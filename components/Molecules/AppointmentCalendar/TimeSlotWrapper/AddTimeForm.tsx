import { zodResolver } from '@hookform/resolvers/zod';
import { Divider, IconButton, Theme } from '@mui/material';
import Stack from '@mui/material/Stack';
import dayjs, { Dayjs } from 'dayjs';
import { useSession } from 'next-auth/react';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useSearchParams } from 'next/navigation';
import { customPalettes } from '@/theme/customPalettes';
import {
  useMutationAppointmentCreateBreakOrBlockTime,
  useMutationAppointmentDeleteBreakOrBlockTime,
  useMutationAppointmentUpdateBreakOrBlockTime,
} from '@/lib/hooks/mutation/appointment';
import Typography from '@/components/Atoms/Typography';
import Icon from '@/components/Atoms/Icon';
import RHFItem from '../../RHFItems/RHFItems';
import { StyledWrapper } from '../AppointmentCalendar.styled';
import { StyledButton, StyledButtonWrapper, StyledFormContainer } from './AddTimeForm.styled';
import { addTimeSchema, fieldOptions, TFieldOptions } from './fieldOptions';
import { useBreakAndBlockTime } from '@/lib/hooks/queries/appointment';
import { FORMAT_TIME_FULL } from '@/lib/constants/dateTime';

type TFormValues = any;
const AddTimeForm = ({
  title,
  defaultValues,
  handleClose,
  type = 'edit',
  appointmentType,
  employeeId,
}: {
  employeeId?: string;
  title: string;
  handleClose: () => void;
  appointmentType: 'breaktime' | 'blocktime';
  defaultValues?: {
    startTime: string;
    endTime: string;
    id?: string;
  };
  type?: 'create' | 'edit';
}) => {
  const searchParams = useSearchParams();
  const selectedDate = dayjs(searchParams.get('date')).isValid()
    ? dayjs(searchParams.get('date')).toDate()
    : new Date();

  const { data: session } = useSession();
  const currentBranchId = session?.user?.branchChosen?.id || '';
  const { mutate: revalidateBreakAndBlockTime } = useBreakAndBlockTime({
    startTime: dayjs(selectedDate).startOf('day').toDate().toISOString(),
    endTime: dayjs(selectedDate).endOf('day').toDate().toISOString(),
    branchId: currentBranchId,
  });

  const { trigger: ondeleteBreakOrBlockTime, isMutating: isDeleting } = useMutationAppointmentDeleteBreakOrBlockTime();
  const { trigger: triggerUpdateBreakOrBlock, isMutating: isUpdating } = useMutationAppointmentUpdateBreakOrBlockTime();

  const { onCreateAppointmentBreakOrBlock, isMutating } = useMutationAppointmentCreateBreakOrBlockTime();
  const initValues: TFormValues = {
    startTime: defaultValues?.startTime || dayjs(),
    endTime: defaultValues?.endTime || dayjs().add(15, 'minute'),
  };

  const methods = useForm<TFormValues>({
    defaultValues: initValues,
    resolver: zodResolver(addTimeSchema),
  });

  const {
    formState: { isSubmitting },
    reset,
  } = methods;

  const isLoading = isSubmitting || isMutating || isDeleting || isUpdating;

  useEffect(() => {
    if (!defaultValues?.endTime) return;
    const endTime = dayjs(defaultValues?.endTime);
    const startTime = dayjs(defaultValues?.startTime);
    reset({
      startTime,
      endTime,
    });
  }, [defaultValues]);

  const handleSubmit = async (value: { startTime: Dayjs; endTime: Dayjs }) => {
    const isWaiting = employeeId === '1';

    if (type === 'create') {
      await onCreateAppointmentBreakOrBlock({
        startTime: dayjs(value?.startTime).toISOString(),
        endTime: dayjs(value?.endTime).toISOString(),
        type: appointmentType,
        branchId: currentBranchId,
        ...(!isWaiting ? { employeeId } : {}),
      });
    } else {
      await triggerUpdateBreakOrBlock({
        startTime: dayjs(value?.startTime).toISOString(),
        endTime: dayjs(value?.endTime).toISOString(),
        type: appointmentType,
        branchId: currentBranchId,
        id: defaultValues?.id,
        ...(!isWaiting ? { employeeId } : {}),
      });
    }
    revalidateBreakAndBlockTime();
    handleClose?.();
  };

  const handleDelete = async () => {
    if (!defaultValues?.id) return;
    await ondeleteBreakOrBlockTime(defaultValues?.id);
    revalidateBreakAndBlockTime();
    handleClose?.();
  };

  return (
    <StyledWrapper>
      <Stack direction="row" justifyContent="space-between" alignItems="center" m="8px">
        <Typography component="h5" variant="heading-xsmall-700" color={customPalettes?.neutrals?.N50?.main}>
          {type === 'create' ? `New ${title}` : `Edit ${title}`}
        </Typography>
        <IconButton onClick={handleClose} disableRipple sx={{ padding: 0 }}>
          <Icon variant="close" size={2} />
        </IconButton>
      </Stack>
      <Divider
        sx={(theme: Theme) => ({
          borderBottomWidth: theme.spacing(1 / 16),
          borderColor: theme.palette.neutrals.N180.main,
        })}
      />
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handleSubmit)} style={{ height: '100%' }}>
          <Stack height="100%" justifyContent="space-between">
            <StyledFormContainer>
              {(Object.keys(fieldOptions) as Array<keyof TFieldOptions>).map(fieldName => {
                const { type, attributes, containerProps } = fieldOptions[fieldName];
                return <RHFItem key={fieldName} type={type} attributes={attributes} containerProps={containerProps} />;
              })}
            </StyledFormContainer>

            <StyledButtonWrapper>
              <StyledButton
                variant="outlined"
                label="Cancel"
                size="large"
                isLoading={isLoading}
                loadingText="Cancel"
                onClick={handleClose}
              />
              <StyledButton
                variant="contained"
                type="submit"
                label="Save"
                size="large"
                isLoading={isLoading}
                loadingText="Saving"
              />

              {type === 'edit' && (
                <IconButton disableRipple sx={{ padding: 0 }} onClick={handleDelete}>
                  <Icon variant="red_delete" size={2} />
                </IconButton>
              )}
            </StyledButtonWrapper>
          </Stack>
        </form>
      </FormProvider>
    </StyledWrapper>
  );
};

export default AddTimeForm;
