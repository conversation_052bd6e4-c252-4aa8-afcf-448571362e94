import { z } from 'zod';
import { TRHFPasswordProps, TRHFTextFieldProps } from '../../RHFItems/RHFItems.types';

type TTextfields = 'username';
type TPasswordfield = 'password';

type TRHFTextfieldOption = Record<TTextfields, TRHFTextFieldProps>;
type TRHFPasswordOption = Record<TPasswordfield, TRHFPasswordProps>;

type TFieldOptions = TRHFTextfieldOption & TRHFPasswordOption;

export const schemaForm = z.object({
  username: z.string().trim().min(1).email(),
  password: z.string().min(1),
});

export const fieldOptions: TFieldOptions = {
  username: {
    type: 'textfield',
    attributes: {
      name: 'username',
      label: 'Email',
      placeholder: 'Email Address *',
      size: 'medium',
    },
  },
  password: {
    type: 'password',
    attributes: {
      name: 'password',
      size: 'medium',
      label: 'Password',
      placeholder: 'Password *',
    },
  },
};
