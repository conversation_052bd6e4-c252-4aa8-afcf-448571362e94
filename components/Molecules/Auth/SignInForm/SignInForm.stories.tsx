import { Meta, StoryObj } from '@storybook/react';
import { SignInForm } from './SignInForm';

export default {
  title: 'Molecules/Auth/SignInForm',
  component: SignInForm,
  tags: ['autodocs'],
} satisfies Meta;

type Story = StoryObj<typeof SignInForm>;

export const Default: Story = {
  args: {},
  render: (args: any) => {
    const onSignIn = () => {
      console.log('signIn');
    };
    return (
      <div>
        <SignInForm onSignIn={() => {}} isSigningIn={false} />
      </div>
    );
  },
};
