import styled from '@emotion/styled';
import Paper from '@mui/material/Paper';
import Link from 'next/link';
import Typography from '@/components/Atoms/Typography';

export const StyledPaperWrapper = styled(Paper)(({ theme }) => ({
  width: theme.spacing(54),
  padding: theme.spacing(4),
  [theme.breakpoints.down('sm')]: {
    width: '80vw',
    padding: theme.spacing(1),
  },
}));

export const StyledHeading = styled(Typography)(({ theme }) => ({
  color: theme.palette.neutrals.N50.main,
}));

export const StyledForgetPasswordLink = styled(Link)(({ theme }) => ({
  ...theme.typography['body-medium-400'],
  color: theme.palette.neutrals.N110.main,
}));
