'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Stack from '@mui/material/Stack';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import Button from '@/components/Atoms/Button';
import RHFItem from '../../RHFItems/RHFItems';
import { StyledForgetPasswordLink, StyledHeading, StyledPaperWrapper } from './SignInForm.styled';
import { TSignInFields, TSignInFormProps } from './SignInForm.types';
import { fieldOptions, schemaForm } from './fieldOptions';
import { EPathnames } from '@/lib/types/enum/pathnames';

export const SignInForm: React.FC<TSignInFormProps> = ({ isSigningIn, onSignIn }) => {
  const params = useSearchParams();

  const error = params.get('error');

  const methods = useForm<TSignInFields>({
    defaultValues: { username: '', password: '', isRemember: false },
    resolver: zodResolver(schemaForm),
  });

  const {
    formState: { isSubmitting },
    setError,
  } = methods;

  const handleOnSign = (values: TSignInFields) => {
    onSignIn(values);
  };

  useEffect(() => {
    if (error) {
      setError('username', { message: 'Username or password is incorrect' });
      setError('password', { message: 'Username or password is incorrect' });
    }
  }, [error]);

  return (
    <StyledPaperWrapper>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handleOnSign)}>
          <Stack gap={6}>
            <StyledHeading variant="heading-xxlarge-700" textAlign="center">
              Sign in
            </StyledHeading>
            <Stack gap={3}>
              {(Object.keys(fieldOptions) as Array<keyof typeof fieldOptions>).map(fieldName => (
                <RHFItem key={fieldName} {...fieldOptions[fieldName]} />
              ))}
              <Stack direction="row" alignItems="center" justifyContent="flex-end">
                <StyledForgetPasswordLink href={EPathnames.FORGET_PASSWORD}>Forgot password</StyledForgetPasswordLink>
              </Stack>
            </Stack>
            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                variant="contained"
                type="submit"
                label="Sign in"
                size="large"
                isLoading={isSigningIn}
                loadingText="Signing in"
              />
            </Stack>
          </Stack>
        </form>
      </FormProvider>
    </StyledPaperWrapper>
  );
};
