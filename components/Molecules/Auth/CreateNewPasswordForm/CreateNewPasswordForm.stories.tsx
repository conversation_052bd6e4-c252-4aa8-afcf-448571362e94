import { Meta, StoryObj } from '@storybook/react';
import { CreateNewPasswordForm } from './CreateNewPasswordForm';
import { TCreateNewPasswordFormProps } from './CreateNewPasswordForm.types';

export default {
  title: 'Molecules/Auth/CreateNewPassword',
  component: CreateNewPasswordForm,
  tags: ['autodocs'],
} satisfies Meta<TCreateNewPasswordFormProps>;

type Story = StoryObj<typeof CreateNewPasswordForm>;

export const Default: Story = {
  args: {
    onReset: () => {},
    isResetting: false,
  },
  render: (args: any) => (
    <div
      style={{
        width: '100%',
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#B4A093',
      }}
    >
      <CreateNewPasswordForm {...args} />
    </div>
  ),
};
