'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Stack from '@mui/material/Stack';
import { FormProvider, useForm } from 'react-hook-form';
import Button from '@/components/Atoms/Button';
import RHFItem from '../../RHFItems/RHFItems';
import { fieldOptions, schemaForm } from './fieldOptions';
import { StyledPaperWrapper, StyledHeading } from './CreateNewPasswordForm.styled';
import { TCreateNewPasswordFields, TCreateNewPasswordFormProps } from './CreateNewPasswordForm.types';

export const CreateNewPasswordForm: React.FC<TCreateNewPasswordFormProps> = ({ onReset, isResetting }) => {
  const methods = useForm<TCreateNewPasswordFields>({
    defaultValues: { newPassword: '', reNewPassword: '' },
    resolver: zodResolver(schemaForm),
  });

  const {
    formState: { isSubmitting },
  } = methods;

  const handleOnCreateNewPassword = (values: TCreateNewPasswordFields) => {
    onReset(values.newPassword);
  };

  return (
    <StyledPaperWrapper>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handleOnCreateNewPassword)}>
          <Stack gap={1}>
            <Stack gap={6}>
              <StyledHeading variant="heading-xxlarge-700" textAlign="center">
                Create new password
              </StyledHeading>
              <Stack gap={3}>
                {(Object.keys(fieldOptions) as Array<keyof typeof fieldOptions>).map(fieldName => (
                  <RHFItem key={fieldName} {...fieldOptions[fieldName]} />
                ))}
              </Stack>
              <Stack direction="row" spacing={2} justifyContent="center">
                <Button
                  variant="contained"
                  type="submit"
                  label="Reset"
                  size="large"
                  isLoading={isResetting}
                  loadingText="Resetting"
                />
              </Stack>
            </Stack>
          </Stack>
        </form>
      </FormProvider>
    </StyledPaperWrapper>
  );
};
