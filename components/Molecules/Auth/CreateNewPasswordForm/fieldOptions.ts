import { z } from 'zod';
import { TRHFPasswordProps } from '../../RHFItems/RHFItems.types';

type TPasswordFields = 'newPassword' | 'reNewPassword';

type TRHFPasswordOption = Record<TPasswordFields, TRHFPasswordProps>;

type TFieldOptions = TRHFPasswordOption;

export const schemaForm = z
  .object({
    newPassword: z.string().min(1),
    reNewPassword: z.string().min(1),
  })
  .refine(data => data.newPassword === data.reNewPassword, {
    path: ['reNewPassword'], // path of error
    message: "New password and re-new password don't match",
  });

export const fieldOptions: TFieldOptions = {
  newPassword: {
    type: 'password',
    attributes: {
      name: 'newPassword',
      size: 'medium',
      label: 'New password',
      placeholder: 'New password *',
    },
  },
  reNewPassword: {
    type: 'password',
    attributes: {
      name: 'reNewPassword',
      size: 'medium',
      label: 'Re-enter password',
      placeholder: 'Re-enter password *',
    },
  },
};
