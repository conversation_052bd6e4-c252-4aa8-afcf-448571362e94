import { Meta, StoryObj } from '@storybook/react';
import { ForgotPasswordForm } from './ForgotPasswordForm';
import { TForgotPasswordProps } from './ForgotPasswordForm.types';

export default {
  title: 'Molecules/Auth/ForgotPasswordForm',
  component: ForgotPasswordForm,
  tags: ['autodocs'],
} satisfies Meta<TForgotPasswordProps>;

type Story = StoryObj<typeof ForgotPasswordForm>;

export const Default: Story = {
  args: {
    onForgot: () => {},
    isForgetting: false,
  },
  render: (args: any) => (
    <div
      style={{
        width: '100%',
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#B4A093',
      }}
    >
      <ForgotPasswordForm {...args} />
    </div>
  ),
};
