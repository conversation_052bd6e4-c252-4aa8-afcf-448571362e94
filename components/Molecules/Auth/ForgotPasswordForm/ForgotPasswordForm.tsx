'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import Stack from '@mui/material/Stack';
import { FormProvider, useForm } from 'react-hook-form';
import { EPathnames } from '@/lib/types/enum/pathnames';
import Button from '@/components/Atoms/Button';
import RHFItem from '../../RHFItems/RHFItems';
import { StyledBackToLink, StyledBackToText, StyledHeading, StyledPaperWrapper } from './ForgotPasswordForm.styled';
import { TForgotFields, TForgotPasswordProps } from './ForgotPasswordForm.types';
import { fieldOptions, schemaForm } from './fieldOptions';

export const ForgotPasswordForm: React.FC<TForgotPasswordProps> = ({ onForgot, isForgetting }) => {
  const methods = useForm<TForgotFields>({
    defaultValues: { email: '' },
    resolver: zod<PERSON><PERSON><PERSON>ver(schemaForm),
  });

  const {
    reset,
    formState: { isSubmitting },
  } = methods;

  const handleOnForgotPassword = async (values: TForgotFields) => {
    await onForgot({ email: values.email });
  };

  return (
    <StyledPaperWrapper>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handleOnForgotPassword)}>
          <Stack gap={1}>
            <Stack gap={6}>
              <StyledHeading variant="heading-xxlarge-700" textAlign="center">
                Forgot password
              </StyledHeading>
              <Stack gap={3}>
                {(Object.keys(fieldOptions) as Array<keyof typeof fieldOptions>).map(fieldName => (
                  <RHFItem key={fieldName} {...fieldOptions[fieldName]} />
                ))}
              </Stack>
              <Stack direction="row" spacing={2} justifyContent="center">
                <Button
                  variant="contained"
                  type="submit"
                  label="Send email"
                  size="large"
                  isLoading={isForgetting}
                  loadingText="Sending email"
                />
              </Stack>
            </Stack>
            <Stack direction="row" justifyContent="center" alignItems="center" gap={1}>
              <StyledBackToText variant="body-xlarge-400">Back to</StyledBackToText>
              <StyledBackToLink href={EPathnames.SIGNIN}>Sign in</StyledBackToLink>
            </Stack>
          </Stack>
        </form>
      </FormProvider>
    </StyledPaperWrapper>
  );
};
