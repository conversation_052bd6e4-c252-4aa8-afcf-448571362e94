import { z } from 'zod';
import { TRHFTextFieldProps } from '../../RHFItems/RHFItems.types';

type TEmailfields = 'email';

type TRHFEmailfieldOption = Record<TEmailfields, TRHFTextFieldProps>;

type TFieldOptions = TRHFEmailfieldOption;

export const schemaForm = z.object({
  email: z.string().trim().min(1).email(),
});

export const fieldOptions: TFieldOptions = {
  email: {
    type: 'textfield',
    attributes: {
      name: 'email',
      label: 'Email',
      placeholder: 'Email Address *',
      size: 'medium',
    },
  },
};
