import Stack from '@mui/material/Stack';
import { Theme } from '@mui/material/styles';
import dayjs from 'dayjs';
import React from 'react';
import Checkbox from '@/components/Atoms/Checkbox';
import Table from '@/components/Atoms/Table';
import Typography from '@/components/Atoms/Typography';
import { FORMAT_DD_MM_YYYY } from '@/lib/constants/dateTime';
import { TMembershipRefund } from '@/lib/types/entities/membership';
import { IN } from '@/lib/constants/tax';
import { IMembershipRefundColumn, TMembershipRefundTableProps } from './MembershipRefundTable.types';

export const MembershipRefundTable: React.FC<TMembershipRefundTableProps> = ({
  checkedId,
  loading = false,
  rows,
  onChecked,
}) => {
  const columns: IMembershipRefundColumn[] = [
    {
      name: 'id',
      label: <></>,
      render: row => {
        if (row?.isRefund) return <></>;
        const isChecked = row?.id === checkedId;
        return (
          <Checkbox size="small" checked={isChecked} onChange={() => onChecked({ id: row?.id, type: row?.type })} />
        );
      },
    },
    {
      name: 'refund',
      label: 'REFUND',
      render: row => (
        <Typography variant="body-xlarge-400" color={row?.isRefund ? 'red' : 'primary'}>
          {row?.refund}
        </Typography>
      ),
    },
    {
      name: 'value',
      label: 'VALUE',
      render: row => (
        <Typography variant="body-xlarge-400" color={row?.isRefund ? 'red' : 'primary'}>
          {row?.value}
        </Typography>
      ),
    },
    {
      name: 'purchaseDate',
      label: 'PURCHASE DATE',
      render: row => (
        <Typography variant="body-xlarge-400" color={row?.isRefund ? 'red' : 'primary'}>
          {dayjs(row?.purchaseDate).format(FORMAT_DD_MM_YYYY)}
        </Typography>
      ),
    },
    {
      name: 'referenceNo',
      label: 'REFERENCE NO.',
      render: row => (
        <Typography variant="body-xlarge-400" color={row?.isRefund ? 'red' : 'primary'}>
          {row?.referenceNo !== 'undefined' ? IN + row?.referenceNo : ''}
        </Typography>
      ),
    },
    {
      name: 'description',
      label: 'DESCRIPTION',
      render: row => (
        <Typography variant="body-xlarge-400" color={row?.isRefund ? 'red' : 'primary'}>
          {row?.description}
        </Typography>
      ),
    },
    {
      name: 'description',
      label: 'BALANCE',
      render: row => (
        <Typography variant="body-xlarge-400" color={row?.isRefund ? 'red' : 'primary'}>
          {`${row?.refund || 0}/${row?.price || 0}`}
        </Typography>
      ),
    },
    {
      name: 'expiredDate',
      label: 'EXPIRED DATE',
      render: row => (
        <Typography variant="body-xlarge-400" color={row?.isRefund ? 'red' : 'primary'}>
          {dayjs(row?.expiredDate).format(FORMAT_DD_MM_YYYY)}
        </Typography>
      ),
    },
    {
      name: 'price',
      label: 'PRICE',
      render: (row: TMembershipRefund) => (
        <Typography variant="body-xlarge-400" color={row?.isRefund ? 'red' : 'primary'}>
          {row?.price}
        </Typography>
      ),
    },
    {
      name: 'total',
      label: 'TOTAL',
      render: row => (
        <Typography variant="body-xlarge-400" color={row?.isRefund ? 'red' : 'primary'}>
          {row?.total}
        </Typography>
      ),
    },
  ];
  return (
    <Stack gap={2} alignItems="center" bgcolor="white" paddingBottom={2} borderRadius="10px">
      <Table
        isLoading={loading}
        columns={columns}
        tableCellProps={{
          sx: (theme: Theme) => ({
            '&.MuiTableCell-root': {
              borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
              padding: theme.spacing(2, 1),
            },
            '&.MuiTableCell-body': {
              textTransform: 'capitalize',
              ...theme.typography['body-xlarge-400'],
            },
            '& > h5': {
              fontSize: theme.typography['heading-xsmall-700'],
              textTransform: 'uppercase',
            },
          }),
        }}
        sttProps={{
          sx: (theme: Theme) => ({
            '&.MuiTableCell-root': {
              borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
              minWidth: theme.spacing(6),
            },
          }),
        }}
        tableContainerProps={{
          sx: { padding: 3, maxHeight: '300px', overflow: 'auto', borderRadius: '10px !important' },
        }}
        rows={rows || []}
        showSTT={false}
        hasHeaderBackground={false}
      />
    </Stack>
  );
};
