import { TColumnTable } from '@/components/Atoms/Table/Table';
import { TMembershipRefund } from '@/lib/types/entities/membership';
import { ECreditType } from '@/lib/types/enum/credit';

export interface IMembershipRefundColumn extends TColumnTable {
  name: keyof TMembershipRefund | 'actions';
}

export type TMembershipRefundTableProps = {
  loading?: boolean;
  rows?: TMembershipRefund[];
  checkedId?: string;
  onChecked: (value: { id: string; type: ECreditType }) => void;
};
