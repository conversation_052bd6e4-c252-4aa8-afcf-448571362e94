import { Theme } from '@mui/material';
import Stack from '@mui/material/Stack';
import React from 'react';
import { PlusIcon } from '@/components/Atoms/IconsComponent';

type TPlusButtonProps = {
  onClick: () => void;
};
export const PlusButton: React.FC<TPlusButtonProps> = ({ onClick }) => (
  <Stack
    sx={(theme: Theme) => ({
      width: theme.spacing(3),
      height: theme.spacing(3),
      justifyContent: 'center',
      alignItems: 'center',
      color: theme.palette.neutrals.N50.main,
      backgroundColor: theme.palette.common.white,
      border: `1px solid ${theme.palette.neutrals.N50.main}`,
      borderRadius: '50%',
      cursor: 'pointer',
    })}
    onClick={e => {
      e.preventDefault();
      e.stopPropagation();
      onClick();
    }}
  >
    <PlusIcon />
  </Stack>
);
