'use client';

import React, { useEffect, useRef, useState } from 'react';
import ContentEditable from 'react-contenteditable';
import Icon from '@/components/Atoms/Icon';
import { StyledIconButton, StyledTextEditableWrapper, StyledWrapper } from './TextEditable.styled';
import useClickOutside from '@/lib/hooks/utils/useClickOutside';
import ConfirmModal from '@/components/Atoms/ConfirmModal';

export type TTextEditableProps = {
  value: string;
  isBold?: boolean;
  isUppercase?: boolean;
  onDelete?: () => void;
  onChange?: (value: string) => void;
};

export const TextEditable: React.FC<TTextEditableProps> = ({ value, isBold, isUppercase, onChange, onDelete }) => {
  const [currValue, setCurrValue] = useState<string>('');
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const [focused, setFocused] = useState<boolean>(false);
  const ref = useRef<HTMLDivElement>(null);
  const editableRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    setCurrValue(value);
  }, [value]);
  useClickOutside(ref, () => {
    setCurrValue(value);
  });
  return (
    <>
      <StyledWrapper ref={ref}>
        <StyledTextEditableWrapper isBold={isBold} isUppercase={isUppercase}>
          <ContentEditable
            innerRef={editableRef}
            html={currValue} // innerHTML of the editable div
            disabled={false} // use true to disable editing
            onChange={() => setCurrValue(editableRef.current?.innerText || '')} // handle innerHTML change
            onFocus={() => setFocused(true)}
            onBlur={() =>
              setTimeout(() => {
                setFocused(false);
              }, 300)
            }
            className="category_setting_item--texteditable"
          />
        </StyledTextEditableWrapper>
        {focused && typeof onChange === 'function' && (
          <>
            <StyledIconButton
              disableRipple
              disableFocusRipple
              onClick={() => {
                setCurrValue(value);
              }}
            >
              <Icon variant="gray_close" size={3} />
            </StyledIconButton>
            <StyledIconButton
              disableRipple
              disableFocusRipple
              onClick={() => {
                if (currValue.trim().length > 0) onChange(currValue);
              }}
            >
              <Icon variant="green_checked" size={3} />
            </StyledIconButton>
          </>
        )}
        {!focused && typeof onDelete === 'function' && (
          <StyledIconButton
            disableRipple
            disableFocusRipple
            sx={{ display: 'none' }}
            onClick={() => setIsOpenModal(true)}
          >
            <Icon variant="delete" size={3} />
          </StyledIconButton>
        )}
      </StyledWrapper>
      {isOpenModal && onDelete && (
        <ConfirmModal
          isOpen={isOpenModal}
          title="Confirm delete category"
          bodyContent="Do you want to delete this category?"
          onCancel={() => setIsOpenModal(false)}
          onClose={() => setIsOpenModal(false)}
          onConfirm={() => {
            onDelete();
            setIsOpenModal(false);
          }}
        />
      )}
    </>
  );
};
