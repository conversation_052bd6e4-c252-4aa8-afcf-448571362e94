import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import IconButton, { iconButtonClasses } from '@mui/material/IconButton';
import { Box } from '@mui/material';

export const StyledWrapper = styled(Stack)(({ theme }) => ({
  minWidth: theme.spacing(200 / 8),
  minHeight: theme.spacing(5),
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.spacing(1.5),
  padding: theme.spacing(0, 1.5),
  borderRadius: theme.spacing(1.25),
  '&:hover': {
    backgroundColor: theme.palette.neutrals.N210.main,
    [`.${iconButtonClasses.root}`]: {
      display: 'flex',
    },
  },
}));

export const StyledTextEditableWrapper = styled(Box)<{ isBold?: boolean; isUppercase?: boolean }>(
  ({ theme, isBold, isUppercase }) => ({
    flex: 1,
    '.category_setting_item--texteditable': {
      border: '1px solid transparent',
      whiteSpace: 'nowrap',
      textTransform: isUppercase ? 'uppercase' : 'none',
      ...(isBold ? theme.typography['heading-medium-700'] : theme.typography['body-xlarge-400']),
      '&:focus': {
        border: '1px solid transparent',
      },
    },
  })
);

export const StyledIconButton = styled(IconButton)(({ theme }) => ({
  padding: 0,
}));
