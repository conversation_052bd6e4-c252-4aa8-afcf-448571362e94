'use client';

import React, { useEffect, useRef, useState } from 'react';
import ContentEditable from 'react-contenteditable';
import { StyledTextEditableWrapper, StyledWrapper, StyledIconButton } from './RoomQuantityEditable.styled';
import useClickOutside from '@/lib/hooks/utils/useClickOutside';
import Icon from '@/components/Atoms/Icon';
import { useAlert } from '@/lib/hooks/context/useAlert';

export type TRoomQuantityEditableProps = {
  value: string;
  onChange: (value: string) => void;
};

export const RoomQuantityEditable: React.FC<TRoomQuantityEditableProps> = ({ value, onChange }) => {
  const [currValue, setCurrValue] = useState<string>('');
  const ref = useRef<HTMLDivElement>(null);
  const editableRef = useRef<HTMLDivElement>(null);
  const [focused, setFocused] = useState<boolean>(false);
  const { showError } = useAlert();
  useEffect(() => {
    setCurrValue(value);
  }, [value]);
  useClickOutside(ref, () => {
    setCurrValue(value);
  });
  return (
    <>
      <StyledWrapper ref={ref}>
        <StyledTextEditableWrapper>
          <ContentEditable
            innerRef={editableRef}
            html={currValue} // innerHTML of the editable div
            disabled={false} // use true to disable editing
            onChange={() => setCurrValue(editableRef.current?.innerText || '')} // handle innerHTML change
            onFocus={() => setFocused(true)}
            onBlur={() =>
              setTimeout(() => {
                setFocused(false);
              }, 300)
            }
            className="category_setting_item--texteditable"
          />
        </StyledTextEditableWrapper>
        {focused && (
          <>
            <StyledIconButton
              disableRipple
              disableFocusRipple
              onClick={() => {
                setCurrValue(value);
              }}
            >
              <Icon variant="gray_close" size={3} />
            </StyledIconButton>
            <StyledIconButton
              disableRipple
              disableFocusRipple
              onClick={() => {
                if (Number.isNaN(Number(currValue))) {
                  showError({ title: 'Room quantity is invalid. Please enter a number' });
                  return;
                }
                if (currValue.trim().length > 0) onChange(currValue);
              }}
            >
              <Icon variant="green_checked" size={3} />
            </StyledIconButton>
          </>
        )}
      </StyledWrapper>
    </>
  );
};
