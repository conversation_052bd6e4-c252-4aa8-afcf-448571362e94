import React, { useContext } from 'react';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import { Theme } from '@mui/material';
import { isNumber } from 'lodash';
import { TCategorySettingItem } from '../CategorySetting.types';
import Switch from '@/components/Atoms/Switch';
import PlusButton from '../PlusButton';
import { TextEditable } from '@/components/Molecules/CategorySetting/TextEditable/TextEditable';
import { TCategoryUpdateReq } from '@/lib/types/entities/category';
import { ChevronDownIcon } from '@/components/Atoms/IconsComponent';
import { MediaUpload } from '@/components/Atoms/MediaUpload';
import { CategorySettingContext } from '../CategorySetting';
import Typography from '@/components/Atoms/Typography';
import { RoomQuantityEditable } from '../RoomQuantityEditable/RoomQuantityEditable';

export type TCatSetAccordionHeaderProps = {
  value: TCategorySettingItem;
  hasChildren: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
  showTotalRoom: boolean;
  canChangeStatus: boolean;
  onCreate: () => void;
  onUpdate: (dataToUpdate: TCategoryUpdateReq) => void;
  onDelete: () => void;
};

export const CatSetAccordionHeader: React.FC<TCatSetAccordionHeaderProps> = ({
  value,
  hasChildren,
  showTotalRoom,
  canCreate,
  canUpdate,
  canDelete,
  canChangeStatus,
  onCreate,
  onUpdate,
  onDelete,
}) => {
  const { statusOptions } = useContext(CategorySettingContext);
  return (
    <Stack
      sx={(theme: Theme) => ({
        width: '100%',
        position: 'relative',
        justifyContent: 'flex-start',
        alignItems: 'center',
        flexDirection: 'row',
        gap: theme.spacing(3),
        padding: theme.spacing(0, 3),
        cursor: hasChildren ? 'pointer' : 'default',
        zIndex: 3,
      })}
      onClick={e => {
        if (!hasChildren) e.stopPropagation();
      }}
    >
      <Stack
        sx={(theme: Theme) => ({
          width: 'fit-content',
          minWidth: theme.spacing(21 / 8),
          justifyContent: 'flex-start',
          alignItems: 'center',
          flexDirection: 'row',
          visibility: hasChildren ? 'visible' : 'hidden',
        })}
      >
        <ChevronDownIcon />
      </Stack>
      <Box flex={isNumber(value?.roomQuantity) && showTotalRoom ? '7' : '9'} onClick={e => e.stopPropagation()}>
        <TextEditable
          value={value.name}
          isBold={!!canCreate}
          isUppercase={!!canCreate}
          onChange={
            canUpdate
              ? (newValue: string) =>
                  onUpdate({
                    id: value.id,
                    name: newValue,
                  })
              : undefined
          }
          onDelete={canDelete ? onDelete : undefined}
        />
      </Box>
      {isNumber(value.roomQuantity) && showTotalRoom && (
        <Stack alignItems="center" direction="row" gap={0.5} flex="2" onClick={e => e.stopPropagation()}>
          <Typography variant="body-xlarge-400">Rooms:</Typography>
          <RoomQuantityEditable
            value={`${value.roomQuantity}`}
            onChange={(newValue: string) => {
              onUpdate({
                id: value.id,
                roomQuantity: Number(newValue),
                name: value.name,
              });
            }}
          />
        </Stack>
      )}
      <Stack flex="2" direction="row" justifyContent="flex-start" alignItems="center" px={3} gap={3}>
        <Box
          onClick={e => {
            e.stopPropagation();
          }}
          minWidth="42px"
        >
          {canCreate && hasChildren && (
            <MediaUpload
              variant="category_thumbnail"
              getFileUploadRes={(data: { id: string; url?: string | undefined }) => {
                onUpdate({
                  id: value?.id,
                  name: value?.name || '',
                  avatar: data?.url
                    ? {
                        id: data?.id || '',
                        url: data?.url || '',
                      }
                    : null,
                });
              }}
              avatarProps={{ src: value?.avatar?.url || '' }}
              value={{ id: value?.avatar?.id || '', url: value?.avatar?.url || '' }}
            />
          )}
        </Box>
        <Box flex="1">
          <Switch
            checked={statusOptions?.[0]?.id === value?.status?.id}
            onChange={
              canChangeStatus
                ? (checked: boolean) => {
                    onUpdate({
                      id: value?.id || '',
                      name: value?.name || '',
                      status: {
                        id: checked ? statusOptions?.[0]?.id : statusOptions?.[1]?.id,
                      },
                    });
                  }
                : undefined
            }
          />
        </Box>
      </Stack>
      <Stack direction="row" justifyContent="flex-end" minWidth="24px">
        {canCreate && <PlusButton onClick={onCreate} />}
      </Stack>
    </Stack>
  );
};
