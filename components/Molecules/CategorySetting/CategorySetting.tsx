import Stack from '@mui/material/Stack';
import React, { useEffect, useMemo, useState } from 'react';
import Accordion from '@/components/Atoms/Accordion';
import Typography from '@/components/Atoms/Typography';
import { CATGEORY_NAMES } from '@/lib/constants/category';
import { useAlert } from '@/lib/hooks/context/useAlert';
import {
  useMutationCategoryCreate,
  useMutationCategoryDelete,
  useMutationCategoryUpdate,
} from '@/lib/hooks/mutation/category';
import { useSetting } from '@/lib/hooks/queries/setting';
import { TCategoryCreateReq, TCategoryUpdateReq } from '@/lib/types/entities/category';
import { TSelectOption } from '../RHFItems/RHFSelect/RHFSelect';
import CatSetAccordionHeader from './CatSetAccordionHeader';
import { TCategorySettingItem, TCategorySettingProps } from './CategorySetting.types';
import PlusButton from './PlusButton';

const MAX_LEVEL = 3;

export const CategorySettingContext = React.createContext<{
  statusOptions: TSelectOption[];
}>({
  statusOptions: [],
});

const renderTree = (
  accordionItem: TCategorySettingItem,
  activeKeys: string[],
  onChange: (activeKey: string) => void,
  onDelete: (id: string) => void,
  onCreate: (parentId: string, level: number) => void,
  onUpdate: (data: TCategoryUpdateReq) => void,
  isServiceCategory: boolean,
  canCreate: boolean,
  canUpdate: boolean,
  canDelete: boolean,
  canChangeStatus: boolean
) => (
  <Accordion
    key={accordionItem.id}
    activeKeys={activeKeys}
    items={accordionItem.children.map(childItem => ({
      key: childItem.id,
      header: (
        <CatSetAccordionHeader
          value={childItem}
          canCreate={childItem.level < MAX_LEVEL && canCreate}
          canUpdate={canUpdate}
          canDelete={canDelete}
          hasChildren={childItem?.children?.length > 0}
          showTotalRoom={childItem.level === MAX_LEVEL && isServiceCategory}
          onUpdate={onUpdate}
          onDelete={() => onDelete(childItem.id)}
          onCreate={() => {
            onCreate(childItem.id, childItem.level);
            if (!activeKeys.includes(childItem.id)) onChange(childItem.id);
          }}
          canChangeStatus={canChangeStatus}
        />
      ),
      hasDivider: childItem.level < MAX_LEVEL,
      content: renderTree(
        childItem,
        activeKeys,
        onChange,
        onDelete,
        onCreate,
        onUpdate,
        isServiceCategory,
        canCreate,
        canUpdate,
        canDelete,
        canChangeStatus
      ), // Pass the same activeKeys and onChange down
    }))}
    variant="unstyled"
    onChange={(activeKey: string) => {
      onChange(activeKey);
    }}
  />
);

export const CategorySetting: React.FC<TCategorySettingProps> = ({
  items,
  hasPermissionCreate: canCreate,
  hasPermissionDelete: canDelete,
  hasPermissionUpdate: canUpdate,
  hasPermissionChangeStatus: canChangeStatus,
  onRefetch,
}) => {
  const [activeKeys, setActiveKeys] = useState<string[]>([]);
  const { onCreateCategory } = useMutationCategoryCreate();
  const { onUpdateCategory } = useMutationCategoryUpdate();
  const { onDeleteCategory } = useMutationCategoryDelete();
  const { showError, showSuccess } = useAlert();
  const { data: OPTION_BY_KEY } = useSetting(['status']);
  const level1CategoryIds = items.map(item => item.id);

  useEffect(() => {
    setActiveKeys(level1CategoryIds);
  }, [JSON.stringify(level1CategoryIds)]);

  const onChange = (activeKey: string) => {
    if (activeKeys.includes(activeKey)) {
      setActiveKeys(activeKeys.filter(activeKeyItem => activeKeyItem !== activeKey));
    } else {
      setActiveKeys([...activeKeys, activeKey]);
    }
  };

  const onCreate = async (parentId: string, level: number) => {
    if (!canCreate) return;
    const activeOption = (OPTION_BY_KEY?.['status'] || []).find(option => option?.name?.toLowerCase() === 'active');
    const dataToCreate: TCategoryCreateReq = {
      parent: {
        id: parentId,
      },
      name: level === 1 ? 'NEW CATEGORY' : 'New Category',
    };
    if (activeOption) {
      dataToCreate['status'] = {
        id: activeOption?.id || '',
        name: activeOption?.id || '',
      };
    }
    const response = await onCreateCategory(dataToCreate);
    if (response.status === 'error') {
      if (typeof response?.message?.error === 'string') {
        showError({ title: response?.message?.error });
        return;
      }
      return;
    }
    showSuccess({ title: 'Created! !' });
    onRefetch();
  };

  const onUpdate = async (dataToUpdate: TCategoryUpdateReq) => {
    if (canUpdate || canChangeStatus) {
      const response = await onUpdateCategory(dataToUpdate);
      if (response.status === 'error') {
        if (typeof response?.message?.error === 'string') {
          showError({ title: response?.message?.error });
          return;
        }
      }
      showSuccess({ title: 'Updated !' });
      onRefetch();
    }
  };
  const onDelete = async (id: string) => {
    if (!canDelete) return;
    const response = await onDeleteCategory(id);
    if (response.status === 'error') {
      if (typeof response?.message?.error === 'string') {
        showError({ title: response?.message?.error });
        return;
      }
      return;
    }
    showSuccess({ title: 'Deleted !' });
    onRefetch();
  };
  const memoizedResources = useMemo(
    () => ({
      statusOptions: OPTION_BY_KEY?.['status'] || [],
    }),
    [JSON.stringify(OPTION_BY_KEY?.['status'])]
  );
  return (
    <CategorySettingContext.Provider value={memoizedResources}>
      <Stack gap={3}>
        {items.map(item => (
          <Accordion
            key={item.id}
            activeKeys={activeKeys}
            items={[
              {
                key: item.id,
                header: (
                  <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
                    <Typography component="p" variant="heading-medium-700">
                      {CATGEORY_NAMES?.[item?.name] || item?.name}
                    </Typography>

                    {canCreate && <PlusButton onClick={() => onCreate(item.id, item.level)} />}
                  </Stack>
                ),
                content: renderTree(
                  item,
                  activeKeys,
                  onChange,
                  onDelete,
                  onCreate,
                  onUpdate,
                  item?.name === 'SERVICE',
                  canCreate,
                  canUpdate,
                  canDelete,
                  canChangeStatus
                ), // Pass the onChange function
              },
            ]}
            variant="styled" // Level 1 items rendered as styled accordion
            onChange={onChange}
          />
        ))}
      </Stack>
    </CategorySettingContext.Provider>
  );
};
