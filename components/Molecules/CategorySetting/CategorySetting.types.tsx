export type TCategorySettingItem = {
  id: string;
  name: string;
  level: number;
  roomQuantity?: number;
  avatar: {
    id: string;
    url: string;
  };
  status: {
    id: string;
    name: string;
  };
  children: TCategorySettingItem[];
};

export type TCategorySettingProps = {
  items: TCategorySettingItem[];
  hasPermissionCreate: boolean;
  hasPermissionUpdate: boolean;
  hasPermissionDelete: boolean;
  hasPermissionChangeStatus: boolean;
  onRefetch: () => void;
};
