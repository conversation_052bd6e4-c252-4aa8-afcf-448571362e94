'use client';

import { Divider, Theme } from '@mui/material';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import { useEffect, useMemo } from 'react';
import Typography from '@/components/Atoms/Typography';
import { FORMAT_DD_MM_YYYY, FORMAT_YYYY_MM_DD } from '@/lib/constants/dateTime';
import { TCustomer } from '@/lib/types/entities/customer';
import { TOrder } from '@/lib/types/entities/order';
import { ECreditType } from '@/lib/types/enum/credit';
import { concatenateNames, round2decimal } from '@/lib/utils/string';
import Price from '../Price';
import { TUser } from '@/lib/types/entities/user';

export type TRefundMembershipPrintingProps = {
  user?: TUser;
  customer?: TCustomer;
  employee?: { id: string; name: string };
  orders?: TOrder[];
  refundDate: string;
  refundBeforeTax: number;
  taxPercent: number;
  totalTax: number;
  totalRefund: number;
  price: number;
  referenceNo: string;
  purchaseDate: string;
  description: string;
  used: number;
  balance: number;
  credit: number;
  refund: number;
  type: ECreditType;
};
export const RefundMembershipPrinting: React.FC<TRefundMembershipPrintingProps> = ({
  customer,
  employee,
  orders,
  refundDate,
  refundBeforeTax,
  taxPercent,
  totalTax,
  totalRefund,
  price,
  referenceNo,
  purchaseDate,
  description,
  used,
  balance,
  credit,
  refund,
  type,
  user,
}) => {
  const renderBody = useMemo(() => {
    const OLD_COLUMN = [
      { name: 'PURCHASE DATE', width: '90px' },
      { name: 'REFERENCE NO.', width: '90px' },
      { name: 'DESCRIPTION', width: '150px' },
      { name: 'CREDIT', width: '90px' },
      { name: 'USED', width: '90px' },
      { name: 'BALANCE', width: '90px' },
      { name: 'REFUND', width: '90px' },
    ];
    const COLUMN_VALUE = [
      { value: dayjs(purchaseDate).format(FORMAT_DD_MM_YYYY), width: '90px' },
      { value: referenceNo ? `IN${referenceNo}` : '', width: '90px' },
      { value: description, width: '150px' },
      { value: credit, width: '90px' },
      { value: used, width: '90px' },
      { value: balance, width: '90px' },
      { value: refund, width: '90px' },
    ];
    if (type === ECreditType.OLD_CREDIT)
      return (
        <Stack>
          <Stack flexDirection="row" flexWrap="nowrap" justifyContent="space-between">
            {OLD_COLUMN?.map(({ name, width }, indx) => (
              <Typography
                color="primary"
                textAlign={indx === OLD_COLUMN.length - 1 ? 'right' : 'left'}
                width={width}
                variant="heading-xxsmall-700"
                key={name}
              >
                {name}
              </Typography>
            ))}
          </Stack>
          <Divider
            sx={(theme: Theme) => ({
              borderBottomWidth: '1px',
              borderColor: theme.palette.primary.main,
              my: '12px',
            })}
          />
          <Stack flexDirection="row" flexWrap="nowrap" justifyContent="space-between">
            {COLUMN_VALUE?.map(({ value, width }, indx) => (
              <Typography
                color="primary"
                textAlign={indx === OLD_COLUMN.length - 1 ? 'right' : 'left'}
                width={width}
                variant="body-medium-400"
                key={value}
              >
                {value}
              </Typography>
            ))}
          </Stack>
        </Stack>
      );

    const NEW_COLUMN = [
      { name: 'PURCHASE DATE', width: '90px' },
      { name: 'REFERENCE NO.', width: '90px' },
      { name: 'DESCRIPTION', width: '150px' },
      { name: '', width: '90px' },
      { name: 'REFUND', width: '90px' },
    ];
    const COLUMN_NEW_VALUE = [
      { value: dayjs(purchaseDate).format(FORMAT_YYYY_MM_DD), width: '90px' },
      { value: referenceNo ? `IN${referenceNo}` : '', width: '90px' },
      { value: description, width: '150px' },
      { value: `${round2decimal(refund || 0)}/${round2decimal(price || 0)}`, width: '90px' },
      { value: `$ ${round2decimal(refund || 0)}`, width: '90px' },
    ];
    return (
      <Stack>
        <Stack flexDirection="row" flexWrap="nowrap" justifyContent="space-between">
          {NEW_COLUMN?.map(({ name, width }, indx) => (
            <Typography
              color="primary"
              textAlign={indx === NEW_COLUMN.length - 1 ? 'right' : 'left'}
              width={width}
              variant="heading-xxsmall-700"
              key={indx as number}
            >
              {name}
            </Typography>
          ))}
        </Stack>
        <Divider
          sx={(theme: Theme) => ({
            borderBottomWidth: '1px',
            borderColor: theme.palette.primary.main,
            my: '12px',
          })}
        />
        <Stack flexDirection="row" flexWrap="nowrap" justifyContent="space-between">
          {COLUMN_NEW_VALUE?.map(({ value, width }, indx) => (
            <Typography
              color="primary"
              textAlign={indx === COLUMN_NEW_VALUE.length - 1 ? 'right' : 'left'}
              width={width}
              variant="body-medium-400"
              key={indx as number}
            >
              {value}
            </Typography>
          ))}
        </Stack>
        <Typography color="primary" variant="heading-xsmall-700" sx={{ pt: '24px', pb: '12px' }}>
          Service utilised
        </Typography>
        {orders?.map(order => (
          <>
            <Stack
              key={order?.id}
              flexDirection="row"
              flexWrap="nowrap"
              width="100%"
              justifyContent="space-between"
              gap={1}
            >
              <Stack>
                <Typography width="90px" color="primary" variant="body-medium-400">
                  {dayjs(order?.invoiceDate).format(FORMAT_DD_MM_YYYY)}
                </Typography>
              </Stack>
              {order?.referenceNo && (
                <Stack gap={1.5} flex="1 1 10%">
                  <Typography color="primary" variant="body-medium-400">
                    {`${order?.referenceNo ? `IN${order?.referenceNo}` : ''}`}a
                  </Typography>
                </Stack>
              )}
              <Stack gap={0.5} flexGrow="1" flex="1 1 65%">
                {order?.items?.map((item, indx) => (
                  <Stack flexDirection="row" flexWrap="nowrap" alignItems="center" justifyContent="space-between">
                    <Stack flexDirection="row" flexWrap="nowrap" gap={1} alignItems="center" flex="1 1 50%">
                      <Typography color="primary" variant="body-medium-400">
                        {item?.quantity || 0}
                      </Typography>
                      <Typography color="primary" variant="body-medium-400">
                        {item?.product?.name || ''}
                      </Typography>
                    </Stack>

                    <Price
                      amount={item?.price || 0}
                      showSymbol={false}
                      typographyProps={{
                        component: 's',
                        color: 'primary',
                        variant: 'body-medium-400',
                      }}
                    />
                    <Price
                      amount={item?.nonMemberPrice || 0}
                      showSymbol={false}
                      typographyProps={{
                        component: 's',
                        color: 'primary',
                        variant: 'body-medium-400',
                        px: '8px',
                      }}
                    />
                    <Price
                      amount={(item?.price || 0) - (item?.nonMemberPrice || 0)}
                      typographyProps={{
                        color: 'primary',
                        variant: 'body-medium-400',
                        width: '80px',
                        minWidth: '80px',
                        textAlign: 'right',
                      }}
                    />
                  </Stack>
                ))}
              </Stack>
            </Stack>
            <Divider
              sx={(theme: Theme) => ({
                borderBottomWidth: '1px',
                borderColor: theme.palette.primary.main,
                my: '12px',
              })}
            />
          </>
        ))}
      </Stack>
    );
  }, [type, customer, orders]);

  return (
    <Stack
      sx={theme => ({
        width: '100%',
        maxHeight: '100%',
        padding: theme.spacing(6),
        backgroundColor: theme.palette.common.white,
        gap: theme.spacing(10),
        height: 'fit-content',
        overflow: 'hidden auto',
        color: theme.palette.neutrals.N50.main,
        '&::-webkit-scrollbar': {
          background: '#DCE5F2',
          borderRadius: '5px',
          width: '5px',
        },
        '&:: -webkit-scrollbar-thumb': {
          background: '#99A3B4',
          borderRadius: '5px',
        },
      })}
    >
      <Stack gap={6}>
        <Stack gap={1.5} textAlign="center">
          <Typography variant="heading-xlarge-700">CREDIT NOTE</Typography>
          <Stack gap={0.5}>
            <Typography variant="heading-medium-700">
              Onsen Retreat and Spa (Singapore) Pte Ltd. GST Reg. No.: 201425725W
            </Typography>
            <Typography variant="body-medium-400">
              1 Stadium Place #02-17/18 Kallang Wave Mall Singapore 397628
            </Typography>
          </Stack>
        </Stack>
        <Stack direction="row" gap={7} justifyContent="flex-start" alignItems="center">
          <Stack gap={1} flex="1">
            <Stack direction="row" gap={3}>
              <Typography variant="body-xlarge-400" flex="1">
                Customer
              </Typography>
              <Typography variant="heading-medium-700" flex="1">
                : {concatenateNames(customer?.firstName || '', customer?.lastName || '')}
              </Typography>
            </Stack>
            <Stack direction="row" gap={3}>
              <Typography variant="body-xlarge-400" flex="1">
                Phone
              </Typography>
              <Typography variant="heading-medium-700" flex="1">
                : {customer?.phone}
              </Typography>
            </Stack>
          </Stack>
          <Stack gap={1} flex="1">
            <Stack direction="row" gap={3}>
              <Typography variant="body-xlarge-400" flex="1">
                Employee name
              </Typography>
              <Typography variant="heading-medium-700" flex="1">
                : {employee?.name}
              </Typography>
            </Stack>
            <Stack direction="row" gap={3}>
              <Typography variant="body-xlarge-400" flex="1">
                Date
              </Typography>
              <Typography variant="heading-medium-700" flex="1">
                : {dayjs(refundDate).isValid() ? dayjs(refundDate).format('DD/MM/YYYY') : ''}
              </Typography>
            </Stack>
            <Stack direction="row" gap={3}>
              <Typography variant="body-xlarge-400" flex="1">
                Credit note NO
              </Typography>
              <Typography variant="heading-medium-700" flex="1">
                : {referenceNo ? `CN${referenceNo}` : ''}
              </Typography>
            </Stack>
          </Stack>
        </Stack>
        <Stack gap={3}>
          {renderBody}
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="body-xlarge-400">Subtotal</Typography>
            <Typography variant="body-xlarge-400">
              <Price
                amount={refundBeforeTax}
                typographyProps={{ minWidth: '25px', textAlign: 'right', variant: 'body-xlarge-400' }}
              />
            </Typography>
          </Stack>
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="body-xlarge-400">Tax {taxPercent}%</Typography>
            <Price
              amount={Number(totalTax.toFixed(2))}
              typographyProps={{ minWidth: '25px', textAlign: 'right', variant: 'body-xlarge-400' }}
            />
          </Stack>
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="heading-medium-700">TOTAL REFUND</Typography>
            <Price
              amount={totalRefund}
              typographyProps={{ minWidth: '25px', textAlign: 'right', variant: 'heading-medium-700' }}
            />
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  );
};
