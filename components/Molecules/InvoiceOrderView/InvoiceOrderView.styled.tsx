import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';

export const StyledReceiptWrapper = styled(Stack)(({ theme }) => ({
  width: theme.spacing(302 / 8),
  height: 'fit-content',
  maxHeight: '100%',
  overflow: 'hidden auto',
  gap: theme.spacing(3),
  backgroundColor: theme.palette.common.white,
  color: theme.palette.neutrals.N50.main,
  padding: theme.spacing(4, 1.5, 1.5, 1.5),
  '&::-webkit-scrollbar': {
    background: '#DCE5F2',
    borderRadius: '5px',
    width: '5px',
  },
  '&:: -webkit-scrollbar-thumb': {
    background: '#99A3B4',
    borderRadius: '5px',
  },
}));
