import { TInvoice } from '@/lib/types/entities/invoice';
import { TCouponApplied } from '../DesktopCheckout/DesktopCheckout';

export type TInvoiceOrderItem = {
  id: string;
  name: string;
  quantity: number;
  price: number;
  note: string;
  couponCode?: string;
};
export type TInvoiceOrder = {
  items: TInvoiceOrderItem[];
  code: string;
  totalBeforeTax: number;
  subTotal?: number;
  transferBy?: { id: string };
};
export type TInvoiceOrderViewProps = {
  variant: 'invoice' | 'order';
  orders: TInvoiceOrder[];
  createdDate: string;
  customer: {
    id: string;
    name: string;
    phone: string;
    rfid?: string;
    rfidLocker?: string;
    rfidGroup?: string;
  };
  employee: {
    name: string;
  };
  paymentMethods: {
    name: string;
    amount: number;
    roundNumber: number;
    billCode: string;
  }[];
  note: string;
  orderInvoiceId: string;
  branch: {
    address: string;
    phone: string;
  };
  unPaid: number;
  total: number;
  totalRounding?: number;
  subTotal: number;
  refund?: number;
  taxPercent: number;
  totalBeforeTax: number;
  coupons: TCouponApplied[];
  childInvoices?: TInvoice[];
  newCredit?: {
    balance: number;
    expiry: string;
  };
  oldCredit?: {
    balance: number;
    expiry: string;
  };
};
