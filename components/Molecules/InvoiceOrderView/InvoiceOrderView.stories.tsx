import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import InvoiceOrderView from '.';

export default {
  title: 'Molecules/InvoiceOrderView',
  component: InvoiceOrderView,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof InvoiceOrderView>;

export const Default: Story = {
  args: {
    variant: 'order',
    orders: [
      {
        items: [
          {
            id: '1',
            name: 'Aveda Invati Advanced Scalp Revitaliser 150ml',
            quantity: 1,
            price: 58,
            note: '',
          },
          {
            id: '1',
            name: 'Aveda Invati Advanced Scalp Revitaliser 150ml',
            quantity: 1,
            price: 58,
            note: '',
          },
          {
            id: '2',
            name: 'Aveda Invati Advanced Scalp Revitaliser 150ml',
            quantity: 1,
            price: 58,
            note: '',
          },
          {
            id: '3',
            name: 'Aveda Invati Advanced Scalp Revitaliser 150ml',
            quantity: 1,
            price: 58,
            note: '',
          },
          {
            id: '4',
            name: 'Aveda Invati Advanced Scalp Revitaliser 150ml',
            quantity: 1,
            price: 58,
            note: '',
          },
        ],
        totalBeforeTax: 0,
        code: '12312',
      },
    ],
    createdDate: '2023-12-08T03:55:57+0000',
    customer: {
      id: '66154',
      name: 'SHENNY YONG',
      phone: '84 8234 4566',
      rfid: '00924524',
    },
    employee: {
      name: 'Tong',
    },
    paymentMethods: [],
    note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididun',
    orderInvoiceId: '191',
    branch: {
      address: 'Branch 1: 1 Stadium Place #02-17/18 Kallang Wave Mall Singapore 397628',
      phone: '(+65) 6386 4126 / 6385 7985',
    },
    total: 0,
    taxPercent: 0,
    subTotal: 0,
    unPaid: 0,
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <InvoiceOrderView {...args} />
    </Box>
  ),
};
