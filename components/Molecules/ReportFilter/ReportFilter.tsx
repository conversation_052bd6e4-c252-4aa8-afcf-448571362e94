import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import { isEmpty, uniq } from 'lodash';
import { useEffect, useMemo } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useSetting } from '@/lib/hooks/queries/setting';
import { useCategory } from '@/lib/hooks/queries/category';
import RHFItem from '@/components/Molecules/RHFItems/RHFItems';
import Button from '@/components/Atoms/Button';
import { OptionTypes } from '../RHFItems/RHFItems.types';
import { StyledContainer, StyledFormContainer } from './ReportFilter.styled';
import { TReportFilterProps } from './ReportFilter.types';
import { TKeyOfForm, defaultFieldOptions } from './fieldOptions';

export const ReportFilter = <T extends Partial<Record<TKeyOfForm, any>>>({
  onFilter,
  fieldOptionNames,
  optionSettings = [],
  useFormProps,
  categoryName,
  creditTypeOptions = [],
  rangeDateLabel = 'Select Date',
  customOptions,
  customFieldOptions,
}: TReportFilterProps<T>) => {
  const { data: categoryData } = useCategory({ filterValue: categoryName ? [categoryName] : [] });
  const { data: OPTION_BY_KEY } = useSetting(optionSettings);
  const methods = useForm<T>({
    ...useFormProps,
  });

  const OPTIONS_BY_FIELDNAME = useMemo(() => {
    const temp: Partial<Record<string, any[]>> = {
      ...customOptions,
      category: categoryData?.[0]?.children || [],
    };
    optionSettings.forEach(optionSetting => {
      temp[optionSetting] =
        OPTION_BY_KEY?.[optionSetting]?.map(b => ({
          id: b?.id || '',
          name: b?.name || '',
        })) || [];
    });
    return temp;
  }, [categoryData, OPTION_BY_KEY, customOptions]);

  useEffect(() => {
    const subscription = methods.watch((value, { name }) => {
      if (
        name === 'startDate' &&
        fieldOptionNames.includes('endDate') &&
        fieldOptionNames.includes('startDate') &&
        !fieldOptionNames.includes('rangeDate')
      ) {
        methods.setValue('endDate' as any, value.startDate);
      }
    });
    return () => subscription.unsubscribe();
  }, [methods.watch]);

  const onSubmit = (values: T) => {
    onFilter(values);
  };

  const reorderFieldOptions = (optionKeys: string[]) => {
    if (fieldOptionNames?.length) {
      return uniq([...fieldOptionNames, ...optionKeys]);
    }
    return optionKeys;
  };

  const renderRHFItems = () => {
    const fieldOptionKeys = reorderFieldOptions(
      Object.keys(defaultFieldOptions).filter(fieldName => fieldOptionNames.includes(fieldName as TKeyOfForm))
    );

    return fieldOptionKeys.map(fieldName => {
      if (
        isEmpty(defaultFieldOptions?.[fieldName as TKeyOfForm]?.type) ||
        isEmpty(defaultFieldOptions?.[fieldName as TKeyOfForm]?.containerProps) ||
        isEmpty(defaultFieldOptions?.[fieldName as TKeyOfForm]?.attributes)
      )
        return <></>;

      const customAttribute = customFieldOptions?.[fieldName as TKeyOfForm]?.attributes || {};
      const customContainerProps = customFieldOptions?.[fieldName as TKeyOfForm]?.containerProps || {};
      return (
        <RHFItem
          key={fieldName}
          type={defaultFieldOptions?.[fieldName as TKeyOfForm]?.type as OptionTypes}
          attributes={{
            ...defaultFieldOptions?.[fieldName as TKeyOfForm]?.attributes,
            ...(OPTIONS_BY_FIELDNAME[fieldName] ? { options: OPTIONS_BY_FIELDNAME[fieldName] } : {}),
            ...(fieldName === 'creditType' && !isEmpty(creditTypeOptions) ? { options: creditTypeOptions } : {}),
            ...(fieldName === 'rangeDate' ? { label: rangeDateLabel } : {}),
            ...customAttribute,
          }}
          containerProps={{
            ...defaultFieldOptions?.[fieldName as TKeyOfForm]?.containerProps,
            ...customContainerProps,
          }}
        />
      );
    });
  };

  return (
    <StyledContainer>
      <FormProvider {...methods}>
        <Box flex="1">
          <form id="ReportFilterForm" onSubmit={methods.handleSubmit(onSubmit)}>
            <StyledFormContainer>{renderRHFItems()}</StyledFormContainer>
          </form>
        </Box>
        <Stack width="156px" height="100%" justifyContent="flex-start" paddingTop="24px">
          <Button
            form="ReportFilterForm"
            type="submit"
            color="primary"
            label="Generate"
            variant="contained"
            fullWidth={false}
          />
        </Stack>
      </FormProvider>
    </StyledContainer>
  );
};
