import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  flexDirection: 'row',
  flexWrap: 'wrap',
  gap: theme.spacing(3),
  padding: theme.spacing(3),
  borderRadius: theme.spacing(1.25),
  backgroundColor: theme.palette.neutrals.N100.main,
}));

export const StyledFormContainer = styled(Stack)(({ theme }) => ({
  gap: theme.spacing(1.5),
  borderRadius: theme.spacing(1.25),
  flexDirection: 'row',
  flexWrap: 'wrap',
  '&::-webkit-scrollbar': {
    background: '#DCE5F2',
    borderRadius: '5px',
    width: '5px',
  },
  '&:: -webkit-scrollbar-thumb': {
    background: '#99A3B4',
    borderRadius: '5px',
  },
  '& .Tag-container': {
    height: '20px',
  },
}));
