import { FieldValues, UseFormProps } from 'react-hook-form';
import { TKeyOfOptions } from '@/lib/hooks/queries/setting';
import { TNameRoot } from '@/lib/types/entities/category';
import { TSelectOption } from '../RHFItems/RHFSelect/RHFSelect';
import { TCustomFieldOptions, TKeyOfForm } from './fieldOptions';
import { EBoolString } from '@/lib/types/enum/balance';

export type TFormValues = {
  keySearch: string;
  categoryId?: TSelectOption[];
  statusId?: TSelectOption[];
  startDate?: string;
  endDate?: string;
  rangeDateType?: TSelectOption;
  treatedById?: TSelectOption[];
  saleEmployee?: TSelectOption[];
  showInactiveTherapists?: EBoolString;
};

// Define generic types for the form values and props
export type TReportFilterProps<T extends FieldValues> = {
  fieldOptionNames: TKeyOfForm[];
  optionSettings?: TKeyOfOptions[];
  categoryName?: TNameRoot;
  useFormProps?: UseFormProps<T>;
  creditTypeOptions?: TSelectOption[];
  rangeDateLabel?: string;
  onFilter: (values: T) => void;
  customOptions?: Record<string, TSelectOption[]>;
  customFieldOptions?: TCustomFieldOptions;
};
