import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import { Price } from './Price';

export default {
  title: 'Molecules/Price',
  component: Price,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof Price>;

export const Default: Story = {
  args: {
    amount: 100,
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <Price {...args} />
    </Box>
  ),
};
