import Stack, { StackProps } from '@mui/material/Stack';
import { useSession } from 'next-auth/react';
import React, { ReactNode } from 'react';
import { round2decimal } from '@/lib/utils/string';
import { TBranch } from '@/lib/types/entities/branch';
import Typography, { ITypographyProps } from '@/components/Atoms/Typography';

export type TPriceProps = {
  amount?: number;
  typographyProps?: ITypographyProps;
  containerProps?: StackProps;
  branch?: TBranch;
  prefix?: string;
  suffix?: string | ReactNode;
  showTwoDecimalPlace?: boolean;
  showSymbol?: boolean;
};

export const Price: React.FC<TPriceProps> = ({
  prefix = '',
  amount = 0,
  suffix = '',
  typographyProps,
  containerProps,
  branch,
  showTwoDecimalPlace,
  showSymbol = true,
}) => {
  const { data } = useSession();
  const branchUsing = branch || data?.user?.branchChosen;
  const currency = branchUsing?.currency;
  const symbol = showSymbol ? `${currency?.symbol || '$'} ` : '';
  return (
    <Stack {...containerProps}>
      <Typography {...typographyProps}>
        {prefix}
        {`${symbol}${showTwoDecimalPlace ? amount?.toFixed(2) : round2decimal(amount || 0)}`}
        {suffix}
      </Typography>
    </Stack>
  );
};
