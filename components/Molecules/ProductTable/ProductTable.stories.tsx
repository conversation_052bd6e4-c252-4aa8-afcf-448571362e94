import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import ProductTable from '.';
import { IProductColumn } from './ProductTable.types';
import { TProduct } from '@/lib/types/entities/product';

export default {
  title: 'Molecules/ProductTable',
  component: ProductTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof ProductTable>;

const SAMPLE_ROWS: TProduct[] = [];

const SAMPLE_COLUMNS: IProductColumn[] = [
  {
    name: 'name',
    label: 'NAME',
  },
  {
    name: 'cost',
    label: 'COST',
  },
  {
    name: 'price',
    label: 'PRICE',
  },
  {
    name: 'status',
    label: 'STATUS',
  },
  {
    name: 'actions',
    label: ' ',
  },
];

export const Default: Story = {
  args: {
    loading: false,
    rows: SAMPLE_ROWS,
    columns: SAMPLE_COLUMNS,
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <ProductTable {...args} />
    </Box>
  ),
};
