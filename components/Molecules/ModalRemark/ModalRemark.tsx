import Stack from '@mui/material/Stack';
import { useEffect, useState } from 'react';
import { Theme } from '@mui/material';
import { dialogClasses } from '@mui/material/Dialog';
import { TextField } from '@/components/Atoms/TextField/TextField';
import Modal from '@/components/Atoms/Modal';
import { StyledButton, StyledButtonWrapper } from './ModalRemark.styled';

type TFormValues = {
  remark: string;
};

type TModalRemark = {
  isOpen: boolean;
  defaultValues?: TFormValues;
  handleClose: () => void;
  handleConfirm: (value: TFormValues) => void;
};

export const ModalRemark: React.FC<TModalRemark> = ({ isOpen, handleClose, handleConfirm, defaultValues }) => {
  const [remark, setRemark] = useState<string>();

  useEffect(() => {
    setRemark(defaultValues?.remark || '');
    return () => setRemark('');
  }, [defaultValues]);

  return (
    <Modal
      isOpen={isOpen}
      handleClose={handleClose}
      title="REMARK"
      dialogProps={{
        sx: (theme: Theme) => ({
          [`& .${dialogClasses.paper}`]: {
            maxWidth: theme.spacing(500 / 8),
            height: 'fit-content',
            padding: theme.spacing(3),
            maxHeight: 'calc(100% - 48px)',
            backgroundColor: theme.palette.neutrals.N100.main,
            borderRadius: theme.spacing(1.25),
            [theme.breakpoints.down('md')]: {
              maxWidth: 'auto',
              padding: theme.spacing(2),
            },
          },
        }),
      }}
    >
      <Stack>
        <TextField
          value={remark}
          onChange={e => {
            setRemark(e?.target?.value);
          }}
          sx={{ width: '500px' }}
          id="filled-multiline-static"
          label="Notes"
          multiline
          rows={7}
        />
        <StyledButtonWrapper>
          <StyledButton variant="outlined" label="Cancel" size="large" loadingText="Cancel" onClick={handleClose} />
          <StyledButton
            variant="contained"
            type="submit"
            label="Save"
            size="large"
            loadingText="Saving"
            onClick={() => {
              handleConfirm({ remark: remark || '' });
              setRemark('');
            }}
          />
        </StyledButtonWrapper>
      </Stack>
    </Modal>
  );
};
