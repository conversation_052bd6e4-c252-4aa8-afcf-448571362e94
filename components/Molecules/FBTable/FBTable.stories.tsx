import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import FBTable from '.';
import { IFBColumn } from './FBTable.types';
import { TFoodBeverage } from '@/lib/types/entities/foodBeverage';

export default {
  title: 'Molecules/FBTable',
  component: FBTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof FBTable>;

const SAMPLE_ROWS: TFoodBeverage[] = [];

const SAMPLE_COLUMNS: IFBColumn[] = [
  {
    name: 'name',
    label: 'NAME',
  },
  {
    name: 'name',
    label: 'BRANCH',
  },
  {
    name: 'name',
    label: 'SECTION',
  },
  {
    name: 'name',
    label: 'CATEGORY',
  },
  {
    name: 'actions',
    label: ' ',
  },
];

export const Default: Story = {
  args: {
    loading: false,
    rows: SAMPLE_ROWS,
    columns: SAMPLE_COLUMNS,
  },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <FBTable {...args} />
    </Box>
  ),
};
