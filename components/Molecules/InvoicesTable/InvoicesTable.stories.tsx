import Box from '@mui/material/Box';
import { StoryObj } from '@storybook/react';
import { TInvoice } from '@/lib/types/entities/invoice';
import { concatenateNames } from '@/lib/utils/string';
import InvoicesTable from '.';
import { Price } from '../Price/Price';
import { IInvoiceColumn } from './InvoicesTable.types';

export default {
  title: 'Molecules/InvoicesTable',
  component: InvoicesTable,
  tags: ['autodocs'],
};

type Story = StoryObj<typeof InvoicesTable>;

const SAMPLE_ROWS: TInvoice[] = [];
const SAMPLE_COLUMNS: IInvoiceColumn[] = [
  {
    name: 'code',
    label: 'INVOICE',
  },
  {
    name: 'customer',
    label: 'CUSTOMER',
    render: (row: TInvoice) => concatenateNames(row?.customer?.firstName || '', row?.customer?.lastName || ''),
  },
  {
    name: 'customer',
    label: 'PHONE',
    render: (row: TInvoice) => row?.customer?.phone,
  },
  {
    name: 'total',
    label: 'TOTAL',
    render: (row: TInvoice) => <Price amount={row?.total} typographyProps={{ variant: 'body-xlarge-400' }} />,
  },
  {
    name: 'invoicePayments',
    label: 'PAYMENT',
    render: (row: TInvoice) =>
      row?.invoicePayments?.map(invoicePayment => invoicePayment?.paymentMethod?.name)?.join(', '),
  },
  {
    name: 'total',
    label: 'CREDIT',
    render: (row: TInvoice) => 0,
  },
  {
    name: 'paid',
    label: 'PAID',
    render: (row: TInvoice) => <Price amount={row?.paid} typographyProps={{ variant: 'body-xlarge-400' }} />,
  },
  {
    name: 'status',
    label: 'STATUS',
  },
  {
    name: 'actions',
    label: '',
    render: (row: TInvoice) => <></>,
  },
];

export const Default: Story = {
  args: { rows: SAMPLE_ROWS, columns: SAMPLE_COLUMNS },
  render: args => (
    <Box sx={{ width: '100%', backgroundColor: '#EADFCE' }}>
      <InvoicesTable {...args} />
    </Box>
  ),
};
