import { Theme } from '@mui/material/styles';
import React from 'react';
import CustomTable from '@/components/Atoms/Table';
import { TInvoicesTableProps } from './InvoicesTable.types';

export const InvoicesTable: React.FC<TInvoicesTableProps> = ({ rows, columns, ...restProps }) => (
  <CustomTable
    stickyHeader
    columns={columns.map(column => ({
      tableCellProps: {
        sx: (theme: Theme) => ({
          '&.MuiTableCell-root': {
            borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
            padding: theme.spacing(2, 1),
          },
          '&.MuiTableCell-body': {
            verticalAlign: 'middle',
            ...theme.typography['body-xlarge-400'],
          },
          '& > h5': {
            fontSize: theme.typography['heading-xsmall-700'],
            textTransform: 'uppercase',
          },
        }),
      },
      ...column,
    }))}
    sttProps={{
      sx: (theme: Theme) => ({
        '&.MuiTableCell-root': {
          verticalAlign: 'middle',
          borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
          minWidth: theme.spacing(6),
        },
      }),
    }}
    rows={rows}
    tableContainerProps={{
      sx: { padding: '0 24px 24px', maxHeight: 'calc(100vh - 64px - 24px - 50px - 24px - 98px)', overflow: 'auto' },
    }}
    showSTT
    hasHeaderBackground={false}
    infinitiLoad
    {...restProps}
  />
);
