'use client';

import React from 'react';
import FormControl from '@mui/material/FormControl';
import dayjs, { Dayjs } from 'dayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { isEmpty } from 'lodash';
import Stack from '@mui/material/Stack';
import { StyledTimeField, StyledLabel } from './RangeTimeField.styled';
import { TRangeTimeFieldProps } from './RangeTimeField.types';
import Typography from '../Typography';

export const RangeTimeField: React.FC<TRangeTimeFieldProps> = ({
  formControlProps,
  startTimeFieldName,
  endTimeFieldName,
  label,
  value,
  onChange,
  error,
}) => {
  const transformStartTimeValue = isEmpty(value[startTimeFieldName]) ? null : dayjs(value[startTimeFieldName]);
  const transformEndTimeValue = isEmpty(value[startTimeFieldName]) ? null : dayjs(value[startTimeFieldName]);
  return (
    <FormControl {...formControlProps}>
      {label && <StyledLabel variant="heading-medium-700">{label}</StyledLabel>}
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Stack direction="row" alignItems="center" gap={1.5}>
          <StyledTimeField
            value={transformStartTimeValue}
            onChange={newValue =>
              onChange && onChange({ field: startTimeFieldName, value: (newValue as Dayjs)?.toDate()?.toUTCString() })
            }
            format="HH:mm"
            InputLabelProps={{
              shrink: true,
            }}
            InputProps={{
              error,
            }}
          />
          <Typography variant="body-xlarge-400">To</Typography>
          <StyledTimeField
            value={transformEndTimeValue}
            onChange={newValue =>
              onChange && onChange({ field: endTimeFieldName, value: (newValue as Dayjs)?.toDate()?.toUTCString() })
            }
            format="HH:mm"
            InputLabelProps={{
              shrink: true,
            }}
            InputProps={{
              error,
            }}
          />
        </Stack>
      </LocalizationProvider>
    </FormControl>
  );
};
