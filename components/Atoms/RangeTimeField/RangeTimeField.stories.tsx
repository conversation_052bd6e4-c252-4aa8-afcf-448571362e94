import { Meta, StoryObj } from '@storybook/react';
import { RangeTimeField } from './RangeTimeField';

export default {
  title: 'Atoms/RangeTimeField',
  component: RangeTimeField,
  tags: ['autodocs'],
} satisfies Meta<typeof RangeTimeField>;

type Story = StoryObj<typeof RangeTimeField>;

export const Default: Story = {
  args: { label: 'this is label' },
  render: args => (
    <div style={{ width: '200px' }}>
      <RangeTimeField {...args} />
    </div>
  ),
};
