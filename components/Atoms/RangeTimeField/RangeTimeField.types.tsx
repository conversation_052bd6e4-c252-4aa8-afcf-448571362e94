'use client';

import { FormControlProps } from '@mui/material/FormControl';
import { TOption } from '@/components/Molecules/RHFItems/RHFItems.types';

export type TRangeTimeFieldProps<T = TOption> = {
  label?: string;
  value: Record<string, string>;
  startTimeFieldName: string;
  endTimeFieldName: string;
  formControlProps?: FormControlProps;
  error?: boolean;
  onChange?: (val: { field: string; value: string }) => void;
};
