import CircularProgress from '@mui/material/CircularProgress';
import { ButtonProps } from '@mui/material/Button';
import React, { forwardRef, ReactNode } from 'react';
import Icon, { IconVariantTypes } from '../Icon';
import { StyledButton } from './Button.styled';
import Typography from '../Typography';

export interface IButtonProps extends ButtonProps {
  label?: string | ReactNode;
  variant?: 'outlined' | 'contained';
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning' | 'warning';
  startIcon?: IconVariantTypes;
  endIcon?: IconVariantTypes;
  isLoading?: boolean;
  loadingIcon?: ReactNode;
  loadingText?: string;
  onClick?: () => void;
}

export const Button: React.FC<IButtonProps> = forwardRef(
  (
    {
      label,
      variant = 'outlined',
      color = 'primary',
      startIcon,
      endIcon,
      isLoading,
      loadingIcon,
      loadingText = 'Loading ...',
      onClick,
      ...otherProps
    },
    ref
  ) => (
    <StyledButton
      color={color}
      variant={variant}
      disabled={isLoading}
      startIcon={!isLoading && startIcon && <Icon variant={startIcon} />}
      endIcon={!isLoading && endIcon && <Icon variant={endIcon} />}
      onClick={onClick}
      ref={ref}
      {...otherProps}
    >
      {isLoading && (loadingIcon || <CircularProgress size={14} thickness={5} />)}
      {isLoading && <Typography variant="heading-medium-700">{loadingText}</Typography>}
      {!isLoading && (typeof label === 'string' ? <span>{label}</span> : label)}
    </StyledButton>
  )
);
