import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta = {
  title: 'Atoms/Button',
  component: Button,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof Button>;

export const Primary: Story = {
  args: { variant: 'contained', color: 'primary', label: 'Add survey', loadingText: 'Adding survey...' },
  render: args => (
    <Box width="327px">
      <Button {...args} />
    </Box>
  ),
};

export const Success: Story = {
  args: { variant: 'contained', color: 'success', label: 'Submit', loadingText: 'Submitting...' },
  render: args => (
    <Box width="327px">
      <Button {...args} />
    </Box>
  ),
};

export const Error: Story = {
  args: { variant: 'contained', color: 'error', label: 'Delete', loadingText: 'Deleting...' },
  render: args => (
    <Box width="327px">
      <Button {...args} />
    </Box>
  ),
};

export const Info: Story = {
  args: { variant: 'outlined', color: 'info', label: 'Scan QR', loadingText: 'Scanning QR...' },
  render: args => (
    <Box width="327px" sx={{ backgroundColor: '#F2EADC', padding: '20px' }}>
      <Button {...args} />
    </Box>
  ),
};

export const Warning: Story = {
  args: { variant: 'contained', color: 'warning', label: 'Check out', loadingText: 'Checking out...' },
  render: args => (
    <Box width="327px">
      <Button {...args} />
    </Box>
  ),
};

export const StartIcon: Story = {
  args: {
    variant: 'contained',
    color: 'primary',
    startIcon: 'print',
    label: 'Print receipt',
    loadingText: 'Printing receipt...',
  },
  render: args => (
    <Box width="327px">
      <Button {...args} />
    </Box>
  ),
};

export const EndIcon: Story = {
  args: {
    variant: 'contained',
    color: 'primary',
    endIcon: 'print',
    label: 'Check out',
    loadingText: 'Checking out...',
  },
  render: args => (
    <Box width="327px">
      <Button {...args} />
    </Box>
  ),
};
