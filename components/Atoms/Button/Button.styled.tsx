import styled from '@emotion/styled';
import Button from '@mui/material/Button';
import { buttonClasses, circularProgressClasses } from '@mui/material';

export const StyledButton = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    width: '100%',
    textTransform: 'none',
    letterSpacing: theme.spacing(0.3 / 8),
    borderRadius: theme.spacing(1.25),
    boxShadow: 'none',
    ...theme.typography['heading-medium-700'],
  },

  [`&.${buttonClasses.sizeMedium}`]: {
    padding: theme.spacing(1.5),
  },

  [`&.${buttonClasses.sizeLarge}`]: {
    padding: theme.spacing(1.5, 4),
  },

  [`&.${buttonClasses.disabled}`]: {
    cursor: 'not-allowed',
    pointerEvents: 'auto',
    opacity: 0.5,
  },

  [`& .${buttonClasses.startIcon}`]: {
    marginLeft: 0,
    marginRight: theme.spacing(1.25),
    '& img': {
      width: theme.spacing(2.25),
      height: theme.spacing(2.25),
    },
  },

  [`& .${buttonClasses.endIcon}`]: {
    marginRight: 0,
    marginLeft: theme.spacing(1.25),
    '& img': {
      width: theme.spacing(2.25),
      height: theme.spacing(2.25),
    },
  },

  [`& .${circularProgressClasses.root}`]: {
    marginLeft: 0,
    marginRight: theme.spacing(1.25),
  },

  [`&.${buttonClasses.containedPrimary}`]: {
    color: theme.palette.common.white,
    border: `1px solid ${theme.palette.neutrals.N50.main}`,
    backgroundColor: theme.palette.neutrals.N50.main,
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.common.white,
    },
  },

  [`&.${buttonClasses.outlinedPrimary}`]: {
    color: theme.palette.neutrals.N50.main,
    border: `1px solid ${theme.palette.neutrals.N50.main}`,
    backgroundColor: 'transparent',
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.neutrals.N50.main,
    },
    // '&:hover': {
    //   color: theme.palette.common.white,
    //   backgroundColor: theme.palette.neutrals.N50.main,
    // },
  },

  [`&.${buttonClasses.containedSuccess}`]: {
    color: theme.palette.common.white,
    border: `1px solid ${theme.palette.success.main}`,
    backgroundColor: theme.palette.success.main,
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.common.white,
    },
  },

  [`&.${buttonClasses.containedError}`]: {
    color: theme.palette.common.white,
    border: `1px solid ${theme.palette.error.main}`,
    backgroundColor: theme.palette.error.main,
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.common.white,
    },
  },

  [`&.${buttonClasses.outlinedSuccess}`]: {
    color: theme.palette.success.main,
    border: `1px solid ${theme.palette.success.main}`,
    backgroundColor: 'transparent',
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.success.main,
    },
    // '&:hover': {
    //   color: theme.palette.common.white,
    //   backgroundColor: theme.palette.success.main,
    // },
  },

  [`&.${buttonClasses.containedError}`]: {
    color: theme.palette.common.white,
    border: `1px solid ${theme.palette.error.main}`,
    backgroundColor: theme.palette.error.main,
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.common.white,
    },
  },

  [`&.${buttonClasses.outlinedError}`]: {
    color: theme.palette.error.main,
    border: `1px solid ${theme.palette.error.main}`,
    backgroundColor: 'transparent',
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.error.main,
    },
    // '&:hover': {
    //   color: theme.palette.common.white,
    //   backgroundColor: theme.palette.error.main,
    // },
  },

  [`&.${buttonClasses.containedWarning}`]: {
    color: theme.palette.common.white,
    border: `1px solid ${theme.palette.warning.main}`,
    backgroundColor: theme.palette.warning.main,
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.common.white,
    },
  },

  [`&.${buttonClasses.outlinedWarning}`]: {
    color: theme.palette.warning.main,
    border: `1px solid ${theme.palette.warning.main}`,
    backgroundColor: 'transparent',
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.warning.main,
    },
    // '&:hover': {
    //   color: theme.palette.common.white,
    //   backgroundColor: theme.palette.warning.main,
    // },
  },

  [`&.${buttonClasses.containedInfo}`]: {
    color: theme.palette.common.white,
    border: `1px solid ${theme.palette.info.main}`,
    backgroundColor: theme.palette.info.main,
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.common.white,
    },
  },

  [`&.${buttonClasses.outlinedInfo}`]: {
    color: theme.palette.info.main,
    border: `1px solid ${theme.palette.info.main}`,
    backgroundColor: 'transparent',
    [`& .${circularProgressClasses.root}`]: {
      color: theme.palette.info.main,
    },
    // '&:hover': {
    //   color: theme.palette.common.white,
    //   backgroundColor: theme.palette.info.main,
    // },
  },
}));
