import React, { ReactNode } from 'react';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import AccordionDetails, { AccordionDetailsProps } from '@mui/material/AccordionDetails';
import AccordionSummary, { AccordionSummaryProps, accordionSummaryClasses } from '@mui/material/AccordionSummary';
import MuiAccordion, { AccordionProps, accordionClasses } from '@mui/material/Accordion';
import { Theme } from '@mui/material';
import Typography from '../Typography';

export type TAccordionItem = {
  key: string;
  hasDivider?: boolean;
  header: ReactNode | string;
  content: ReactNode | string;
  accordionProps?: Omit<AccordionProps, 'children'>;
  headerProps?: AccordionSummaryProps;
  contentProps?: AccordionDetailsProps;
};

export type TAccordionProps = {
  items: TAccordionItem[];
  activeKeys: string[];
  onChange: (activeKey: string) => void;
  variant: 'styled' | 'unstyled';
};

export const Accordion: React.FC<TAccordionProps> = ({ items, activeKeys, variant, onChange }) => {
  const handleChange = (activeKey: string) => (event: React.SyntheticEvent, newExpanded: boolean) => {
    onChange(activeKey);
  };
  const defaultStyledHeaderProps = {
    sx: (theme: Theme) => ({
      [`&.${accordionSummaryClasses.root}`]: {
        width: '100%',
        minHeight: 'fit-content',
        padding: theme.spacing(1.5, 3),
        backgroundColor: theme.palette.neutrals.N50.main,
        color: theme.palette.common.white,
        textTransform: 'capitalize',
        ...theme.typography['heading-large-700'],
        [`&.${accordionSummaryClasses.expanded}`]: {
          minHeight: 'fit-content',
          margin: 0,
        },
        [`&.${accordionSummaryClasses.disabled}`]: {
          opacity: 1,
        },
      },
      [`& .${accordionSummaryClasses.content}`]: {
        margin: 0,
      },
      [`& .${accordionSummaryClasses.content}.${accordionSummaryClasses.expanded}`]: {
        margin: 0,
      },
      [`& .${accordionSummaryClasses.expandIconWrapper}`]: {
        width: theme.spacing(3),
        height: theme.spacing(3),
        justifyContent: 'center',
        alignItems: 'center',
        color: theme.palette.neutrals.N50.main,
        backgroundColor: theme.palette.common.white,
        borderRadius: '50%',
        [`&.${accordionSummaryClasses.expanded}`]: {
          transform: 'rotate(90deg)',
        },
      },
    }),
  };
  const defaultUnstyledHeaderProps = {
    sx: (theme: Theme) => ({
      [`&.${accordionSummaryClasses.root}`]: {
        width: '100%',
        minHeight: 'fit-content',
        padding: 0,
        flexDirection: 'row-reverse',
        backgroundColor: 'transparent',
        [`&.${accordionSummaryClasses.expanded}`]: {
          minHeight: 'fit-content',
          margin: 0,
        },
        [`&.${accordionSummaryClasses.disabled}`]: {
          opacity: 1,
        },
      },
      [`& .${accordionSummaryClasses.content}`]: {
        margin: 0,
      },
      [`& .${accordionSummaryClasses.content}.${accordionSummaryClasses.expanded}`]: {
        margin: 0,
      },
      [`& .${accordionSummaryClasses.expanded} svg`]: {
        transform: 'rotate(180deg)',
      },
      [`& .${accordionSummaryClasses.expandIconWrapper}`]: {
        width: theme.spacing(3),
        height: theme.spacing(3),
        justifyContent: 'center',
        alignItems: 'center',
        color: theme.palette.neutrals.N50.main,
        backgroundColor: theme.palette.common.white,
        borderRadius: '50%',
      },
    }),
  };
  const defaultStyledContentProps = {
    sx: (theme: Theme) => ({
      margin: 0,
      padding: theme.spacing(1, 0),
    }),
  };
  const defaultUnstyledContentProps = {
    sx: (theme: Theme) => ({
      margin: 0,
      padding: theme.spacing(1, 0),
    }),
  };
  const defaultStyledAccordionProps = {
    sx: (theme: Theme) => ({
      borderRadius: theme.spacing(1.25),
      [`&.${accordionClasses.expanded}`]: {
        margin: 0,
      },
      [`&.${accordionClasses.disabled}`]: {
        backgroundColor: 'transparent',
      },
      overflow: 'hidden',
      '&:not(:last-child)': {
        borderBottom: 0,
      },
      '&:before': {
        display: 'none',
      },
    }),
  };
  const defaultUnstyledAccordionProps = {
    sx: (theme: Theme) => ({
      [`&.${accordionClasses.expanded}`]: {
        margin: 0,
      },
      [`&.${accordionClasses.disabled}`]: {
        backgroundColor: 'transparent',
      },
      overflow: 'hidden',
      '&:not(:last-child)': {
        borderBottom: 0,
      },
      '&:before': {
        display: 'none',
      },
    }),
  };
  return (
    <Stack gap={1}>
      {items?.map(({ key, hasDivider, header, content, accordionProps, headerProps, contentProps }, index) => (
        <>
          <MuiAccordion
            key={`accordion-item-${key}`}
            elevation={0}
            square
            expanded={activeKeys.includes(key)}
            onChange={handleChange(key)}
            {...(variant === 'styled' ? defaultStyledAccordionProps : defaultUnstyledAccordionProps)}
            {...accordionProps}
          >
            <AccordionSummary
              id={`${key}-header`}
              aria-controls={`${key}-content`}
              {...(variant === 'styled' ? defaultStyledHeaderProps : defaultUnstyledHeaderProps)}
              {...headerProps}
            >
              {typeof header === 'string' ? (
                <Typography component="p" variant="heading-medium-700">
                  {header}
                </Typography>
              ) : (
                header
              )}
            </AccordionSummary>
            <AccordionDetails
              {...(variant === 'styled' ? defaultStyledContentProps : defaultUnstyledContentProps)}
              {...contentProps}
            >
              {content}
            </AccordionDetails>
          </MuiAccordion>
          {index !== items.length - 1 && hasDivider && (
            <Divider
              sx={(theme: Theme) => ({
                borderBottomWidth: theme.spacing(1 / 8),
                borderColor: theme.palette.neutrals.N40.main,
              })}
            />
          )}
        </>
      ))}
    </Stack>
  );
};
