import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import { Accordion, TAccordionProps } from './Accordion';

export default {
  title: 'Atoms/Accordion',
  component: Accordion,
  tags: ['autodocs'],
} satisfies Meta<TAccordionProps>;

type Story = StoryObj<typeof Accordion>;

export const Default: Story = {
  args: {
    activeKeys: ['1', '2'],
    items: [
      {
        key: '1',
        header: 'Product',
        content: '',
      },
      {
        key: '2',
        header: 'Service',
        content: '',
      },
    ],
    onChange: (activeKey: string) => {},
    variant: 'unstyled',
  },
  render: args => (
    <Box width="100%">
      <Accordion {...args} />
    </Box>
  ),
};
