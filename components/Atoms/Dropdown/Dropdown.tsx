import React, { ReactNode, useId, useState } from 'react';
import Popover from '@mui/material/Popover';
import { ButtonProps } from '@mui/material/Button';
import { StyledDropdownAnchor, StyledDropdownItem, StyledDropdownItemsWrapper } from './Dropdown.styled';

export type TDropdownItem = {
  label: string;
  onClick: () => void;
  disabled?: boolean;
};
export type TDropdownProps = {
  label: string | ReactNode;
  items: TDropdownItem[];
  isCloseAfterClick?: boolean;
};

export const Dropdown: React.FC<TDropdownProps> = ({ label, items, isCloseAfterClick }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [open, setOpen] = useState<boolean>(false);

  const handleClose = () => {
    setAnchorEl(null);
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };
  const id = open ? 'dropdown-popover' : undefined;
  return (
    <>
      <StyledDropdownAnchor
        variant="contained"
        color="primary"
        label={label}
        endIcon="chevron_down"
        onClick={handleOpen}
        ref={(ref: HTMLButtonElement) => {
          setAnchorEl(ref);
        }}
      />
      <Popover
        id={id}
        open={open && Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={theme => ({
          marginTop: theme.spacing(0.5),
        })}
        disableRestoreFocus
      >
        <StyledDropdownItemsWrapper
          sx={{
            width: `${anchorEl?.getBoundingClientRect()?.width}px !important`,
          }}
        >
          {items?.map(({ label, onClick, ...rest }) => {
            const handleClick = () => {
              onClick();
              if (isCloseAfterClick) {
                handleClose();
              }
            };
            return (
              <StyledDropdownItem
                key={`dropdown-item-${useId()}`}
                variant="contained"
                label={label}
                onClick={handleClick}
                {...rest}
              />
            );
          })}
        </StyledDropdownItemsWrapper>
      </Popover>
    </>
  );
};
