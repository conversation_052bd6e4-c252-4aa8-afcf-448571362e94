import styled from '@emotion/styled';
import { buttonClasses } from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Button from '../Button';

export const StyledDropdownItemsWrapper = styled(Stack)(({ theme }) => ({
  width: '100%',
  overflow: 'hidden',
  borderRadius: theme.spacing(1.25),
}));

export const StyledDropdownAnchor = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    padding: theme.spacing(1.5, 5),
    ...theme.typography['heading-medium-700'],
  },
}));

export const StyledDropdownItem = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    borderRadius: 0,
    border: 'none',
    color: theme.palette.neutrals.N90.main,
    backgroundColor: theme.palette.common.white,
    ...theme.typography['body-xlarge-400'],
    transition: 'all .2s',
    '&:hover': {
      backgroundColor: theme.palette.neutrals.N150.main,
      ...theme.typography['heading-medium-700'],
    },
  },
}));
