import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import { Dropdown } from './Dropdown';

const meta: Meta = {
  title: 'Atoms/Dropdown',
  component: Dropdown,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof Dropdown>;

export const Default: Story = {
  args: {
    label: 'Add new',
    items: [
      {
        label: 'Add Appointment',
        onClick: () => {},
      },
      {
        label: 'Add Membership',
        onClick: () => {},
      },
    ],
  },
  render: args => (
    <Box width="fit-content">
      <Dropdown {...args} />
    </Box>
  ),
};
