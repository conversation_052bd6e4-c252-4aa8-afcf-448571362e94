import styled from '@emotion/styled';
import Box from '@mui/material/Box';
import Tab, { tabClasses } from '@mui/material/Tab';
import Tabs, { tabsClasses } from '@mui/material/Tabs';
import { CSSProperties } from 'react';

export const StyledTabsWrapper = styled(Box)(({ theme }) => ({
  width: '100%',
  height: '100%',
  maxWidth: '100%',
  overflow: 'auto',
  background: 'white',
  borderRadius: theme.spacing(1.25),
}));

export const StyledTabsNavWrapper = styled(Box)<{ tabsNavStyles?: CSSProperties }>(({ tabsNavStyles, theme }) => ({
  width: '100%',
  maxWidth: '100%',
  overflow: 'auto',
  borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
  ...(tabsNavStyles || {
    marginBottom: theme.spacing(3),
  }),
}));

export const StyledTabsContentWrapper = styled(Box)<{ tabsContentStyles?: CSSProperties }>(
  ({ theme, tabsContentStyles }) => ({
    width: '100%',
    maxWidth: '100%',
    height: '100%',
    overflow: 'auto',
    ...(tabsContentStyles || {
      padding: theme.spacing(2.25, 3),
    }),
  })
);

export const StyledTabContentWrapper = styled(Box)(() => ({
  width: '100%',
  maxWidth: '100%',
  overflow: 'auto',
}));

export const StyledTabs = styled(Tabs)<{ tabsBarGutter: number; tabsBarStyles?: CSSProperties }>(
  ({ theme, tabsBarGutter, tabsBarStyles }) => ({
    [`&.${tabsClasses.root}`]: {
      minHeight: 'auto',
      ...(tabsBarStyles || {
        padding: theme.spacing(2.25, 3),
      }),
    },
    [`& .${tabsClasses.indicator}`]: {
      height: theme.spacing(3 / 8),
      backgroundColor: theme.palette.neutrals.N50.main,
    },
    [`& .${tabsClasses.flexContainer}`]: {
      gap: theme.spacing(tabsBarGutter),
    },
  })
);

export const StyledTab = styled(Tab)(({ theme }) => ({
  [`&.${tabClasses.root}`]: {
    minWidth: 'fit-content',
    minHeight: 'auto',
    padding: 0,
    paddingBottom: theme.spacing(1),
    fontSize: theme.spacing(2),
    fontWeight: theme.typography.fontWeightRegular,
    color: theme.palette.neutrals.N50.main,
    textTransform: 'capitalize',
    '&:hover': {
      fontWeight: theme.typography.fontWeightBold,
    },
  },
  [`&.${tabClasses.selected}`]: {
    fontWeight: theme.typography.fontWeightBold,
  },
}));
