import React, { ReactNode, useEffect, useState, CSSProperties } from 'react';
import {
  StyledTab,
  StyledTabContentWrapper,
  StyledTabs,
  StyledTabsContentWrapper,
  StyledTabsNavWrapper,
  StyledTabsWrapper,
} from './Tabs.styled';

export interface ITabItem {
  key: string;
  label: string;
  children: ReactNode | string;
  disabled?: boolean;
}

export interface ITabsProps {
  items: ITabItem[];
  defaultKey: string;
  tabsBarGutter?: number;
  tabsNavStyles?: CSSProperties;
  tabsBarStyles?: CSSProperties;
  tabsContentStyles?: CSSProperties;
  onChange?: (activeKey: string) => void;
}

export interface ITabPanelProps {
  item: ITabItem;
  isActive: boolean;
}

export const Tabs: React.FC<ITabsProps> = ({
  items,
  defaultKey,
  tabsBarGutter = 3,
  tabsBarStyles,
  tabsNavStyles,
  tabsContentStyles,
  onChange,
}) => {
  if (items.length === 0) return <></>;
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0);

  const handleChange = (event: React.SyntheticEvent, newActiveIndex: number) => {
    setActiveTabIndex(newActiveIndex);
    if (onChange) onChange(items?.[newActiveIndex]?.key || '');
  };

  useEffect(() => {
    if (defaultKey) {
      const currActiveIndex = items.findIndex(item => item.key === defaultKey);
      setActiveTabIndex(currActiveIndex > -1 ? currActiveIndex : 0);
    }
  }, [defaultKey]);
  return (
    <StyledTabsWrapper data-name="TabContainer">
      <StyledTabsNavWrapper tabsNavStyles={tabsNavStyles}>
        <StyledTabs
          value={activeTabIndex}
          onChange={handleChange}
          variant="scrollable"
          scrollButtons={false}
          tabsBarGutter={tabsBarGutter}
          tabsBarStyles={tabsBarStyles}
          sx={{ height: '100%' }}
        >
          {items.map(item => (
            <StyledTab key={`tab-${item.key}`} label={item.label} disableRipple />
          ))}
        </StyledTabs>
      </StyledTabsNavWrapper>
      <StyledTabsContentWrapper tabsContentStyles={tabsContentStyles}>
        {items.map((item, index) => (
          <StyledTabContentWrapper key={`tab-pannel-${item.key}`}>
            {activeTabIndex === index && item.children}
          </StyledTabContentWrapper>
        ))}
      </StyledTabsContentWrapper>
    </StyledTabsWrapper>
  );
};
