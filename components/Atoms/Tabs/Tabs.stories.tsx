import { Meta, StoryObj } from '@storybook/react';
import { ITabItem, Tabs } from './Tabs';

const meta: Meta = {
  title: 'Atoms/Tabs',
  component: Tabs,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof Tabs>;

const samepleItems: ITabItem[] = Array.from({ length: 10 }, (_, i) => ({
  key: `tab-${i + 1}`,
  label: `Tab ${i + 1}`,
  children: <div style={{ height: '300px', background: 'white' }}>tab panel {i + 1}</div>,
}));

export const Default: Story = {
  args: {
    items: samepleItems,
    defaultKey: samepleItems[0]['key'],
    tabsBarGutter: 3,
    tabsBarStyles: {
      padding: '18px 24px',
    },
    tabsContentStyles: {
      padding: '12px 24px',
    },
  },
  render: args => (
    // <Box sx={{ border: '1px solid grey' }}>
    <Tabs {...args} />
    // </Box>
  ),
};
