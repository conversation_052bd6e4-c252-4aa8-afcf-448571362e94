import { AutocompleteProps, SxProps, Theme } from '@mui/material';
import { formControlClasses } from '@mui/material/FormControl';
import Stack from '@mui/material/Stack';
import React, { useId } from 'react';
import { TOption } from '@/components/Molecules/RHFItems/RHFAutocomplete/RHFAutocomplete';
import { TextField } from '@/components/Atoms/TextField/TextField';
import Checkbox from '../Checkbox';
import { TTextFieldProps } from '../TextField/TextField.types';
import { StyledAutoComplete, StyledLabel, StyledPopper } from './Autocomplete.styled';
import Typography from '../Typography';

export type TAutocompleteProps<T = TOption> = {
  label?: string;
  name: string;
  options?: T[];
  textfieldProps?: TTextFieldProps;
  showValue?: boolean;
  defaultValue?: T[];
  sxLabel?: SxProps<Theme>;
  sxContainer?: SxProps<Theme>;
  isHideDeleteWorker?: boolean;
  error?: boolean;
  helperText?: string;
  showCheckbox?: boolean;
  handleOnChange?: (value: (string | T)[] | T | any) => void;
  disabled?: boolean;
} & Partial<AutocompleteProps<T, any, any, any>>;

export const Autocomplete = <T extends TOption>({
  value,
  onChange,
  ref,
  options,
  textfieldProps,
  showValue,
  sxLabel,
  isHideDeleteWorker,
  sxContainer,
  error,
  helperText,
  handleOnChange,
  label = '',
  showCheckbox = true,
  disabled = false,
  ...restProps
}: TAutocompleteProps<T>) => {
  const id = useId();
  return (
    <Stack sx={{ width: '100%', height: '100%', ...sxContainer }}>
      {label && <StyledLabel variant="heading-medium-700">{label}</StyledLabel>}
      <StyledAutoComplete
        ref={ref}
        multiple
        limitTags={2}
        handleHomeEndKeys
        value={value}
        // @ts-ignore
        onChange={(_, val) => {
          // @ts-ignore
          if (handleOnChange && val) handleOnChange(val);
        }}
        options={options || []}
        id={id}
        disableCloseOnSelect
        noOptionsText={<Typography variant="body-large-400">No options</Typography>}
        isOptionEqualToValue={(opt, val) => opt?.id === val?.id}
        PopperComponent={StyledPopper}
        getOptionLabel={option => {
          if (typeof option === 'string') return option;
          // @ts-ignore
          return option?.name || option?.displayName || '';
        }}
        renderOption={(props, option, { selected }) => (
          <li {...props}>
            {showCheckbox && <Checkbox size="small" checkboxProps={{ sx: { height: '20px' } }} checked={selected} />}
            <Typography variant="body-xlarge-400" sx={{ pl: 1 }}>
              {option?.name}
            </Typography>
          </li>
        )}
        disabled={disabled}
        renderInput={params => (
          <TextField
            error={error}
            helperText={helperText}
            {...params}
            {...textfieldProps}
            InputProps={{
              ...params.InputProps,
              ...textfieldProps?.InputProps,
              readOnly: disabled,
            }}
          />
        )}
        sx={[
          { width: '100%' },
          {
            [`& .${formControlClasses.root}`]: {
              height: '100%',
              width: '100%',
            },
          },
        ]}
        {...restProps}
      />
    </Stack>
  );
};
