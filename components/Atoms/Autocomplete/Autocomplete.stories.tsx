import { Stack } from '@mui/material';
import { Meta, StoryObj } from '@storybook/react';
import Autocomplete, { TAutocompleteProps } from '.';

export default {
  title: 'Atoms/Autocomplete',
  component: Autocomplete,
  tags: ['autodocs'],
} satisfies Meta<TAutocompleteProps>;

type Story = StoryObj<typeof Autocomplete>;

export const Default: Story = {
  args: {
    label: 'Employee',
    value: [],
    helperText: 'Invalid',
    options: [
      {
        id: '<PERSON>',
        name: '<PERSON>',
      },
      {
        id: '<PERSON>',
        name: '<PERSON>',
      },
      {
        id: '<PERSON>',
        name: '<PERSON>',
      },
    ],
  },
  render: args => (
    <Stack
      sx={{
        width: '300px',
        height: '300px',
        padding: '20px',
        justifyContent: 'center',
        backgroundColor: '#F2EADC',
      }}
    >
      <Autocomplete {...args} />
    </Stack>
  ),
};
