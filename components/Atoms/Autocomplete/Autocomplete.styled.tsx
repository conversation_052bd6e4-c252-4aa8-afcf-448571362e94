import styled from '@emotion/styled';
import { typographyClasses } from '@mui/material/Typography';
import ListItemText from '@mui/material/ListItemText';
import { outlinedInputClasses } from '@mui/material/OutlinedInput';
import FormHelperText, { formHelperTextClasses } from '@mui/material/FormHelperText';
import Autocomplete, { autocompleteClasses } from '@mui/material/Autocomplete';
import { chipClasses } from '@mui/material/Chip';
import Popper from '@mui/material/Popper';
import { formControlClasses, textFieldClasses } from '@mui/material';
import Typography from '../Typography';

export const StyledAutoComplete = styled(Autocomplete)(({ theme }) => ({
  [`.${outlinedInputClasses.root}`]: {
    padding: theme.spacing(1.5),
  },

  [`.${formControlClasses.root}`]: {
    width: '100%',
  },

  [`.${autocompleteClasses.inputRoot}`]: {
    gap: theme.spacing(1.25),
  },

  [`.${outlinedInputClasses.root} .${autocompleteClasses.input}`]: {
    padding: 0,
    ...theme.typography['body-xlarge-400'],
    color: theme.palette.neutrals.N50.main,
    '&::placeholder': {
      opacity: 1,
      color: theme.palette.neutrals.N50.main,
    },
  },

  [`& .${outlinedInputClasses.root}`]: {
    border: 'none',
    borderRadius: theme.spacing(1.25),
    backgroundColor: theme.palette.common.white,
  },

  [`& .${outlinedInputClasses.root}.${outlinedInputClasses.focused} .${outlinedInputClasses.notchedOutline}`]: {
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N50.main,
  },

  [`& .${outlinedInputClasses.root}:hover .${outlinedInputClasses.notchedOutline}`]: {
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N50.main,
  },

  [`& .${textFieldClasses.root} .${outlinedInputClasses.root} .${outlinedInputClasses.notchedOutline}`]: {
    top: theme.spacing(-0.625),
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N40.main,
  },

  [`.${chipClasses.root}`]: {
    height: 'fit-content',
    ...theme.typography['body-xlarge-400'],
  },

  [`.${autocompleteClasses.tag}`]: {
    margin: 0,
  },
})) as typeof Autocomplete;

export const StyledListItemText = styled(ListItemText)(({ theme }) => ({
  [`& .${typographyClasses.root}`]: {
    textTransform: 'capitalize',
    color: theme.palette.neutrals.N90.main,
    ...theme.typography['body-xlarge-400'],
  },
}));

export const StyledPopper = styled(Popper)(({ theme }) => ({
  [`& .${autocompleteClasses.paper}`]: {
    marginTop: theme.spacing(0.5),
    marginBottom: theme.spacing(0.5),
    borderRadius: theme.spacing(1.25),
  },
  [`& .${autocompleteClasses.listbox}`]: {
    boxSizing: 'border-box',
    '& ul': {
      padding: 0,
      margin: 0,
    },
    '& li': {
      color: theme.palette.neutrals.N50.main,
      ...theme.typography['body-xlarge-400'],
    },
  },
}));

export const StyledFormHelperText = styled(FormHelperText)(({ theme }) => ({
  [`&.${formHelperTextClasses.root}`]: {
    padding: 0,
    margin: 0,
    color: theme.palette.neutrals.N110.main,
    ...theme.typography['body-small-400'],
  },
}));

export const StyledLabel = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  color: theme.palette.neutrals.N50.main,
}));
