import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';
import React from 'react';
import { DropdownIcon, LeftArrowIcon, RightArrowIcon } from '../IconsComponent';
import { StyledDateCalendar } from './Calendar.styled';

export interface ICalendarProps {
  value: Date;
  onChange: (value: Date) => void;
}

export const Calendar: React.FC<ICalendarProps> = ({ value, onChange }) => (
  <LocalizationProvider dateAdapter={AdapterDayjs}>
    <StyledDateCalendar
      value={dayjs(value)}
      onChange={newValue => {
        onChange((newValue as Dayjs).toDate());
      }}
      slots={{
        leftArrowIcon: LeftArrowIcon,
        rightArrowIcon: RightArrowIcon,
        switchViewButton: DropdownIcon,
      }}
      views={['year', 'month', 'day']}
    />
  </LocalizationProvider>
);
