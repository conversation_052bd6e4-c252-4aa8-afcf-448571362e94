import styled from '@emotion/styled';
import { pickersDayClasses, pickersMonthClasses, pickersYearClasses } from '@mui/x-date-pickers';
import {
  DateCalendar,
  dateCalendarClasses,
  dayPickerClasses,
  pickersCalendarHeaderClasses,
  pickersSlideTransitionClasses,
} from '@mui/x-date-pickers/DateCalendar';
import { pickersArrowSwitcherClasses } from '@mui/x-date-pickers/internals';

export const StyledDateCalendar = styled(DateCalendar)(({ theme }) => ({
  [`&.${dateCalendarClasses.root}`]: {
    backgroundColor: theme.palette.common.white,
    borderRadius: theme.spacing(0.5625),
    width: theme.spacing(40.625),
    height: theme.spacing(40.375),
    maxHeight: theme.spacing(40.375),
    padding: theme.spacing(2.125, 0),
  },
  [`& .${pickersSlideTransitionClasses.root}`]: {
    minHeight: theme.spacing(25.5),
  },
  [`& .${dayPickerClasses.header}`]: {
    marginBottom: theme.spacing(1.375),
  },
  [`& .${dayPickerClasses.weekDayLabel}`]: {
    width: theme.spacing(4.75),
    height: theme.spacing(5.25),
    color: theme.palette.neutrals.N120.main,
    ...theme.typography['body-medium-400'],
  },
  [`& .${pickersDayClasses.root}`]: {
    width: theme.spacing(4.75),
    height: theme.spacing(4.75),
    ...theme.typography['body-large-400'],
    [`&.${pickersDayClasses.selected}`]: {
      color: theme.palette.common.white,
      backgroundColor: theme.palette.neutrals.N140.main,
    },
  },
  [`& .${pickersCalendarHeaderClasses.root}`]: {
    padding: 0,
    margin: 0,
    paddingLeft: theme.spacing(25 / 8),
    paddingRight: theme.spacing(12 / 8),
  },
  [`& .${pickersCalendarHeaderClasses.labelContainer}`]: {
    gap: theme.spacing(0.5),
  },
  [`& .${pickersCalendarHeaderClasses.label}`]: {
    color: theme.palette.neutrals.N130.main,
    ...theme.typography['heading-medium-700'],
  },
  [`& .${pickersYearClasses.yearButton}`]: {
    color: theme.palette.neutrals.N130.main,
    ...theme.typography['body-large-400'],
    [`&.${pickersYearClasses.selected}`]: {
      color: theme.palette.common.white,
      backgroundColor: theme.palette.neutrals.N140.main,
    },
  },
  [`& .${pickersMonthClasses.monthButton}`]: {
    color: theme.palette.neutrals.N130.main,
    ...theme.typography['body-large-400'],
    [`&.${pickersMonthClasses.selected}`]: {
      color: theme.palette.common.white,
      backgroundColor: theme.palette.neutrals.N140.main,
    },
  },
  [`& .${pickersArrowSwitcherClasses.root}`]: {
    [`.${pickersArrowSwitcherClasses.button}`]: {
      display: 'block',
      width: theme.spacing(4.375),
      height: theme.spacing(4.375),
      padding: 0,
      margin: 0,
    },
    [`.${pickersArrowSwitcherClasses.spacer}`]: {
      width: theme.spacing(3.125),
    },
  },
}));
