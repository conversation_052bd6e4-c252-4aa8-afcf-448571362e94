import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import { Calendar } from './Calendar';

const meta: Meta = {
  title: 'Atoms/Calendar',
  component: Calendar,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof Calendar>;

export const Default: Story = {
  args: { value: new Date('2023-05-17'), onChange: value => console.log(value) },
  render: args => (
    <Box width="327px">
      <Calendar {...args} />
    </Box>
  ),
};
