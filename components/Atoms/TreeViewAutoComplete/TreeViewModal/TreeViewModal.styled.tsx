import styled from '@emotion/styled';
import { Stack } from '@mui/material';
import Button from '@/components/Atoms/Button';

export const StyledButtonWrapper = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  spacing: theme.spacing(2),
  justifyContent: 'flex-end',
  gap: theme.spacing(1.5),
  alignItems: 'center',
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  width: 'fit-content',
  maxWidth: theme.spacing(200 / 8),
  padding: theme.spacing(1.5),
}));
