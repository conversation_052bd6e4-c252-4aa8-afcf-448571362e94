import { Stack } from '@mui/material';
import { Meta, StoryObj } from '@storybook/react';
import TreeViewAutoComplete, { TTreeViewAutoCompleteProps } from '.';

export default {
  title: 'Atoms/TreeViewAutoComplete',
  component: TreeViewAutoComplete,
  tags: ['autodocs'],
} satisfies Meta<TTreeViewAutoCompleteProps>;

type Story = StoryObj<typeof TreeViewAutoComplete>;

export const Default: Story = {
  args: {
    label: 'Employee',
    value: [],
    helperText: 'Invalid',
    options: [
      {
        id: '<PERSON>',
        name: '<PERSON>',
      },
      {
        id: '<PERSON>',
        name: '<PERSON>',
      },
      {
        id: '<PERSON>',
        name: '<PERSON>',
      },
    ],
  },
  render: args => (
    <Stack
      sx={{
        width: '300px',
        height: '300px',
        padding: '20px',
        justifyContent: 'center',
        backgroundColor: '#F2EADC',
      }}
    >
      <TreeViewAutoComplete {...args} />
    </Stack>
  ),
};
