import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import FormHelperText, { formHelperTextClasses } from '@mui/material/FormHelperText';
import IconButton from '@mui/material/IconButton';
import Typography from '../Typography';

export const StyledInputWrapper = styled(Stack)<{ $disabled: boolean }>(({ theme, $disabled }) => ({
  width: '100%',
  flexDirection: 'row',
  flexWrap: 'wrap',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  cursor: $disabled ? 'not-allowed' : 'auto',
  backgroundColor: theme.palette.common.white,
  borderRadius: theme.spacing(1.25),
  padding: theme.spacing(1.25),
  borderWidth: theme.spacing(1 / 16),
  borderStyle: 'solid',
  borderColor: theme.palette.neutrals.N180.main,
  '&:hover': {
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N50.main,
  },

  '&.focused': {
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N50.main,
  },

  '& input': {
    backgroundColor: theme.palette.common.white,
    color: theme.palette.neutrals.N40.main,
    boxSizing: 'border-box',
    width: '0',
    minWidth: '30px',
    flexGrow: '1',
    border: '0',
    margin: '0',
    outline: '0',
    cursor: $disabled ? 'not-allowed' : 'auto',
  },
}));

export const StyledFormHelperText = styled(FormHelperText)(({ theme }) => ({
  [`&.${formHelperTextClasses.root}`]: {
    padding: 0,
    margin: 0,
    color: theme.palette.neutrals.N110.main,
    ...theme.typography['body-small-400'],
  },
}));

export const StyledLabel = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  color: theme.palette.neutrals.N50.main,
}));

export const StyledSearchButton = styled(IconButton)(({ theme }) => ({
  width: 'fit-content',
  height: 'fit-content',
  color: theme.palette.neutrals.N50.main,
  cursor: 'pointer',
  padding: 0,
}));
