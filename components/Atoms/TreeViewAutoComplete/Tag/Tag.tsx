import { StyledTag, StyledChipTotal, StyledCloseButton } from './Tag.styled';
import Typography from '@/components/Atoms/Typography';
import { customPalettes } from '@/theme/customPalettes';
import { CloseIcon } from '@/components/Atoms/IconsComponent';

export interface ITagProps {
  label: string;
  onDelete: () => void;
  disabled?: boolean;
}

export const Tag: React.FC<ITagProps> = ({ label, onDelete, disabled = false }) => (
  <StyledTag className="Tag-container">
    <Typography variant="body-xlarge-400" color={customPalettes?.neutrals?.N50.main}>
      {label}
    </Typography>
    <StyledCloseButton
      disableRipple
      disableFocusRipple
      disableTouchRipple
      disabled={disabled}
      onClick={() => {
        if (disabled) return;
        onDelete();
      }}
    >
      <CloseIcon />
    </StyledCloseButton>
  </StyledTag>
);
