import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@/components/Atoms/Typography';

export const StyledTag = styled(Stack)(({ theme }) => ({
  width: 'fit-content',
  justifyContent: 'flex-start',
  alignItems: 'center',
  flexDirection: 'row',
  gap: theme.spacing(1.25),
  color: theme.palette.neutrals.N50.main,
  backgroundColor: theme.palette.neutrals.N100.main,
  borderRadius: theme.spacing(5 / 8),
  padding: theme.spacing(0.75, 0.5),
  outline: '0',
  overflow: 'hidden',
}));

export const StyledChipTotal = styled(Typography)(({ theme }) => ({
  width: 'fit-content',
  height: 'fit-content',
  color: theme.palette.common.white,
  backgroundColor: theme.palette.neutrals.N50.main,
  borderRadius: theme.spacing(1.25),
  padding: theme.spacing(0.25, 1),
  ...theme.typography['body-small-400'],
}));

export const StyledCloseButton = styled(IconButton)(({ theme }) => ({
  width: 'fit-content',
  height: 'fit-content',
  padding: 0,
  color: theme.palette.neutrals.N50.main,
  '&.Mui-disabled': {
    cursor: 'not-allowed',
    pointerEvents: 'auto',
  },
}));
