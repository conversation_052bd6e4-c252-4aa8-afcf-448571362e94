import { <PERSON>a, <PERSON>Obj } from '@storybook/react';
import { useState } from 'react';
import { TreeViewMultiple } from './TreeViewMultiple';
import { ITreeViewOption } from './TreeViewMultiple.types';

export default {
  title: 'Atoms/TreeViewMultiple',
  component: TreeViewMultiple,
  tags: ['autodocs'],
} satisfies Meta<typeof TreeViewMultiple>;

type Story = StoryObj<typeof TreeViewMultiple>;

const data: ITreeViewOption[] = [
  {
    id: '1',
    name: 'Parent 1',
    children: [
      {
        id: '2',
        name: 'Child 1',
        parent: { id: '1', name: 'Parent 1' },
        children: [
          {
            id: '5',
            name: 'Grandchild 1',
            parent: { id: '2', name: 'Child 1' },
            children: [
              {
                id: '9',
                name: 'Great-grandchild 1',
                parent: { id: '5', name: 'Grandchild 1' },
              },
              {
                id: '10',
                name: 'Great-grandchild 2',
                parent: { id: '5', name: 'Grandchild 1' },
              },
            ],
          },
          {
            id: '6',
            name: 'Grandchild 2',
            parent: { id: '2', name: 'Child 1' },
            children: [
              {
                id: '11',
                name: 'Great-grandchild 3',
                parent: { id: '6', name: 'Grandchild 2' },
              },
              {
                id: '12',
                name: 'Great-grandchild 4',
                parent: { id: '6', name: 'Grandchild 2' },
              },
            ],
          },
        ],
      },
      {
        id: '3',
        name: 'Child 2',
        parent: { id: '1', name: 'Parent 1' },
        children: [
          {
            id: '7',
            name: 'Grandchild 3',
            parent: { id: '3', name: 'Parent 1' },
          },
        ],
      },
    ],
  },
  {
    id: '4',
    name: 'Parent 2',
    children: [
      {
        id: '8',
        name: 'Child 3',
        parent: { id: '4', name: 'Parent 2' },
        children: [
          {
            id: '13',
            name: 'Grandchild 4',
            parent: { id: '8', name: 'Child 3' },
            children: [
              {
                id: '14',
                name: 'Great-grandchild 5',
                parent: { id: '13', name: 'Grandchild 4' },
              },
              {
                id: '15',
                name: 'Great-grandchild 6',
                parent: { id: '13', name: 'Grandchild 4' },
              },
            ],
          },
          {
            id: '16',
            name: 'Grandchild 5',
            parent: { id: '8', name: 'Child 3' },
            children: [
              {
                id: '17',
                name: 'Great-grandchild 7',
                parent: { id: '16', name: 'Grandchild 5' },
              },
              {
                id: '18',
                name: 'Great-grandchild 8',
                parent: { id: '16', name: 'Grandchild 5' },
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: '19',
    name: 'Parent',
    children: [
      {
        id: '20',
        name: 'Child 4',
        parent: { id: '19', name: 'Parent' },
        children: [
          {
            id: '21',
            name: 'Grandchild 6',
            parent: { id: '20', name: 'Child 4' },
            children: [
              {
                id: '22',
                name: 'Great-grandchild 9',
                parent: { id: '21', name: 'Grandchild 6' },
              },
              {
                id: '23',
                name: 'Great-grandchild 10',
                parent: { id: '21', name: 'Grandchild 6' },
              },
            ],
          },
          {
            id: '24',
            name: 'Grandchild 7',
            parent: { id: '20', name: 'Child 4' },
          },
        ],
      },
    ],
  },
];
export const Default: Story = {
  args: { options: data, selected: ['1', '2'] },
  render: args => {
    const [selectedList, setSelected] = useState<string[]>([]);

    const onChange = (newValue: string[]) => {
      setSelected(newValue);
    };

    return <TreeViewMultiple {...args} selected={selectedList} onSelectedChange={onChange} />;
  },
};
