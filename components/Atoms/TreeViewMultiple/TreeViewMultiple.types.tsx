import { BoxProps } from '@mui/material/Box';
import { TreeViewProps } from '@mui/x-tree-view/TreeView/TreeView.types';

export interface ITreeViewOption {
  id: string;
  name: string | React.ReactNode;
  parent?: {
    id: string;
    name: string;
  };
  children?: Array<ITreeViewOption & { parent?: { id: string } }>;
  items?: ITreeViewOption[];
}

export enum ETreeViewMultipleVariants {
  SMALL = 'small',
  MEDIUM = 'medium',
}

export type TTreeViewMultiple = {
  options: ITreeViewOption[];
  onSelectedChange?: (nodeId: string[]) => void;
  wrapperProps?: BoxProps;
  variant?: ETreeViewMultipleVariants;
} & Omit<TreeViewProps<true>, 'options'>;
