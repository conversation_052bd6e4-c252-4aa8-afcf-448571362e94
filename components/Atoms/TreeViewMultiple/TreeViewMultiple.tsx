import * as React from 'react';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import { TreeView as MUITreeView } from '@mui/x-tree-view/TreeView';
import { convertToSelectOptions } from '@/lib/utils/array';
import Checkbox from '../Checkbox';
import Typography, { ITypographyProps } from '../Typography';
import { MinusSquare, PlusSquare, StyledChipTotal, StyledTreeItem } from './TreeViewMutiple.styled';
import { ETreeViewMultipleVariants, ITreeViewOption, TTreeViewMultiple } from './TreeViewMultiple.types';
// BFS algorithm to find node by his ID
const bfsSearch = (graph: ITreeViewOption[], targetId: string) => {
  const queue = [...graph];
  while (queue.length > 0) {
    const currNode = queue.shift();
    if (currNode?.id === targetId) {
      return currNode;
    }
    if (currNode?.children) {
      queue.push(...currNode.children);
    }
    if (currNode?.items) {
      queue.push(...currNode.items);
    }
  }
};

export const TreeViewMultiple: React.FC<TTreeViewMultiple> = ({
  options,
  selected: selectedList,
  onSelectedChange,
  wrapperProps,
  variant = ETreeViewMultipleVariants['MEDIUM'],
  ...restProps
}) => {
  const [expanded, setExpanded] = React.useState<string[]>([]);
  const getAllIds = (node: ITreeViewOption, idList: string[] = []) => {
    idList.push(node.id);
    if (node.children) {
      node.children.forEach(child => getAllIds(child, idList));
    }
    if (node.items) {
      node.items.forEach(child => getAllIds(child, idList));
    }
    return idList;
  };

  const getAllChild = (parentId: string) => {
    const search = bfsSearch(options, parentId);
    if (!search) return;
    const result: string[] = [];
    getAllIds(search, result);
    return result;
  };

  // Get all father IDs from specific node
  const getAllFathers = (id: string, list: string[] = []): string[] => {
    const node = bfsSearch(options, id);
    if (!node) return [];
    if (node?.parent) {
      list.push(node?.parent?.id);
      return getAllFathers(node?.parent?.id, list);
    }
    return list;
  };

  function isAllChildrenChecked(node?: ITreeViewOption, list: string[] = []) {
    const allChild = getAllChild(node?.id || '');
    const nodeIdIndex = allChild?.indexOf(node?.id || '');
    // @ts-ignore
    allChild?.splice(nodeIdIndex, 1);

    return allChild?.every(nodeId => (selectedList || []).concat(list).includes(nodeId));
  }

  const onSelectedNote = ({ nodeId }: { checked: boolean; nodeId: string }) => {
    const allChild = getAllChild(nodeId);
    const fathers = getAllFathers(nodeId);
    if (typeof onSelectedChange !== 'function') return;
    if ((selectedList || []).includes(nodeId)) {
      //  de-check note
      const newSelected = selectedList?.filter(id => !allChild?.concat(fathers).includes(id));
      onSelectedChange(newSelected || []);
    } else {
      // Check more
      const toBeChecked = allChild || [];

      fathers.forEach(f => {
        if (isAllChildrenChecked(bfsSearch(options || [], f), toBeChecked)) {
          toBeChecked.push(f);
        }
      });
      onSelectedChange((selectedList || []).concat(toBeChecked));
    }
  };

  const renderTree = (nodes: ITreeViewOption) => {
    const isParent = Array.isArray(nodes?.children) || Array.isArray(nodes?.items);
    const isChecked = selectedList?.includes(nodes.id);
    const nestedChildren = ((nodes?.children?.length || 0) > 0 ? nodes?.children : nodes?.items) || [];
    let typographyVariant: ITypographyProps['variant'] = isParent ? 'heading-medium-700' : 'body-xlarge-400';
    if (variant === ETreeViewMultipleVariants['SMALL']) {
      typographyVariant = isParent ? 'heading-small-700' : 'body-large-400';
    }
    return (
      <StyledTreeItem
        key={nodes.id}
        nodeId={nodes.id}
        label={
          <Stack
            height="100%"
            flexDirection="row"
            flexWrap="nowrap"
            alignItems="center"
            gap="12px"
            onClick={event => event.stopPropagation()}
          >
            <Checkbox
              size="small"
              checked={isChecked}
              color="success"
              onChange={checked => onSelectedNote({ checked, nodeId: nodes.id })}
              checkboxProps={{ sx: { width: '24px', height: '24px' } }}
              label={
                <Typography variant={typographyVariant} textTransform={isParent ? 'uppercase' : 'capitalize'}>
                  {nodes.name}
                </Typography>
              }
            />
            {isParent && <StyledChipTotal>{nodes?.children?.length || nodes?.items?.length}</StyledChipTotal>}
          </Stack>
        }
      >
        {isParent ? nestedChildren?.map(node => renderTree(node)) : null}
      </StyledTreeItem>
    );
  };

  const defaultWrapperProps = {
    sx: {
      width: '100%',
    },
  };

  React.useEffect(
    () => setExpanded(convertToSelectOptions(options || [])?.map(option => option?.id) || []),
    [JSON.stringify(options)]
  );

  return (
    <Box {...defaultWrapperProps} {...wrapperProps}>
      <MUITreeView
        aria-label="customized"
        defaultCollapseIcon={<MinusSquare />}
        defaultExpandIcon={<PlusSquare />}
        sx={{ overflowX: 'hidden' }}
        selected={selectedList}
        multiSelect
        onNodeSelect={(event, nodeId) => {
          const index = expanded.indexOf(nodeId?.[0] || '');
          const copyExpanded = [...expanded];
          if (index === -1) {
            copyExpanded.push(nodeId?.[0] || '');
          } else {
            copyExpanded.splice(index, 1);
          }
          setExpanded(copyExpanded);
        }}
        expanded={expanded}
        {...restProps}
      >
        {options?.map(o => renderTree(o))}
      </MUITreeView>
    </Box>
  );
};
