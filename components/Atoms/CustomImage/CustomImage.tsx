import { StackProps } from '@mui/material';
import { ImageProps } from 'next/image';
import React, { useState } from 'react';
import { StyledImage, StyledImageWrapper } from './CustomImage.styled';

type TCustomImage = {
  containerProps?: StackProps;
  src: string;
  alt: string;
  style?: React.CSSProperties;
} & ImageProps;

const IMAGE_ERROR = '/assets/placeholder.png';
const LOADING_IMAGE = '/assets/loading.jpg';

export const CustomImage: React.FC<TCustomImage> = ({ containerProps, alt = 'img', src, ...restProps }) => {
  const [srcImg, setSrc] = useState(src);
  return (
    <StyledImageWrapper className="CustomImage__container" {...containerProps}>
      <StyledImage
        src={srcImg}
        onError={() => setSrc(IMAGE_ERROR)}
        fill
        loading="lazy"
        alt={alt}
        // placeholder="blur"
        // blurDataURL={LOADING_IMAGE}
        style={{ objectFit: 'cover' }}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        {...restProps}
      />
    </StyledImageWrapper>
  );
};
