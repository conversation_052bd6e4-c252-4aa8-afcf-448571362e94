import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import dayjs from 'dayjs';
import { TimePicker } from './TimePicker';

const meta: Meta = {
  title: 'Atoms/TimePicker',
  component: TimePicker,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof TimePicker>;

export const Default: Story = {
  args: { value: dayjs(), onChange: value => console.log(value) },
  render: args => (
    <Box width="fit-content">
      <TimePicker {...args} />
    </Box>
  ),
};
