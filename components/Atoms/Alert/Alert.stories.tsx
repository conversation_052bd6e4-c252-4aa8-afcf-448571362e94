import { <PERSON>a, StoryObj } from '@storybook/react';
import { <PERSON><PERSON>, TAlert } from './Alert';

export default {
  title: 'Atoms/Alert',
  component: Alert,
  tags: ['autodocs'],
} satisfies Meta<TAlert>;

type Story = StoryObj<typeof Alert>;

export const Default: Story = {
  args: { isOpen: true, content: 'this is alert content' },
  render: args => <Alert {...args} />,
};

export const Success: Story = {
  args: { isOpen: true, content: 'this is alert content', type: 'success' },
  render: args => <Alert {...args} />,
};

export const Info: Story = {
  args: { isOpen: true, type: 'info', content: 'this is alert content' },
  render: args => <Alert {...args} />,
};

export const Warning: Story = {
  args: { isOpen: true, content: 'this is alert content', type: 'warning' },
  render: args => <Alert {...args} />,
};

export const Error: Story = {
  args: { isOpen: true, content: 'this is alert content', type: 'error' },
  render: args => <Alert {...args} />,
};
