import { FC, forwardRef } from 'react';

import MuiAlert, { AlertProps } from '@mui/material/Alert';
import Snackbar, { SnackbarProps } from '@mui/material/Snackbar';

const AlertMui = forwardRef<HTMLDivElement, AlertProps>((props, ref) => (
  <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />
));

AlertMui.displayName = 'Alert';

export type TAlertSeverity = AlertProps['severity'];

export type TAlert = {
  isOpen: boolean;
  content: string;
  type?: TAlertSeverity;
  autoHideDuration?: number;
  AlertProps?: AlertProps;
  SnackBarProps?: SnackbarProps;
  onClose: () => void;
};

export const Alert: FC<TAlert> = ({
  isOpen,
  autoHideDuration = 2000,
  content,
  type = 'success',
  AlertProps,
  SnackBarProps,
  onClose,
}) => (
  <Snackbar
    open={isOpen}
    autoHideDuration={autoHideDuration}
    anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
    onClose={onClose}
    {...SnackBarProps}
  >
    <AlertMui onClose={onClose} severity={type} {...AlertProps}>
      {content}
    </AlertMui>
  </Snackbar>
);
