import styled from '@emotion/styled';
import { IconButton, Stack } from '@mui/material';
import { CSSProperties } from 'react';
import { TMediaVariants } from './MediaUpload';

export const StyledImageContainer = styled(Stack)(({ theme }) => ({
  position: 'relative',
  width: theme.spacing(81.5),
  height: theme.spacing(32.5),
}));

export const StyledCameraIcon = styled(IconButton)<{ variant: TMediaVariants }>(({ theme, variant }) => {
  const commonProperties: Record<TMediaVariants, CSSProperties> = {
    avatar: { bottom: '0', right: '0' },
    contained: { bottom: '18px', right: '18px' },
    category_thumbnail: { bottom: '0', right: '0' },
  };

  return {
    position: 'absolute',
    zIndex: 10,
    bottom: '18px',
    right: '18px',
    backgroundColor: `white !important`,
    border: 'none',
    height: '40px',
    borderRadius: ' 50%',
    ...commonProperties[variant],
  };
});

export const StyledCloseIcon = styled(IconButton)<{ variant: TMediaVariants }>(({ theme, variant }) => {
  const commonProperties: Record<TMediaVariants, CSSProperties> = {
    avatar: { top: '0', right: '0' },
    contained: { top: '18px', right: '18px' },
    category_thumbnail: { top: '0', right: '0', width: theme.spacing(14 / 8), height: theme.spacing(14 / 8) },
  };

  return {
    background: 'red',
    width: theme.spacing(3),
    height: theme.spacing(3),
    position: 'absolute',
    zIndex: 10,
    ':hover': {
      backgroundColor: 'default',
    },
    ...commonProperties[variant],
  };
});
