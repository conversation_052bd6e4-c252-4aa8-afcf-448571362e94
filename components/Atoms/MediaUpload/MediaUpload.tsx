import { CardMedia, IconButton, Stack } from '@mui/material';
import Avatar, { AvatarProps } from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import { useSession } from 'next-auth/react';
import React, { ChangeEvent, useRef, useState } from 'react';
import { uploadAvatar } from '@/lib/services/upload';
import Icon from '../Icon';
import { StyledCameraIcon, StyledCloseIcon } from './MediaUpload.styled';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { ImageCropperModal } from '@/components/Molecules/ImageCropperModal';

type TUploadResponse = {
  id: string;
  url?: string;
};

const avatarVariant = ['avatar', 'contained', 'category_thumbnail'] as const;

export type TMediaVariants = (typeof avatarVariant)[number];

export type TMediaUpload = {
  getFileUploadRes: (data: TUploadResponse) => void;
  defaultValue?: File;
  avatarProps?: AvatarProps;
  value?: Partial<TUploadResponse>;
  children?: React.ReactNode;
  variant: TMediaVariants;
  disabled?: boolean;
};

export const MediaUpload: React.FC<TMediaUpload> = ({
  value,
  defaultValue,
  getFileUploadRes,
  avatarProps,
  children,
  variant,
  disabled = false,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | undefined>(defaultValue);
  const [isLoading, setIsLoading] = useState(false);
  const { data } = useSession();
  const token = data?.user?.accessToken || '';
  const { showError } = useAlert();
  const uploadSelectedFile = async (file?: File) => {
    if (file) {
      try {
        const formData = new FormData();
        formData.set('file', file, file?.name);
        setIsLoading(true);
        const response = await uploadAvatar<TUploadResponse>(formData, token);

        setIsLoading(false);
        if (!response.ok) {
          showError({ title: 'Image size must not exceed 10 MB' });
          return;
        }

        const data = await response.json();
        getFileUploadRes(data);
        setSelectedFile(undefined);
        if (fileInputRef?.current?.value) {
          fileInputRef.current.value = '';
        }
      } catch (e) {
        showError({ title: 'Image size must not exceed 10 MB' });
      }
    }
  };

  const onUploadFile = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleClick = (): void => {
    if (disabled) return;
    fileInputRef.current?.click();
  };

  const onRemoveClick = () => {
    getFileUploadRes({ id: '', url: undefined });

    if (fileInputRef?.current?.value) {
      fileInputRef.current.value = '';
    }
  };

  const isLoadingUpload = isLoading ? '/assets/loading.jpg' : value?.url;
  const visibleCameraIcon = ((!value?.url || disabled) && variant === 'contained') || variant === 'category_thumbnail';
  const isDisplayCloseButton = !!value?.url && !disabled;

  return (
    <>
      <Box
        sx={{
          position: 'relative',
          display: 'inline-block',
          margin: '0 auto',
          borderRadius: '20px',
          background: variant === 'contained' ? 'white' : 'default',
          ...(variant === 'contained' ? { width: '100%' } : {}),
        }}
      >
        {isDisplayCloseButton && (
          <StyledCloseIcon variant={variant} onClick={onRemoveClick}>
            <Icon variant="close_white" size={1} />
          </StyledCloseIcon>
        )}
        <input ref={fileInputRef} type="file" accept="image/*" style={{ display: 'none' }} onChange={onUploadFile} />

        {variant === 'avatar' && (
          <Avatar
            {...avatarProps}
            alt="Uploaded"
            sx={() => ({
              height: '100px',
              width: '100px',
              color: 'black',
              textTransform: 'uppercase',
              border: '2px solid #AFB1B6',
            })}
            src={selectedFile ? URL.createObjectURL(selectedFile) : isLoadingUpload}
          >
            {children}
          </Avatar>
        )}
        {variant === 'category_thumbnail' &&
          (!value?.url ? (
            <IconButton
              disableTouchRipple
              disableRipple
              disableFocusRipple
              sx={theme => ({
                height: theme.spacing(42 / 8),
                width: theme.spacing(42 / 8),
                backgroundColor: theme.palette.neutrals.N50.main,
              })}
              onClick={handleClick}
            >
              <Icon variant="white_camera" size={3} />
            </IconButton>
          ) : (
            <Avatar
              {...avatarProps}
              alt="Uploaded"
              sx={theme => ({
                height: theme.spacing(42 / 8),
                width: theme.spacing(42 / 8),
                backgroundColor: theme.palette.neutrals.N50.main,
              })}
              src={selectedFile ? URL.createObjectURL(selectedFile) : isLoadingUpload}
            >
              {children}
            </Avatar>
          ))}
        {variant === 'contained' && (
          <Stack height="260px">
            {value?.url ? (
              <CardMedia
                sx={{ borderRadius: '20px', objectFit: 'contain' }}
                component="img"
                height="100%"
                image={isLoadingUpload}
                alt={value?.id || ''}
              />
            ) : (
              <Stack justifyContent="center" alignItems="center" height="100%">
                <IconButton
                  disabled={disabled}
                  sx={{
                    width: '48px',
                    height: '48px',
                    cursor: disabled ? 'not-allowed !important' : 'auto',
                    pointerEvents: 'auto !important',
                  }}
                  onClick={handleClick}
                >
                  <Icon variant="camera" size={6} />
                </IconButton>
              </Stack>
            )}
          </Stack>
        )}

        {!visibleCameraIcon && (
          <StyledCameraIcon variant={variant} onClick={handleClick} color="primary" aria-label="upload image">
            <Icon variant="camera" size={3} />
          </StyledCameraIcon>
        )}
      </Box>
      <ImageCropperModal
        isOpen={!!selectedFile}
        onClose={() => {
          setSelectedFile(undefined);
          if (fileInputRef?.current?.value) {
            fileInputRef.current.value = '';
          }
        }}
        image={selectedFile}
        onFileUpLoad={uploadSelectedFile}
        fileName={selectedFile?.name || 'thumbnail'}
        isLoading={isLoading}
      />
    </>
  );
};
