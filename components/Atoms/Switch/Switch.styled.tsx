import styled from '@emotion/styled';
import Box from '@mui/material/Box';

export const StyledSwitchContainer = styled(Box)(({ theme }) => ({
  width: theme.spacing(15),
  position: 'relative',
  display: 'flex',
  padding: 0,
  background: theme.palette.neutrals.N150.main,
  borderRadius: theme.spacing(5),
  userSelect: 'none',
  input: {
    visibility: 'hidden',
    position: 'absolute',
    top: 0,
  },
  label: {
    width: '50%',
    padding: theme.spacing(0.5, 1),
    margin: 0,
    textAlign: 'center',
    color: theme.palette.neutrals.N40.main,
    wordWrap: 'break-word',
    ...theme.typography['heading-xxsmall-700'],
  },
  '.switch-thumb-wrapper': {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: '50%',
    padding: 0,
    zIndex: 3,
    transition: 'transform 0.5s cubic-bezier(.77, 0, .175, 1)',
    pointerEvents: 'none',
  },
  '.switch-thumb-labels': {
    borderRadius: theme.spacing(5),
    color: theme.palette.common.white,
    height: '100%',
    p: {
      display: 'block',
      width: '100%',
      textAlign: 'center',
      opacity: 0,
      padding: 0,
      transition: 'opacity 0.2s cubic-bezier(.77, 0, .175, 1) 0.2s',
      willChange: 'opacity',
      position: 'absolute',
      top: '50%',
      left: 0,
      transform: 'translateY(-50%)',
      color: theme.palette.common.white,
      wordWrap: 'break-word',
      ...theme.typography['heading-xxsmall-700'],
    },
  },

  'input:nth-of-type(1):checked ~ .switch-thumb-wrapper': {
    transform: 'translateX(0%)',
  },

  'input:nth-of-type(2):checked ~ .switch-thumb-wrapper': {
    transform: 'translateX(100%)',
  },

  'input:nth-of-type(1):checked ~ .switch-thumb-wrapper .switch-thumb-labels p:nth-of-type(1)': {
    opacity: 1,
  },

  'input:nth-of-type(2):checked ~ .switch-thumb-wrapper .switch-thumb-labels p:nth-of-type(2)': {
    opacity: 1,
  },

  'input:nth-of-type(1):checked ~ .switch-thumb-wrapper .switch-thumb-labels': {
    background: theme.palette.success.main,
  },

  'input:nth-of-type(2):checked ~ .switch-thumb-wrapper .switch-thumb-labels': {
    background: theme.palette.neutrals.N110.main,
  },
}));
