import Stack from '@mui/material/Stack';
import { Meta, StoryObj } from '@storybook/react';
import { Switch, ISwitchProps } from './Switch';

const meta: Meta = {
  title: 'Atoms/Switch',
  component: Switch,
  tags: ['autodocs'],
} satisfies Meta<ISwitchProps>;

export default meta;

type Story = StoryObj<typeof Switch>;

export const Default: Story = {
  args: {
    checked: false,
    isLoading: false,
    activeLabel: 'Active',
    inactiveLabel: 'Inactive',
    onChange: value => {},
    containerProps: {
      sx: {
        width: '120',
      },
    },
  },
  render: args => (
    <Stack width="327px" gap={10}>
      <Switch {...args} />
    </Stack>
  ),
};
