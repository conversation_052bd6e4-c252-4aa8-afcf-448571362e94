import { Backdrop, CircularProgress } from '@mui/material';
import Box, { BoxProps } from '@mui/material/Box';
import React, { useId } from 'react';
import { customPalettes } from '@/theme/customPalettes';
import Typography from '../Typography';
import { StyledSwitchContainer } from './Switch.styled';

export interface ISwitchProps {
  checked: boolean;
  isLoading?: boolean;
  activeLabel?: string;
  inactiveLabel?: string;
  containerProps?: BoxProps;
  onChange?: (checked: boolean) => void;
}

export const Switch: React.FC<ISwitchProps> = ({
  checked,
  isLoading = false,
  activeLabel = 'Active',
  inactiveLabel = 'Inactive',
  containerProps,
  onChange,
}) => {
  const id = useId();
  const activeInputId = `switchActive-${id}`;
  const inactiveInputId = `switchInactive-${id}`;
  const onCheck = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    e.preventDefault();
    if (!isLoading && checked !== true && typeof onChange === 'function') onChange(true);
  };

  const onUncheck = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    e.preventDefault();
    if (!isLoading && checked !== false && typeof onChange === 'function') onChange(false);
  };

  return (
    <StyledSwitchContainer {...containerProps}>
      <input type="radio" id={activeInputId} name={`switch-${id}`} checked={checked} />
      <input type="radio" id={inactiveInputId} name={`switch-${id}`} checked={!checked} />
      <Typography component="label" onClick={onCheck} sx={{ cursor: isLoading ? 'auto' : 'pointer' }}>
        {activeLabel}
      </Typography>
      <Typography component="label" onClick={onUncheck} sx={{ cursor: isLoading ? 'auto' : 'pointer' }}>
        {inactiveLabel}
      </Typography>
      <Box className="switch-thumb-wrapper">
        <Box className="switch-thumb-labels">
          <Typography>{activeLabel}</Typography>
          <Typography>{inactiveLabel}</Typography>
        </Box>
      </Box>
      <Backdrop
        sx={{
          color: customPalettes.common?.white,
          backgroundColor: 'transparent',
          zIndex: theme => theme.zIndex.drawer + 1,
          position: 'absolute',
          pointerEvents: 'none',
        }}
        open={isLoading}
      >
        <CircularProgress size={12} color="info" thickness={5} />
      </Backdrop>
    </StyledSwitchContainer>
  );
};
