import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import { CustomColorPicker } from './CustomColorPicker';

const meta: Meta = {
  title: 'Atoms/CustomColorPicker',
  component: CustomColorPicker,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof CustomColorPicker>;

export const Default: Story = {
  args: {},
  render: args => (
    <Box>
      <CustomColorPicker value="#FFFFFF" onChange={(color: string) => console.log(1)} />
    </Box>
  ),
};
