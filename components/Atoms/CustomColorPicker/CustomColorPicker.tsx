import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import ColorPicker from '../ColorPicker';
import { StyledContainer } from './CustomColorPicker.styled';

const palette = [
  '#FF9CBB',
  '#E2A5E6',
  '#BBC1E8',
  '#81BBFE',
  '#92E3EE',
  '#6DD5CB',
  '#A5E5BD',
  '#E7F286',
  '#FFEC78',
  '#FFBF69',
  '#FF6D6D',
];

export interface IColorPicker {
  value: string;
  onChange: (color: string) => void;
}

export const CustomColorPicker: React.FC<IColorPicker> = ({ value, onChange }) => (
  <StyledContainer>
    <ColorPicker value={value} onChange={val => onChange(String(val))} />
    {palette.map(color => (
      <Stack
        sx={theme => ({
          minWidth: theme.spacing(4),
          minHeight: theme.spacing(4),
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: '50%',
          overflow: 'hidden',
          cursor: 'pointer',
          borderWidth: theme.spacing(1 / 8),
          borderStyle: 'solid',
          borderColor: color === value ? theme.palette.neutrals.N50.main : 'transparent',
        })}
        onClick={() => onChange(color)}
      >
        <Box
          sx={theme => ({
            width: theme.spacing(3),
            height: theme.spacing(3),
            borderRadius: '50%',
            overflow: 'hidden',
            backgroundColor: color,
          })}
        />
      </Stack>
    ))}
  </StyledContainer>
);
