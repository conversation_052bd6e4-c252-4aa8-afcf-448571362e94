import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';
import Typography from '../Typography';

export const StyledGroupHeaderContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  justifyContent: 'flex-start',
  alignItems: 'center',
  padding: theme.spacing(1, 1.5),
  gap: theme.spacing(1.5),
  backgroundColor: theme.palette.neutrals.N60.main,
}));

export const StyledCounterText = styled(Typography)(({ theme }) => ({
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.spacing(3.875),
  opacity: 0.25,
  color: theme.palette.common.white,
  backgroundColor: theme.palette.neutrals.N50.main,
  textTransform: 'uppercase',
  ...theme.typography['heading-medium-700'],
}));
