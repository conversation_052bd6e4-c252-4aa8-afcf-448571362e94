import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import CheckboxGroup, { TCheckboxOption } from '.';

const meta: Meta = {
  title: 'Atoms/CheckboxGroup',
  component: CheckboxGroup,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof CheckboxGroup>;

const SIMPLE_CHECKBOX_ITEMS: TCheckboxOption[] = [
  {
    label: 'My Profile',
    value: 'my_profile',
    disabled: true,
  },
  {
    label: 'View User List',
    value: 'view_user_list',
  },
  {
    label: 'View User Details',
    value: 'view_user_details',
  },
  {
    label: 'Add User',
    value: 'add_user',
  },
  {
    label: 'Edit User',
    value: 'edit_user',
  },
  {
    label: 'Add Role',
    value: 'add_role',
  },
  {
    label: 'Edit Role',
    value: 'edit_role',
  },
  {
    label: 'Delete Role',
    value: 'delete_role',
  },
  {
    label: 'Publish / Unpublish Role',
    value: 'publish_unpublish_role',
  },
];

export const Default: Story = {
  args: {
    options: SIMPLE_CHECKBOX_ITEMS,
    values: [],
    variant: 'normal',
  },
  render: args => (
    <Box
      width="100%"
      p={2}
      sx={{
        backgroundColor: '#EADFCE',
      }}
    >
      <CheckboxGroup {...args} />
    </Box>
  ),
};

const NESTED_CHECKBOX_ITEMS: TCheckboxOption[] = [
  {
    label: 'Osen entry (Day pass)',
    value: 'onsen_entry',
    childrens: [
      {
        label: 'Adult',
        value: 'adult',
      },
      {
        label: 'Child',
        value: 'child',
      },
      {
        label: 'Senior',
        value: 'senior',
      },
    ],
  },
  {
    label: 'wellness package',
    value: 'wellness_package',
    childrens: [
      {
        label: 'Onsen + Deep Sleep Head Massage 60 Mins',
        value: 'package1',
      },
      {
        label: 'Onsen + Deep Tissue Massage 90 Mins',
        value: 'package2',
      },
      {
        label: 'Onsen + Deep Tissue Massage 90 Mins + Herbal Compress 30 Mins',
        value: 'package3',
      },
    ],
  },
  {
    label: 'Aromatherapy massage package',
    value: 'aromatherapy_massage_package',
    childrens: [
      {
        label: 'Onsen + Deep Sleep Head Massage 60 Mins',
        value: 'package4',
      },
      {
        label: 'Onsen + Deep Tissue Massage 90 Mins',
        value: 'package5',
      },
      {
        label: 'Onsen + Deep Tissue Massage 90 Mins + Herbal Compress 30 Mins',
        value: 'package6',
      },
    ],
  },
];

export const NestedCheckbox: Story = {
  args: {
    options: NESTED_CHECKBOX_ITEMS,
    values: [],
    variant: 'nested_checkboxes',
  },
  render: args => (
    <Box
      width="100%"
      p={2}
      sx={{
        backgroundColor: '#EADFCE',
      }}
    >
      <CheckboxGroup {...args} />
    </Box>
  ),
};
