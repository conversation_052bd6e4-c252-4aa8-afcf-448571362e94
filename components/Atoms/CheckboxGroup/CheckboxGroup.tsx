import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import React from 'react';
import { customTypography } from '@/theme/customTypography';
import { customPalettes } from '@/theme/customPalettes';
import Checkbox from '../Checkbox';
import { StyledCounterText, StyledGroupHeaderContainer } from './CheckboxGroup.styled';

export type TCheckboxOption = {
  label: string;
  value: string;
  disabled?: boolean;
  childrens?: TCheckboxOption[];
};

export type CheckboxGroupVariant = 'normal' | 'select_all_option' | 'nested_checkboxes';

export interface ICheckboxGroupProps {
  options: TCheckboxOption[];
  variant?: CheckboxGroupVariant;
  values: string[];
  onChange: (newValues: string[]) => void;
}

export const CheckboxGroup: React.FC<ICheckboxGroupProps> = ({ options, variant = 'normal', values, onChange }) => {
  if (variant === 'nested_checkboxes') {
    const handleOnChangeParentCheckbox = (childrens: TCheckboxOption[]) => {
      const temp = values.filter(value => childrens.findIndex(children => children.value === value) === -1);
      const isSelectedAll = childrens.every(children => values.includes(children.value));
      if (isSelectedAll) {
        onChange([...temp]);
      } else {
        onChange([...temp, ...childrens.map(children => children.value)]);
      }
    };
    return (
      <Stack gap={3}>
        {options.map(({ label: labelItem, childrens = [] }) => {
          const isSelectedAll = childrens.every(children => values.includes(children.value));
          return (
            <Stack gap={2.25} key={labelItem}>
              <StyledGroupHeaderContainer>
                <Checkbox
                  checked={isSelectedAll}
                  label={labelItem}
                  onChange={() => handleOnChangeParentCheckbox(childrens)}
                  labelProps={{
                    sx: {
                      textTransform: 'uppercase',
                      ...customTypography['heading-large-700'],
                    },
                  }}
                />
                {childrens.length > 0 && (
                  <StyledCounterText>
                    {childrens.length.toLocaleString('en-US', {
                      minimumIntegerDigits: 2,
                      useGrouping: false,
                    })}
                  </StyledCounterText>
                )}
              </StyledGroupHeaderContainer>
              <Grid container spacing={1.5} px={1.5}>
                {childrens?.map(
                  ({ label: childrenLabel, value: childrenValue, disabled: childrenDisabled = false }) => (
                    <Grid item md={6}>
                      <Checkbox
                        checked={values.includes(childrenValue) || values.includes(childrenValue)}
                        label={childrenLabel}
                        disabled={childrenDisabled}
                        onChange={(checked: boolean) => {
                          if (checked) {
                            onChange([...values, childrenValue]);
                          } else {
                            onChange(values.filter(valueItem => valueItem !== childrenValue));
                          }
                        }}
                      />
                    </Grid>
                  )
                )}
              </Grid>
            </Stack>
          );
        })}
      </Stack>
    );
  }
  if (variant === 'select_all_option') {
    const isSelectedAll = options?.every(item => values.includes(item.value));
    return (
      <Stack gap={2.25}>
        <Checkbox
          checked={isSelectedAll}
          label="Select all"
          onChange={() => {
            if (isSelectedAll) {
              onChange([]);
            } else {
              onChange([...options.map(children => children.value)]);
            }
          }}
        />
        <Divider
          sx={{
            backgroundColor: customPalettes.neutrals?.N50.main,
          }}
        />
        <Grid container rowSpacing={2.25} columnSpacing={3}>
          {options?.map(({ label, value, disabled = false }: TCheckboxOption) => (
            <Grid item xs={12} sm={4} md={12 / 5}>
              <Checkbox
                checked={values.includes(value)}
                label={label}
                disabled={disabled}
                onChange={(checked: boolean) => {
                  if (checked) {
                    onChange([...values, value]);
                  } else {
                    onChange(values.filter(valueItem => valueItem !== value));
                  }
                }}
              />
            </Grid>
          ))}
        </Grid>
      </Stack>
    );
  }
  if (variant === 'normal') {
    return (
      <Grid container rowSpacing={2.25} columnSpacing={3}>
        {options?.map(({ label, value, disabled = false }: TCheckboxOption) => (
          <Grid item xs={12} sm={4} md={12 / 5}>
            <Checkbox
              checked={values.includes(value)}
              label={label}
              disabled={disabled}
              onChange={(checked: boolean) => {
                if (checked) {
                  onChange([...values, value]);
                } else {
                  onChange(values.filter(valueItem => valueItem !== value));
                }
              }}
            />
          </Grid>
        ))}
      </Grid>
    );
  }
  return <></>;
};
