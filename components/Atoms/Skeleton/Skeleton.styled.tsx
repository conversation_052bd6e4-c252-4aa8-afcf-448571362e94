import styled from '@emotion/styled';
import Skeleton from '@mui/material/Skeleton';
import { ISkeletonProps } from './Skeleton';

export const StyledSkeleton = styled(Skeleton)<ISkeletonProps>(
  ({ theme, fontSize = 2, width = 6.25, height = 3.75, fillWithContainer = false }) => ({
    fontSize: theme.spacing(fontSize),
    width: fillWithContainer ? '100%' : theme.spacing(width),
    height: fillWithContainer ? '100%' : theme.spacing(height),
  })
);
