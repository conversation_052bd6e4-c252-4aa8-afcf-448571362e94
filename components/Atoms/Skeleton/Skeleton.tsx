import { SkeletonProps } from '@mui/material/Skeleton';
import { StyledSkeleton } from './Skeleton.styled';

export interface ISkeletonProps extends SkeletonProps {
  fontSize?: number;
  width?: number;
  height?: number;
  fillWithContainer?: boolean;
}

export const Skeleton: React.FC<ISkeletonProps> = ({ fillWithContainer = true, ...rest }) => (
  <StyledSkeleton fillWithContainer={fillWithContainer} {...rest} />
);
