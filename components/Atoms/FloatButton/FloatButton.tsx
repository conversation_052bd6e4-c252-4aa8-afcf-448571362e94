import { BackdropProps, SpeedDialProps, Stack } from '@mui/material';
import Link from 'next/link';
import React, { useState } from 'react';
import Icon, { IconVariantTypes } from '../Icon';
import {
  StyledActionName,
  StyledBackdrop,
  StyledFloatButtonWrapper,
  StyledSpeedDial,
  StyledSpeedDialAction,
} from './FloatButton.styled';

export interface IActionItem {
  name: string;
  icon: IconVariantTypes;
  href: string;
  // onClick: () => void;
}

export interface IFloatButtonProps {
  actionItems: IActionItem[];
  showBackdrop?: boolean;
  backdropProps?: BackdropProps;
  floatButtonProps?: SpeedDialProps;
}

export const FloatButton: React.FC<IFloatButtonProps> = ({
  actionItems,
  showBackdrop = true,
  backdropProps,
  floatButtonProps,
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  return (
    <StyledFloatButtonWrapper>
      {showBackdrop && <StyledBackdrop open={open} {...backdropProps} />}
      <StyledSpeedDial
        open={open}
        ariaLabel="float-button"
        icon={<Icon variant="plus" />}
        onOpen={handleOpen}
        onClose={handleClose}
        {...floatButtonProps}
      >
        {actionItems.map(({ name, icon, href }) => (
          <StyledSpeedDialAction
            key={name}
            icon={
              <Link href={href} style={{ textDecoration: 'none' }}>
                <Stack direction="row" gap={1.25} justifyContent="flex-start" alignItems="center">
                  <Icon variant={icon} size={3} />
                  <StyledActionName variant="body-extra-large-400">{name}</StyledActionName>
                </Stack>
              </Link>
            }
            onClick={() => {
              handleClose();
            }}
          />
        ))}
      </StyledSpeedDial>
    </StyledFloatButtonWrapper>
  );
};
