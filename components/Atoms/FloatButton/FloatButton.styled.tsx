import styled from '@emotion/styled';
import { backdropClasses, speedDialActionClasses, speedDialClasses } from '@mui/material';

import Backdrop from '@mui/material/Backdrop';
import Box from '@mui/material/Box';
import SpeedDial from '@mui/material/SpeedDial';
import SpeedDialAction from '@mui/material/SpeedDialAction';

import Typography from '../Typography';

export const StyledFloatButtonWrapper = styled(Box)(() => ({
  width: '100%',
  height: '100%',
}));

export const StyledSpeedDial = styled(SpeedDial)(({ theme }) => ({
  [`&.${speedDialClasses.root}`]: {
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    position: 'absolute',
    bottom: theme.spacing(2.875),
    right: theme.spacing(3),
  },

  [`& .${speedDialClasses.fab}`]: {
    width: theme.spacing(8),
    height: theme.spacing(8),
    backgroundColor: theme.palette.neutrals.N50.main,
    '&: hover': {
      backgroundColor: theme.palette.neutrals.N50.main,
    },
  },
  [`& .${speedDialClasses.actions}`]: {
    padding: 0,
    margin: 0,
    marginBottom: theme.spacing(-8),
    paddingBottom: theme.spacing(11),
    gap: theme.spacing(1.5),
    flexDirection: 'column',
  },
}));
export const StyledSpeedDialAction = styled(SpeedDialAction)(({ theme }) => ({
  [`&.${speedDialActionClasses.fab}`]: {
    display: 'block',
    width: '100%',
    height: 'fit-content',
    margin: 0,
    padding: theme.spacing(1.5, 3),
    borderRadius: theme.spacing(6),
    backgroundColor: theme.palette.common.white,
    boxShadow: '4px 7px 20px -8px rgba(60, 35, 19, 0.25), 1px 1px 3px 0px rgba(60, 35, 19, 0.20)',
    '&: hover': {
      backgroundColor: theme.palette.common.white,
      opacity: 0.5,
    },
  },
}));

export const StyledActionName = styled(Typography)(({ theme }) => ({
  textTransform: 'capitalize',
  color: theme.palette.neutrals.N90.main,
}));

export const StyledBackdrop = styled(Backdrop)(({ theme }) => ({
  [`&.${backdropClasses.root}`]: {
    zIndex: 100,
    opacity: '0.6 !important',
    backgroundColor: theme.palette.neutrals.N50.main,
  },
}));
