import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import { FLOAT_BUTTON_ACTION_ITEMS } from '@/lib/constants/backdrop';
import Typography from '../Typography';
import { FloatButton } from './FloatButton';

const meta: Meta = {
  title: 'Atoms/FloatButton',
  component: FloatButton,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof FloatButton>;

export const Default: Story = {
  args: {
    actionItems: FLOAT_BUTTON_ACTION_ITEMS,
  },
  render: args => (
    <Box width="100vw" height="100vh">
      <Typography>Text</Typography>
      <FloatButton {...args} />
    </Box>
  ),
};
