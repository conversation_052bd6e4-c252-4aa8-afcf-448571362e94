import { Meta, StoryObj } from '@storybook/react';
import Box from '@mui/material/Box';
import { Typography } from './Typography';

const meta: Meta = {
  title: 'Atoms/Typography',
  component: Typography,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof Typography>;

export const Default: Story = {
  args: { variant: 'body1' },
  render: args => (
    <Box width="327px">
      <Typography {...args}>Label</Typography>
    </Box>
  ),
};
