import { Stack, TableRowProps } from '@mui/material';
import React, { forwardRef } from 'react';
import { StyledTableRow } from './CustomTableRow.styled';

export interface ICustomtableRow extends TableRowProps {
  onClick?: (row: any) => void;
}

export const CustomTableRow: React.FC<ICustomtableRow> = forwardRef(({ onClick, children, ...otherProps }, ref) => (
  <StyledTableRow onClick={onClick} ref={ref} {...otherProps}>
    {children}
  </StyledTableRow>
));
