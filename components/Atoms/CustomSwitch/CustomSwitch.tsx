import Typography from '@/components/Atoms/Typography';
import { StyledContainer, StyledItem } from './CustomSwitch.styled';

export type TSwitchOptions = { value: string; label: string };

export type TCustomSwitchProps = {
  label?: string;
  onChange: (val: string) => void;
  activeValue: string;
  options?: TSwitchOptions[];
};

const choices = ['no_member', 'member'] as const;

export type TPriceType = (typeof choices)[number];

const OPTIONS: TSwitchOptions[] = [
  { value: 'no_member', label: 'Non member' },
  { value: 'member', label: 'Member' },
];

export const CustomSwitch: React.FC<TCustomSwitchProps> = ({ label, onChange, activeValue, options = OPTIONS }) => (
  <StyledContainer className="CustomSwitch-container">
    {label && (
      <Typography className="CustomSwitch-label" variant="heading-xmedium-700">
        {label}
      </Typography>
    )}
    {options.map(i => (
      <StyledItem
        className="CustomSwitch-item"
        $isactive={activeValue === i.value}
        key={i.value}
        onClick={() => onChange(i.value)}
      >
        {i.label}
      </StyledItem>
    ))}
  </StyledContainer>
);
