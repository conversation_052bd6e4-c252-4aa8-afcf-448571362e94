import { Meta, StoryObj } from '@storybook/react';
import { CustomSwitch, TCustomSwitchProps } from './CustomSwitch';

export default {
  title: 'Atoms/CustomSwitch',
  component: CustomSwitch,
  tags: ['autodocs'],
} satisfies Meta<TCustomSwitchProps>;

type Story = StoryObj<typeof CustomSwitch>;

export const Default: Story = {
  args: { label: 'Price for' },
  render: args => <CustomSwitch {...args} />,
};
