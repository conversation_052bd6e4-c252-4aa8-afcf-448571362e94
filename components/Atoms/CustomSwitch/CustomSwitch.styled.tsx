import styled from '@emotion/styled';
import { Stack } from '@mui/material';
import Typography from '@/components/Atoms/Typography';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  flexDirection: 'row',
  justifyContent: 'flex-start',
  alignItems: 'center',
  borderRadius: theme.spacing(4),
  padding: theme.spacing(0.625, 1.5),
  background: 'white',
  gap: theme.spacing(1.5),
  height: theme.spacing(4),
}));

export const StyledItem = styled(Typography)<{ $isactive: boolean }>(({ theme, $isactive }) => ({
  borderRadius: theme.spacing(3.125),
  padding: theme.spacing(0.25, 1),
  height: theme.spacing(2.75),
  lineHeight: '100%',
  background: $isactive ? theme.palette.success.main : theme.palette.neutrals.N210.main,
  color: $isactive ? 'white' : theme.palette.neutrals.N190.main,

  '&:hover': {
    cursor: 'pointer',
  },
}));
