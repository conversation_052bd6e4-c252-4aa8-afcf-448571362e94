import FormControl, { FormControlProps } from '@mui/material/FormControl';
import { SelectChangeEvent, SelectProps } from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import { TextFieldProps } from '@mui/material/TextField';
import { ChangeEvent, useId } from 'react';
import { AutocompleteProps } from '@mui/material';
import Icon from '../Icon';
import { BaseAutocomplete } from './BaseAutocomplete';
import BaseSelect from './BaseSelect';
import { StyledFormHelperText, StyledLabel, StyledPhoneInput, StyledPhoneInputWrapper } from './PhoneInput.styled';
import { TCountry } from '@/lib/constants/countries';

export type TPhoneInputProps = {
  error?: boolean;
  helperText?: string;
  regionProps?: SelectProps;
  autoCompleteRegionProps?: AutocompleteProps<TCountry, false, false, false>;
  phoneInputProps?: TextFieldProps;
  label?: string;
  formControlProps?: FormControlProps;
  value?: {
    dialCode: string;
    phoneNumber: string;
  };
  onChange?: (value: { dialCode: string; phoneNumber: string }) => void;
  required?: boolean;
  disabled?: boolean;
  isShowDialCode?: boolean;
  phoneVariant?: 'base-select' | 'base-autocomplete';
};

export const PhoneInput: React.FC<TPhoneInputProps> = ({
  label,
  regionProps,
  phoneInputProps,
  value,
  helperText,
  formControlProps,
  onChange,
  required,
  isShowDialCode = false,
  disabled = false,
  phoneVariant = 'base-select',
}) => {
  const reactId = useId();
  const id = `region-select-${reactId}`;
  const labelId = `region-select-label-${reactId}`;
  const handleOnChangeDialCode = (event: SelectChangeEvent<unknown>, child: React.ReactNode) => {
    if (typeof onChange === 'function') {
      onChange({ dialCode: event.target.value as string, phoneNumber: value?.phoneNumber || '' });
    }
  };

  const handleOnChangeDialCodeAutoComplete = (dialCode: string) => {
    if (typeof onChange === 'function') {
      onChange({ dialCode, phoneNumber: value?.phoneNumber || '' });
    }
  };
  const handleOnChangePhoneNumber = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (typeof onChange === 'function') {
      onChange({ dialCode: value?.dialCode || '', phoneNumber: e.target.value.replace(/[^0-9]/g, '') });
    }
  };

  return (
    <FormControl {...formControlProps} variant="filled" error={!!helperText}>
      <StyledLabel $isRequired={!!required} variant="heading-medium-700">
        {label}
      </StyledLabel>
      <StyledPhoneInputWrapper>
        {phoneVariant !== 'base-select' ? (
          <BaseSelect
            id={id}
            labelId={labelId}
            value={value?.dialCode || '+65'}
            disabled={disabled}
            onChange={handleOnChangeDialCode}
            helperText={helperText}
            isShowDialCode={isShowDialCode}
            regionProps={regionProps}
          />
        ) : (
          <BaseAutocomplete
            value={value?.dialCode || '+65'}
            onChange={handleOnChangeDialCodeAutoComplete}
            disabled={disabled}
            helperText={helperText}
            sxContainer={regionProps?.sx}
          />
        )}

        <StyledPhoneInput
          value={value?.phoneNumber || ''}
          onChange={handleOnChangePhoneNumber}
          type="tel"
          error={!!helperText}
          {...phoneInputProps}
          disabled={disabled}
        />
      </StyledPhoneInputWrapper>
      <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
        {helperText && (
          <>
            <Icon variant="warning" size={1.5} />
            <StyledFormHelperText>{helperText}</StyledFormHelperText>
          </>
        )}
      </Stack>
    </FormControl>
  );
};
