import { menuClasses, menuItemClasses, OutlinedInput, SelectChangeEvent, SelectProps, Theme } from '@mui/material';
import React from 'react';
import { COUNTRIES } from '@/lib/constants/countries';
import { ChevronDownIcon } from '../IconsComponent';
import { StyledMenuItem, StyledRegionsSelect } from './PhoneInput.styled';

type BaseSelectProps = {
  id: string;
  labelId: string;
  helperText?: string;
  regionProps?: SelectProps;
  disabled?: boolean;
  isShowDialCode?: boolean;
  value?: string;
  onChange: (event: SelectChangeEvent<unknown>, child: React.ReactNode) => void;
};

const BaseSelect: React.FC<BaseSelectProps> = ({
  id,
  labelId,
  helperText,
  regionProps,
  disabled = false,
  isShowDialCode = false,
  onChange,
  value,
}) => {
  const defaultMenuProps = {
    sx: (theme: Theme) => ({
      marginTop: theme.spacing(0.5),
      [`& .${menuClasses.list}`]: {
        padding: 0,
      },
      [`& .${menuItemClasses.root}`]: {
        justifyContent: 'flex-start',
        padding: theme.spacing(1, 3, 1, 1),
        gap: theme.spacing(1.25),
        ...theme.typography['body-xlarge-400'],
        '&:hover': {
          backgroundColor: theme.palette.neutrals.N150.main,
        },
        [`&.${menuItemClasses.selected}`]: {
          backgroundColor: theme.palette.neutrals.N150.main,
        },
      },
    }),
  };
  return (
    <StyledRegionsSelect
      id={id}
      labelId={labelId}
      input={<OutlinedInput />}
      value={value}
      renderValue={(selected: any) => {
        if (typeof selected === 'string') return selected;
        return selected.join(', ');
      }}
      MenuProps={{
        ...defaultMenuProps,
      }}
      IconComponent={ChevronDownIcon}
      error={!!helperText}
      onChange={onChange}
      {...regionProps}
      disabled={disabled}
    >
      {COUNTRIES.map(option => (
        <StyledMenuItem key={option.code} value={option.dial_code}>
          {`${option.name} ${isShowDialCode ? `(${option.dial_code})` : ''}`}
        </StyledMenuItem>
      ))}
    </StyledRegionsSelect>
  );
};

export default BaseSelect;
