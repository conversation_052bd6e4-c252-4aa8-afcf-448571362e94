import { Stack } from '@mui/material';
import { Meta, StoryObj } from '@storybook/react';
import PhoneInput, { TPhoneInputProps } from '.';

export default {
  title: 'Atoms/PhoneInput',
  component: PhoneInput,
  tags: ['autodocs'],
} satisfies Meta<TPhoneInputProps>;

type Story = StoryObj<typeof PhoneInput>;

export const Default: Story = {
  args: {
    label: 'Phone',
    value: { dialCode: '+84', phoneNumber: '0972263932' },
    phoneInputProps: {
      sx: {
        minWidth: '230px',
      },
    },
    regionProps: {
      sx: {
        minWidth: 'fit-content',
      },
    },
    onChange: ({ dialCode, phoneNumber }: { dialCode: string; phoneNumber: string }) => {
      console.log(dialCode, phoneNumber);
    },
  },
  render: args => (
    <Stack
      sx={{
        width: '400px',
        height: '300px',
        padding: '20px',
        justifyContent: 'center',
        backgroundColor: '#F2EADC',
      }}
    >
      <PhoneInput {...args} />
    </Stack>
  ),
};
