import styled from '@emotion/styled';
import FormHelperText, { formHelperTextClasses } from '@mui/material/FormHelperText';
import Stack from '@mui/material/Stack';
import MenuItem, { menuItemClasses } from '@mui/material/MenuItem';
import Typography from '../Typography';
import { StyledTextField } from '../TextField/TextField.styled';
import { StyledSelect } from '../Select/Select.styled';

export const StyledPhoneInputWrapper = styled(Stack)(({ theme }) => ({
  width: '100%',
  justifyContent: 'flex-start',
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.spacing(1.5),
}));

export const StyledLabel = styled(Typography)<{ $isRequired: boolean }>(({ theme, $isRequired }) => ({
  marginBottom: theme.spacing(1),
  color: theme.palette.neutrals.N50.main,
  ...($isRequired
    ? {
        ':before': {
          content: `"*"`,
          color: 'red',
        },
      }
    : {}),
}));

export const StyledFormHelperText = styled(FormHelperText)(({ theme }) => ({
  [`&.${formHelperTextClasses.root}`]: {
    padding: 0,
    margin: 0,
    color: theme.palette.neutrals.N110.main,
    ...theme.typography['body-small-400'],
  },
}));

export const StyledRegionsSelect = styled(StyledSelect)(({ theme }) => ({}));

export const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  [`&.${menuItemClasses.root}`]: {
    display: 'flex',
    width: '100%',
  },
}));

export const StyledPhoneInput = styled(StyledTextField)(({ theme }) => ({
  flex: 1,
  width: '100%',
  minWidth: '65%',
}));
