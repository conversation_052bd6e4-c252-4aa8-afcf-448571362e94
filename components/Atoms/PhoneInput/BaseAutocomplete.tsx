/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react/jsx-no-duplicate-props */

import { createFilterOptions, SxProps, Theme } from '@mui/material';
import Box from '@mui/material/Box';
import * as React from 'react';
import { COUNTRIES, TCountry } from '@/lib/constants/countries';
import { StyledTextField } from '../TextField/TextField.styled';
import Autocomplete from '../Autocomplete';

type BaseAutocompleteProps = {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  isShowDialCode?: boolean;
  helperText?: string;
  name?: string;
  sxContainer?: SxProps<Theme>;
};

const filter = createFilterOptions<TCountry>({
  matchFrom: 'any',
  stringify: option => option.id,
  ignoreAccents: true,
  ignoreCase: true,
  trim: true,
});

export const BaseAutocomplete = ({
  value,
  onChange,
  disabled,
  helperText,
  sxContainer,
  name = 'phonecode-select',
}: BaseAutocompleteProps) => {
  const autoCompleteValue = React.useMemo(() => COUNTRIES.find(item => item.dial_code === value), [value]);

  return (
    <Autocomplete<TCountry>
      freeSolo={false}
      value={autoCompleteValue}
      componentsProps={{ popper: { placement: 'bottom-start' } }}
      id="phonecode-select"
      name={name}
      options={COUNTRIES}
      disabled={disabled}
      disableCloseOnSelect={false}
      getOptionLabel={option => {
        if (typeof option === 'string') {
          return option;
        }
        return option?.id;
      }}
      renderOption={(props, option) => (
        <Box key={option.id} component="li" {...props}>
          {option.id}
        </Box>
      )}
      filterOptions={filter}
      error={!!helperText}
      multiple={false}
      renderInput={params => <StyledTextField autoComplete="new-password" {...params} />}
      handleOnChange={value => {
        const selectedValue = (value as TCountry)?.dial_code;
        if (selectedValue) {
          onChange(selectedValue);
        }
      }}
      sxContainer={sxContainer}
    />
  );
};
