'use client';

import FormControl from '@mui/material/FormControl';
import Stack from '@mui/material/Stack';
import dayjs, { Dayjs } from 'dayjs';
import { isEmpty } from 'lodash';
import React, { useId } from 'react';
import { ECustomerPrepaidDetailRangeDate, ERangeDate } from '@/lib/types/enum/report';
import { FORMAT_TIME_FULL } from '@/lib/constants/dateTime';
import { CustomDatePicker } from '../CustomDatePicker';
import Select from '../Select';
import Typography from '../Typography';
import { StyledLabel } from './RangeDateField.styled';
import { TRangeDateFieldProps } from './RangeDateField.types';

export const RangeDateField: React.FC<TRangeDateFieldProps> = ({
  formControlProps,
  rangeTypeFieldName,
  startDateFieldName,
  endDateFieldName,
  label,
  value,
  rangeDateTypeOptions = [],
  onChange,
}) => {
  const id = `range-type-select-${useId()}`;
  const labelId = `range-type-label-${useId()}`;
  const transformStartTimeValue = isEmpty(value?.[startDateFieldName]) ? null : dayjs(value?.[startDateFieldName]);
  const transformEndTimeValue = isEmpty(value?.[endDateFieldName]) ? null : dayjs(value?.[endDateFieldName]);
  const handleOnSelectChange = (value: string) => {
    if (!onChange) return;
    onChange({ field: rangeTypeFieldName, value });
    switch (value) {
      case ERangeDate.TODAY:
        onChange({ field: startDateFieldName, value: dayjs().startOf('day').format(FORMAT_TIME_FULL) });
        onChange({ field: endDateFieldName, value: dayjs().endOf('day').format(FORMAT_TIME_FULL) });
        break;
      case ERangeDate.YESTERDAY:
        onChange({
          field: startDateFieldName,
          value: dayjs().subtract(1, 'day').startOf('day').format(FORMAT_TIME_FULL),
        });
        onChange({ field: endDateFieldName, value: dayjs().subtract(1, 'day').endOf('day').format(FORMAT_TIME_FULL) });
        break;
      case ERangeDate.THIS_MONTH:
        onChange({ field: startDateFieldName, value: dayjs().startOf('month').format(FORMAT_TIME_FULL) });
        onChange({ field: endDateFieldName, value: dayjs().endOf('month').format(FORMAT_TIME_FULL) });
        break;
      case ERangeDate.LAST_MONTH:
        onChange({
          field: startDateFieldName,
          value: dayjs().subtract(1, 'month').startOf('month').format(FORMAT_TIME_FULL),
        });
        onChange({
          field: endDateFieldName,
          value: dayjs().subtract(1, 'month').endOf('month').format(FORMAT_TIME_FULL),
        });
        break;
      case ERangeDate.SELECT_DATE:
        onChange({
          field: startDateFieldName,
          value: '',
        });
        onChange({
          field: endDateFieldName,
          value: '',
        });
        break;
      default:
        break;
    }
  };

  const showDateRange =
    value?.[rangeTypeFieldName] === ERangeDate.SELECT_DATE ||
    value?.[rangeTypeFieldName] === ECustomerPrepaidDetailRangeDate.EXPIRED_DATE ||
    value?.[rangeTypeFieldName] === ECustomerPrepaidDetailRangeDate.PURCHASE_DATE ||
    value?.[rangeTypeFieldName] === ECustomerPrepaidDetailRangeDate.LAST_CONSUMED;

  return (
    <FormControl {...formControlProps}>
      {label && <StyledLabel variant="heading-medium-700">{label}</StyledLabel>}
      <Stack direction="row" alignItems="center" gap={1.5} width="100%">
        {!isEmpty(rangeDateTypeOptions) && (
          <Select
            id={id}
            labelId={labelId}
            multiline={false}
            value={value?.[rangeTypeFieldName]}
            handleOnChange={value => {
              if (typeof value === 'string') handleOnSelectChange(value);
            }}
            options={rangeDateTypeOptions}
            showHelperText={false}
            sx={theme => ({ minWidth: 'fit-content' })}
          />
        )}
        {showDateRange && (
          <>
            <CustomDatePicker
              formControlProps={{
                sx: {
                  flex: 1,
                },
              }}
              value={transformStartTimeValue}
              onChange={newValue =>
                onChange &&
                onChange({ field: startDateFieldName, value: (newValue as Dayjs)?.format(FORMAT_TIME_FULL) })
              }
            />
            <Typography variant="body-xlarge-400">To</Typography>
            <CustomDatePicker
              formControlProps={{
                sx: {
                  flex: 1,
                },
              }}
              value={transformEndTimeValue}
              onChange={newValue =>
                onChange && onChange({ field: endDateFieldName, value: (newValue as Dayjs)?.format(FORMAT_TIME_FULL) })
              }
            />
          </>
        )}
      </Stack>
    </FormControl>
  );
};
