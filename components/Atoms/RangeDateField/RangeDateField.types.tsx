'use client';

import { FormControlProps } from '@mui/material/FormControl';
import { TSelectOption } from '@/components/Molecules/RHFItems/RHFSelect/RHFSelect';

export type TRangeDateFieldProps = {
  label?: string;
  value: Record<string, string>;
  rangeTypeFieldName: string;
  startDateFieldName: string;
  endDateFieldName: string;
  formControlProps?: FormControlProps;
  rangeDateTypeOptions?: TSelectOption[];
  error?: boolean;
  onChange?: (val: { field: string; value: string }) => void;
};
