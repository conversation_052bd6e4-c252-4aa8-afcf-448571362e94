import { Meta, StoryObj } from '@storybook/react';
import { RangeDateField } from './RangeDateField';

export default {
  title: 'Atoms/RangeDateField',
  component: RangeDateField,
  tags: ['autodocs'],
} satisfies Meta<typeof RangeDateField>;

type Story = StoryObj<typeof RangeDateField>;

export const Default: Story = {
  args: { label: 'this is label' },
  render: args => (
    <div style={{ width: '200px' }}>
      <RangeDateField {...args} />
    </div>
  ),
};
