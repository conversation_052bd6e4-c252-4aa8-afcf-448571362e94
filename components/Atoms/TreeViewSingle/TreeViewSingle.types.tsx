import { FormControlProps } from '@mui/material/FormControl';
import { TreeViewProps } from '@mui/x-tree-view/TreeView/TreeView.types';

export type TTreeViewOption = {
  id: string;
  name: string | React.ReactNode;
  children?: TTreeViewOption[];
  items?: TTreeViewOption[];
  parent?: {
    id: string;
    name: string;
  };
} & { [key: string]: string | number | boolean | object };

export type TTreeViewSingle = {
  options?: TTreeViewOption;
  onSelectedChange: (nodeId: string) => void;
  formControlProps?: FormControlProps;
  label?: string;
  showHelperText?: boolean;
  helperText?: string;
  children?: React.ReactNode;
  disabled?: boolean;
  onSearch?: (search: string) => void;
  keyword?: string;
} & TreeViewProps<false>;
