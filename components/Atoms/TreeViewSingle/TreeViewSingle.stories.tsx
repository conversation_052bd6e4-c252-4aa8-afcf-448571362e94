import { Meta, StoryObj } from '@storybook/react';
import Stack from '@mui/material/Stack';
import { Theme } from '@mui/material';
import { TreeViewSingle } from './TreeViewSingle';
import { TTreeViewOption } from './TreeViewSingle.types';
import { Typography } from '../Typography/Typography';

export default {
  title: 'Atoms/TreeViewSingle',
  component: TreeViewSingle,
  tags: ['autodocs'],
} satisfies Meta<typeof TreeViewSingle>;

type Story = StoryObj<typeof TreeViewSingle>;

const data: TTreeViewOption = {
  id: 'root',
  name: 'Parent',
  children: [
    {
      id: '1',
      name: 'Child - 1',
      children: [],
    },
    {
      id: '3',
      name: 'Child - 3',
      children: [
        {
          id: '4',
          name: 'Child - 4',
          children: [],
        },
      ],
    },
  ],
};

export const Default: Story = {
  args: { options: data },
  render: args => (
    <TreeViewSingle {...args}>
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={(theme: Theme) => ({
          width: '100%',
          backgroundColor: theme.palette.common.white,
          border: `0.5px solid ${theme.palette.neutrals.N70.main}`,
          padding: theme.spacing(1.5),
          borderRadius: theme.spacing(1.25),
          '&:hover': {
            border: `0.5px solid ${theme.palette.neutrals.N50.main}`,
          },
        })}
      >
        <Typography variant="body-xlarge-400">Beverage & Dessert</Typography>
        <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M5.75 7.875L11 13.125L16.25 7.875"
            stroke="#3C2313"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </Stack>
    </TreeViewSingle>
  ),
};
