import { FormControl, IconButton, Popover, Stack, Theme } from '@mui/material';
import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';
import { TreeView as MUITreeView } from '@mui/x-tree-view/TreeView';
import _, { isEmpty } from 'lodash';
import React, { useMemo } from 'react';
import { customPalettes } from '@/theme/customPalettes';
import { convertToSelectOptions } from '@/lib/utils/array';
import Price from '@/components/Molecules/Price';
import { StyledFormHelperText } from '../Autocomplete/Autocomplete.styled';
import Icon from '../Icon';
import { StyledLabel } from '../TextField/TextField.styled';
import Typography from '../Typography';
import { StyledButton, StyledTreeItem } from './TreeViewSingle.styled';
import { TTreeViewOption, TTreeViewSingle } from './TreeViewSingle.types';
import { TextField } from '../TextField/TextField';
import { BoldCloseIcon } from '../IconsComponent';

const MinusSquare = (props: SvgIconProps) => (
  <SvgIcon fontSize="inherit" style={{ width: 14, height: 14 }} {...props}>
    {/* tslint:disable-next-line: max-line-length */}
    <path d="M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 11.023h-11.826q-.375 0-.669.281t-.294.682v0q0 .401.294 .682t.669.281h11.826q.375 0 .669-.281t.294-.682v0q0-.401-.294-.682t-.669-.281z" />
  </SvgIcon>
);

const PlusSquare = (props: SvgIconProps) => (
  <SvgIcon fontSize="inherit" style={{ width: 14, height: 14 }} {...props}>
    {/* tslint:disable-next-line: max-line-length */}
    <path d="M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 12.977h-4.923v4.896q0 .401-.281.682t-.682.281v0q-.375 0-.669-.281t-.294-.682v-4.896h-4.923q-.401 0-.682-.294t-.281-.669v0q0-.401.281-.682t.682-.281h4.923v-4.896q0-.401.294-.682t.669-.281v0q.401 0 .682.281t.281.682v4.896h4.923q.401 0 .682.281t.281.682v0q0 .375-.281.669t-.682.294z" />
  </SvgIcon>
);

const renderTree = (nodes: TTreeViewOption) => {
  const isParent = Array.isArray(nodes?.children) || Array.isArray(nodes?.items);
  let label = nodes.name;
  if (nodes?.price) {
    label = (
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography variant="body-large-400" color={customPalettes?.neutrals?.N50?.main}>
          {nodes?.name}
        </Typography>
        <Price
          typographyProps={{ variant: 'heading-medium-700', color: customPalettes?.neutrals?.N50?.main }}
          amount={nodes?.price as number}
        />
      </Stack>
    );
  }
  return (
    <StyledTreeItem isParent={isParent} key={nodes.id} nodeId={nodes.id} label={label}>
      {isParent ? (nodes?.children || nodes?.items || [])?.map(node => renderTree(node)) : null}
    </StyledTreeItem>
  );
};

export const TreeViewSingle: React.FC<TTreeViewSingle> = ({
  label,
  options,
  selected,
  onSelectedChange,
  formControlProps,
  helperText = '',
  showHelperText = true,
  children,
  disabled,
  onSearch,
  keyword,
  ...restProps
}) => {
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) {
      event.preventDefault();
      return;
    }
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSelected = (nodeSelected: string) => {
    if (nodeSelected === selected) return;
    onSelectedChange(nodeSelected);
  };

  const open = Boolean(anchorEl);

  const defaultExpaned = useMemo(
    () =>
      convertToSelectOptions(options?.children || [])
        ?.filter(option => !isEmpty(option?.items) || !isEmpty(option.children))
        ?.map(option => option?.id) || [],
    [JSON.stringify(options)]
  );
  return (
    <FormControl {...formControlProps} variant="filled" error={!!helperText}>
      {label && <StyledLabel variant="heading-medium-700">{label}</StyledLabel>}
      <StyledButton onClick={handleClick} disableRipple disabled={disabled}>
        {children}
      </StyledButton>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        sx={theme => ({
          marginTop: theme.spacing(0.625),
        })}
      >
        <Stack gap={1} p={1}>
          <TextField
            placeholder="Service name"
            value={keyword}
            onChange={e => onSearch && onSearch(e.target.value)}
            sx={{ width: '100%' }}
            InputProps={{
              endAdornment: keyword ? (
                <IconButton
                  onClick={() => onSearch && onSearch('')}
                  sx={{ color: customPalettes?.neutrals?.N50?.main }}
                >
                  <BoldCloseIcon style={{ width: '18px', height: '18px' }} />
                </IconButton>
              ) : undefined,
            }}
          />
          <MUITreeView
            aria-label="customized"
            defaultExpanded={defaultExpaned}
            onNodeSelect={(event, id) => {
              handleSelected(id);
            }}
            defaultCollapseIcon={<MinusSquare />}
            defaultExpandIcon={<PlusSquare />}
            sx={(theme: Theme) => ({
              minWidth: theme.spacing(490 / 8),
              overflowX: 'hidden',
              padding: theme.spacing(1),
            })}
            selected={selected}
            {...restProps}
          >
            {options && options.children?.map(children => renderTree(children))}
          </MUITreeView>
        </Stack>
      </Popover>
      {showHelperText && (
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
          {helperText && (
            <>
              <Icon variant="warning" size={1.5} />
              <StyledFormHelperText>{helperText}</StyledFormHelperText>
            </>
          )}
        </Stack>
      )}
    </FormControl>
  );
};
