import styled from '@emotion/styled';
import { alpha } from '@mui/material';
import Button, { buttonClasses } from '@mui/material/Button';
import { TreeItem, TreeItemProps, treeItemClasses } from '@mui/x-tree-view/TreeItem';
import React from 'react';

export const CustomTreeItem = React.forwardRef((props: TreeItemProps, ref: React.Ref<HTMLLIElement>) => (
  <TreeItem {...props} ref={ref} />
));

export const StyledTreeItem = styled(CustomTreeItem)<{ isParent: boolean }>(({ theme, isParent }) => ({
  [`& .${treeItemClasses.iconContainer}`]: {
    '& .close': {
      opacity: 0.3,
    },
  },
  [`& .${treeItemClasses.group}`]: {
    marginLeft: 15,
    paddingLeft: 18,
    borderLeft: `1px dashed ${alpha(theme.palette.text.primary, 0.4)}`,
  },
  [`.${treeItemClasses.selected}`]: {
    background: `${theme.palette.neutrals.N210.main} !important`,
  },
  '.MuiTreeItem-label': {
    fontSize: `${theme.spacing(2)} !important`,
    fontWeight: `${isParent ? 700 : 400} !important`,
  },
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    display: 'flex',
    width: '100%',
    padding: 0,
    backgroundColor: 'transparent',
  },
}));
