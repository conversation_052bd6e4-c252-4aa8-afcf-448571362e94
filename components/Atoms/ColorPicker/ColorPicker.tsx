import IconButton from '@mui/material/IconButton';
import Popover from '@mui/material/Popover';
import { useId, useState } from 'react';
import { SketchPicker } from 'react-color';
import Icon from '../Icon';
import Typography from '../Typography';
import { StyledInputWrapper } from './ColorPicker.styled';

export interface IColorPicker {
  value: string;
  onChange: (color: string) => void;
}

export const ColorPicker: React.FC<IColorPicker> = ({ value, onChange }) => {
  const uniqueId = useId();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [open, setOpen] = useState<boolean>(false);

  const handleClose = () => {
    setAnchorEl(null);
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };
  const id = open ? `color-picker-popover-${uniqueId}` : undefined;
  return (
    <>
      <StyledInputWrapper onClick={handleOpen}>
        <Typography variant="body-xlarge-400" textTransform="uppercase">
          {value}
        </Typography>
        <IconButton
          disableRipple
          ref={ref => {
            setAnchorEl(ref);
          }}
          sx={{
            padding: 0,
          }}
        >
          <Icon variant="color_wheel" />
        </IconButton>
      </StyledInputWrapper>
      <Popover
        id={id}
        open={open && Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={theme => ({
          marginTop: theme.spacing(1.5),
        })}
        disableRestoreFocus
      >
        <SketchPicker
          color={value}
          onChangeComplete={color => {
            onChange(color.hex);
          }}
        />
      </Popover>
    </>
  );
};
