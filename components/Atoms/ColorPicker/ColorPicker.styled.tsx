import styled from '@emotion/styled';
import Stack from '@mui/material/Stack';

export const StyledInputWrapper = styled(Stack)(({ theme }) => ({
  width: theme.spacing(27),
  flexDirection: 'row',
  flexWrap: 'wrap',
  justifyContent: 'space-between',
  alignItems: 'center',
  backgroundColor: theme.palette.common.white,
  borderRadius: theme.spacing(1.25),
  padding: theme.spacing(1.25),
  borderWidth: theme.spacing(1 / 16),
  borderStyle: 'solid',
  borderColor: theme.palette.neutrals.N180.main,
  cursor: 'pointer',
  '&:hover': {
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N50.main,
  },

  '&.focused': {
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N50.main,
  },

  '& input': {
    backgroundColor: theme.palette.common.white,
    color: theme.palette.neutrals.N40.main,
    boxSizing: 'border-box',
    width: '0',
    minWidth: '30px',
    flexGrow: '1',
    border: '0',
    margin: '0',
    outline: '0',
  },
}));
