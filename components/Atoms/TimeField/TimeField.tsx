'use client';

import React from 'react';
import FormControl from '@mui/material/FormControl';
import dayjs, { Dayjs } from 'dayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { isEmpty } from 'lodash';
import { StyledTimeField, StyledLabel } from './TimeField.styled';
import { TTimeFieldProps } from './TimeField.types';

export const TimeField: React.FC<TTimeFieldProps> = ({ formControlProps, label, value, onChange }) => {
  const transformValue = isEmpty(value) ? null : dayjs(value);
  return (
    <FormControl {...formControlProps}>
      {label && <StyledLabel variant="heading-medium-700">{label}</StyledLabel>}
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <StyledTimeField
          value={transformValue}
          onChange={newValue => onChange && onChange((newValue as Dayjs)?.toDate()?.toUTCString())}
          format="HH:mm"
          InputLabelProps={{
            shrink: true,
          }}
        />
      </LocalizationProvider>
    </FormControl>
  );
};
