import { Meta, StoryObj } from '@storybook/react';
import { TimeField } from './TimeField';

export default {
  title: 'Atoms/TimeField',
  component: TimeField,
  tags: ['autodocs'],
} satisfies Meta<typeof TimeField>;

type Story = StoryObj<typeof TimeField>;

export const Default: Story = {
  args: { label: 'this is label' },
  render: args => (
    <div style={{ width: '200px' }}>
      <TimeField {...args} />
    </div>
  ),
};
