import styled from '@emotion/styled';
import { TimeField } from '@mui/x-date-pickers';
import { outlinedInputClasses } from '@mui/material/OutlinedInput';
import Typography from '@/components/Atoms/Typography';

export const StyledTimeField = styled(TimeField)(({ theme }) => {
  const colorSetting = theme.palette.neutrals.N40.main;
  return {
    '& label.Mui-focused ': {
      color: colorSetting,
    },
    // '& .MuiInputLabel-root': { color: colorSetting },
    [`& .${outlinedInputClasses.root}`]: {
      borderRadius: 0,
      background: 'unset',
      ...theme.typography['heading-small-700'],
      [`& .${outlinedInputClasses.notchedOutline}`]: {
        top: theme.spacing(-5 / 8),
        borderWidth: 0,
      },
      [`&.${outlinedInputClasses.root}:hover .${outlinedInputClasses.notchedOutline}`]: {
        borderWidth: 0,
      },
      [`&.${outlinedInputClasses.focused} .${outlinedInputClasses.notchedOutline}`]: {
        borderWidth: 0,
      },
      [`&.${outlinedInputClasses.error} .${outlinedInputClasses.notchedOutline}`]: {
        borderWidth: 0,
      },
    },
    [`& .${outlinedInputClasses.multiline}`]: {
      padding: 0,
    },

    [`& .${outlinedInputClasses.input}`]: {
      padding: 0,
      width: '5ch',
      height: 'fit-content',
      ...theme.typography['heading-small-700'],
    },
  };
});

export const StyledLabel = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  color: theme.palette.neutrals.N50.main,
}));
