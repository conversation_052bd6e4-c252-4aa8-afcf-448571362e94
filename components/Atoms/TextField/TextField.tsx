'use client';

import FormControl from '@mui/material/FormControl';
import MenuItem from '@mui/material/MenuItem';
import React from 'react';
import { StyledLabel, StyledTextField } from './TextField.styled';

import { TTextFieldProps } from './TextField.types';

export const TextField: React.FC<TTextFieldProps> = ({
  options,
  formControlProps,
  InputProps,
  exceptThisSymbols,
  label,
  required,
  ...otherProps
}) => (
  <FormControl {...formControlProps}>
    {label && (
      <StyledLabel variant="heading-medium-700" $isRequired={!!required}>
        {label}
      </StyledLabel>
    )}
    <StyledTextField
      InputLabelProps={{
        shrink: true,
      }}
      color="primary"
      InputProps={{
        onKeyDown: (event: React.KeyboardEvent<HTMLInputElement>) => {
          if (exceptThisSymbols && exceptThisSymbols.includes(event.key)) {
            event.preventDefault();
          }
        },
        ...InputProps,
      }}
      {...otherProps}
    >
      {options &&
        options?.map(option => (
          <MenuItem key={option.id} value={option.id}>
            {option.name}
          </MenuItem>
        ))}
    </StyledTextField>
  </FormControl>
);
