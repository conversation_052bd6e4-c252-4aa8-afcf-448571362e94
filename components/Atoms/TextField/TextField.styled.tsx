import styled from '@emotion/styled';
import { TextField, formHelperTextClasses, outlinedInputClasses } from '@mui/material';

import Typography from '@/components/Atoms/Typography';
import warningIcon from '@/public/assets/icons/warning.svg';

export const StyledTextField = styled(TextField)<{ dynamicColor?: string; error?: boolean }>(({
  theme,
  dynamicColor,
  error,
}) => {
  const colorSetting = dynamicColor || theme.palette.neutrals.N40.main;
  return {
    '& label.Mui-focused ': {
      color: colorSetting,
    },
    // '& .MuiInputLabel-root': { color: colorSetting },
    [`& .${outlinedInputClasses.root}`]: {
      borderRadius: theme.spacing(1.25),
      background: theme.palette.neutrals.N70.main,
      ...theme.typography['body-xlarge-400'],
      [`& .${outlinedInputClasses.notchedOutline}`]: {
        top: theme.spacing(-5 / 8),
        borderWidth: theme.spacing(1 / 16),
        borderColor: theme.palette.neutrals.N40.main,
      },
      [`&.${outlinedInputClasses.root}:hover .${outlinedInputClasses.notchedOutline}`]: {
        borderWidth: theme.spacing(1 / 16),
        borderColor: theme.palette.neutrals.N50.main,
      },
      [`&.${outlinedInputClasses.focused} .${outlinedInputClasses.notchedOutline}`]: {
        borderWidth: theme.spacing(1 / 16),
        borderColor: theme.palette.neutrals.N50.main,
      },
      [`&.${outlinedInputClasses.error} .${outlinedInputClasses.notchedOutline}`]: {
        borderWidth: theme.spacing(1 / 16),
        borderColor: theme.palette.neutrals.N110.main,
      },
    },
    [`& .${outlinedInputClasses.multiline}`]: {
      padding: 0,
    },

    [`& .${outlinedInputClasses.input}`]: {
      padding: theme.spacing(1.5),
      height: theme.spacing(2.25),
      ...theme.typography['body-xlarge-400'],
    },

    '.MuiInputBase-inputMultiline': {
      ...theme.typography['body-medium-400'],
    },

    [`& .${formHelperTextClasses.root}`]: {
      minHeight: theme.spacing(2.5),
      paddingLeft: theme.spacing(2),
      margin: 0,
      marginTop: theme.spacing(0.5),
      color: theme.palette.error.main,
      ...theme.typography['body-small-400'],
      ...(error && { backgroundImage: `url(${warningIcon.src})`, backgroundRepeat: 'no-repeat' }),
    },
    '& input[type=number]': {
      '-moz-appearance': 'textfield',
    },
    '& input[type=number]::-webkit-outer-spin-button': {
      '-webkit-appearance': 'none',
      margin: 0,
    },
    '& input[type=number]::-webkit-inner-spin-button': {
      '-webkit-appearance': 'none',
      margin: 0,
    },
    textarea: {
      ...theme.typography['body-medium-400'],
    },
  };
});

export const StyledLabel = styled(Typography)<{ $isRequired?: boolean }>(({ theme, $isRequired }) => ({
  marginBottom: theme.spacing(1),
  color: theme.palette.neutrals.N50.main,
  maxHeight: '19.5px',
  ...($isRequired
    ? {
        ':before': {
          content: `"*"`,
          color: 'red',
        },
      }
    : {}),
}));
