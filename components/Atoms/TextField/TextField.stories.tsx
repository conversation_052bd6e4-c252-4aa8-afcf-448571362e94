import { Meta, StoryObj } from '@storybook/react';
import { TextField } from './TextField';

export default {
  title: 'Atoms/TextField',
  component: TextField,
  tags: ['autodocs'],
} satisfies Meta<typeof TextField>;

type Story = StoryObj<typeof TextField>;

export const Default: Story = {
  args: { label: 'this is label' },
  render: args => (
    <div style={{ width: '200px' }}>
      <TextField {...args} />
    </div>
  ),
};
