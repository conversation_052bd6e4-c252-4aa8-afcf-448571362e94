import styled from '@emotion/styled';
import FormHelperText, { formHelperTextClasses } from '@mui/material/FormHelperText';
import Stack from '@mui/material/Stack';
import Typography from '../Typography';
import { StyledTextField } from '../TextField/TextField.styled';
import { StyledSelect } from '../Select/Select.styled';

export const StyledConsumptionPeriodWrapper = styled(Stack)(({ theme }) => ({
  width: '100%',
  justifyContent: 'flex-start',
  flexDirection: 'row',
  alignItems: 'flex-start',
  gap: theme.spacing(1.5),
  [`& .MuiInputBase-root`]: {
    minWidth: '100px',
  },
}));

export const StyledLabel = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  color: theme.palette.neutrals.N50.main,
}));

export const StyledFormHelperText = styled(FormHelperText)(({ theme }) => ({
  [`&.${formHelperTextClasses.root}`]: {
    padding: 0,
    margin: 0,
    color: theme.palette.neutrals.N110.main,
    ...theme.typography['body-small-400'],
  },
}));

export const StyledDateTypeSelect = styled(StyledSelect)(({ theme }) => ({}));

export const StyledConsumptionPeriod = styled(StyledTextField)(({ theme }) => ({
  flex: 1,
}));
