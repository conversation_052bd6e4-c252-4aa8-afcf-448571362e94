import FormControl, { FormControlProps } from '@mui/material/FormControl';
import { SelectProps } from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import { TextFieldProps } from '@mui/material/TextField';
import { useEffect, useId, useState } from 'react';
import { TSelectOption } from '@/components/Molecules/RHFItems/RHFSelect/RHFSelect';
import Icon from '../Icon';
import Select from '../Select';
import {
  StyledConsumptionPeriod,
  StyledConsumptionPeriodWrapper,
  StyledFormHelperText,
  StyledLabel,
} from './SelectAndTextfield.styled';

export type TSelectAndTextField = {
  error?: boolean;
  helperText?: string;
  selectProps?: SelectProps;
  textfieldProps?: TextFieldProps;
  label?: string;
  formControlProps?: FormControlProps;
  value: Record<string, string>;
  selectFieldName: string;
  textfieldFieldName: string;
  onChange?: (val: { field: string; value: number | string }) => void;
  options?: TSelectOption[];
  disabled?: boolean;
};

export const SelectAndTextfield: React.FC<TSelectAndTextField> = ({
  label,
  selectProps,
  textfieldProps,
  selectFieldName,
  textfieldFieldName,
  value,
  helperText,
  formControlProps,
  onChange,
  options = [],
  disabled = false,
}) => {
  const id = `period-select-${useId()}`;
  const labelId = `period-select-label-${useId()}`;

  const [selectValue, setSelectValue] = useState(value[selectFieldName]);

  const onChangeSelect = (value?: string | string[]) => {
    if (onChange && typeof value === 'string') {
      setSelectValue(value);
      onChange({ field: selectFieldName, value });
    }
  };

  useEffect(() => {
    if (value[selectFieldName] !== selectValue) {
      setSelectValue(value[selectFieldName]);
    }
  }, [value[selectFieldName]]);

  return (
    <FormControl {...formControlProps} variant="filled" error={!!helperText}>
      <StyledLabel variant="heading-medium-700">{label}</StyledLabel>
      <StyledConsumptionPeriodWrapper>
        <Select
          id={id}
          labelId={labelId}
          multiline={false}
          // defaultValue={options?.at(0)?.id}
          // @ts-ignore
          value={selectValue}
          handleOnChange={onChangeSelect}
          {...selectProps}
          options={options}
          disabled={disabled}
        />
        <StyledConsumptionPeriod
          value={value[textfieldFieldName]}
          error={!!helperText}
          InputProps={{ inputProps: { min: 0 } }}
          onChange={e => {
            if (onChange) onChange({ field: textfieldFieldName, value: Number(e.target.value) });
          }}
          {...textfieldProps}
          type="number"
          disabled={disabled}
        />
      </StyledConsumptionPeriodWrapper>
      {helperText && (
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
          <Icon variant="warning" size={1.5} />
          <StyledFormHelperText>{helperText}</StyledFormHelperText>
        </Stack>
      )}
    </FormControl>
  );
};
