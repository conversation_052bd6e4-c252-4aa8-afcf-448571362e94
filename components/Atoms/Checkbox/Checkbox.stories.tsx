import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import Checkbox from '.';

const meta: Meta = {
  title: 'Atoms/Checkbox',
  component: Checkbox,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof Checkbox>;

export const Default: Story = {
  args: {
    label: 'My Profile',
  },
  render: args => (
    <Box
      width="100%"
      p={2}
      sx={{
        backgroundColor: '#EADFCE',
      }}
    >
      <Checkbox checked {...args} />
    </Box>
  ),
};
