import styled from '@emotion/styled';
import Checkbox from '@mui/material/Checkbox';
import Stack from '@mui/material/Stack';
import { checkboxClasses } from '@mui/material';

export const StyledCheckboxContainer = styled(Stack)<{ disabled?: boolean }>(({ theme, disabled }) => ({
  justifyContent: 'flex-start',
  alignItems: 'center',
  flexDirection: 'row',
  gap: theme.spacing(1.5),
  cursor: disabled ? 'not-allowed' : 'pointer',
  userSelect: 'none',
}));

export const StyledCheckbox = styled(Checkbox)(({ theme }) => ({
  [`&.${checkboxClasses.root}`]: {
    padding: 0,
  },
  [`&.${checkboxClasses.sizeMedium} svg`]: {
    width: theme.spacing(3.25),
    height: theme.spacing(3.25),
  },
  [`&.${checkboxClasses.sizeSmall} svg`]: {
    width: theme.spacing(2.25),
    height: theme.spacing(2.25),
  },
  [`&.${checkboxClasses}`]: {
    color: theme.palette.neutrals.N40.main,
  },
  [`&.${checkboxClasses.root}.${checkboxClasses.disabled}`]: {
    color: theme.palette.neutrals.N40.main,
  },
}));
