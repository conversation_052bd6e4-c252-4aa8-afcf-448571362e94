import React, { ReactNode } from 'react';
import { CheckboxProps, StackProps, TypographyProps } from '@mui/material';
import { customPalettes } from '@/theme/customPalettes';
import Typography from '../Typography';
import { StyledCheckbox, StyledCheckboxContainer } from './Checkbox.styled';
import { CheckedIcon, UncheckedIcon } from '../IconsComponent';

export type CheckboxSizeType = 'medium' | 'small';

export type CheckboxColorType = 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';

export interface ICheckboxProps {
  label?: string | ReactNode;
  checked?: boolean;
  disabled?: boolean;
  size?: CheckboxSizeType;
  color?: CheckboxColorType;
  containerProps?: StackProps;
  checkboxProps?: CheckboxProps;
  labelProps?: TypographyProps;
  onChange?: (checked: boolean) => void;
}

export const Checkbox: React.FC<ICheckboxProps> = ({
  label = '',
  checked = false,
  disabled = false,
  size = 'medium',
  color = 'success',
  checkboxProps,
  containerProps,
  labelProps,
  onChange,
}) => (
  <StyledCheckboxContainer
    onClick={e => {
      if (!disabled && onChange) {
        onChange(!checked);
      }
    }}
    disabled={disabled}
    {...containerProps}
  >
    <StyledCheckbox
      checked={checked}
      disabled={disabled}
      color={color}
      size={size}
      icon={<UncheckedIcon />}
      checkedIcon={<CheckedIcon />}
      {...checkboxProps}
    />
    {label &&
      (typeof label === 'string' ? (
        <Typography
          variant={size === 'medium' ? 'heading-medium-700' : 'heading-xsmall-700'}
          color={disabled ? customPalettes.neutrals?.N40.main : customPalettes.neutrals?.N50.main}
          {...labelProps}
        >
          {label}
        </Typography>
      ) : (
        label
      ))}
  </StyledCheckboxContainer>
);
