import styled from '@emotion/styled';
import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';

export const DividerContainer = styled(Stack)(({ theme }) => ({
  display: 'flex',
  alignItems: 'flex-start',
  width: '100%',
  flexDirection: 'column',
}));

export const StyledDivider = styled(Divider)(({ theme }) => ({
  width: '100%',
  marginTop: theme.spacing(3),
  marginBottom: theme.spacing(3),
  background: theme.palette.neutrals.N50.main,
}));
