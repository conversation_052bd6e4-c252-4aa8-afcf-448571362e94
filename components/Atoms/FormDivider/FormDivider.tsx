import { DividerProps } from '@mui/material';
import { StyledLabel } from '../TextField/TextField.styled';
import { ITypographyProps } from '../Typography';
import { DividerContainer, StyledDivider } from './FormDivider.styled';

export type TFormDivider = {
  label?: string;
  subLabel?: string;
  labelProps?: ITypographyProps;
  subLabelProps?: ITypographyProps;
  dividerProps?: DividerProps;
};

export const FormDivider: React.FC<TFormDivider> = ({ subLabel, label, labelProps, subLabelProps, dividerProps }) => (
  <DividerContainer>
    <StyledDivider {...dividerProps} />
    {label && (
      <StyledLabel variant="heading-medium-700" {...labelProps}>
        {label}
      </StyledLabel>
    )}
    {subLabel && (
      <StyledLabel variant="body-medium-400" {...subLabelProps}>
        {subLabel}
      </StyledLabel>
    )}
  </DividerContainer>
);
