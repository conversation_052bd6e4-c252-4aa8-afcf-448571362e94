'use client';

import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody, { TableBodyProps } from '@mui/material/TableBody';
import { TableCellProps } from '@mui/material/TableCell';
import { TableContainerProps } from '@mui/material/TableContainer';
import TableRow, { TableRowProps } from '@mui/material/TableRow';
import { visuallyHidden } from '@mui/utils';
import React, { Fragment, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import Skeleton from '../Skeleton';
import {
  StyledBodyTableCell,
  StyledHeadTableCell,
  StyledSTTTableCell,
  StyledTableContainer,
  StyledTableHead,
} from './Table.styled';

import { useThrottleLoading } from '@/lib/hooks/utils/useThrottleLoading';
import { CustomImage } from '../CustomImage/CustomImage';
import CustomTableRow from '../CustomTableRow';
import Typography from '../Typography';
import Icon from '../Icon';

type TLoadingType = 'skeleton' | 'endLine';

export type TColumnTable = {
  name: string;
  label: string | React.ReactNode;
  tableCellProps?: TableCellProps;
  activeSort?: boolean;
  orderBy?: string;
  ordering?: 'asc' | 'desc';
  hideOnMobile?: boolean;
  children?: TColumnTable[];
  rowCollapsiable?: boolean;
  verticalAlign?: 'top' | 'middle' | 'bottom';
  sortColumnName?: string;
  renderCollapseRow?: (row: any, index?: number) => React.ReactNode;
  render?: (row: any, index?: number, onToggle?: (key: string) => void, collapsed?: boolean) => React.ReactNode;
  renderLabel?: (row: any, index?: number) => React.ReactNode;
  onSortClick?: (columnName: string) => void;
};

export type TTableProps = {
  columns: TColumnTable[];
  rows: any[];
  isLoading?: boolean;
  showSTT?: boolean;
  tableContainerProps?: TableContainerProps;
  tableBodyProps?: TableBodyProps;
  tableCellProps?: TableCellProps;
  sttProps?: TableCellProps;
  infinitiLoad?: boolean;
  rowProps?: TableRowProps;
  selectedRowOnClick?: boolean;
  hideTableHead?: boolean;
  loadingType?: TLoadingType;
  hasBorder?: boolean;
  hasHeaderBackground?: boolean;
  groupByColumns?: string[];
  stickyHeader?: boolean;
  tableFooter?: ReactNode;
  page?: number;
  rowCollapsiable?: boolean;
  defaultActiveRowKeys?: string[];
  onLoadMore?: () => void;
  onRowClick?: (row: any) => void;
  onRowHover?: (row: any) => void;
  showNoData?: boolean;
};

export const CustomTable: React.FC<TTableProps> = ({
  columns,
  rows,
  showSTT = true,
  onRowClick,
  tableContainerProps,
  tableBodyProps,
  sttProps,
  isLoading: tempLoading,
  infinitiLoad = false,
  rowProps,
  onRowHover,
  selectedRowOnClick,
  hideTableHead = false,
  loadingType = 'skeleton',
  hasBorder = false,
  hasHeaderBackground = false,
  groupByColumns = [],
  stickyHeader = false,
  tableFooter = null,
  page = 1,
  rowCollapsiable = false,
  defaultActiveRowKeys,
  onLoadMore = () => undefined,
  showNoData = false,
}) => {
  const [observerTarget, setObserverTarget] = useState();
  const [selectedRow, setSelectedRow] = useState<any>();
  const [localRow, setLocalRow] = useState<any[]>([]);

  const isLoading = useThrottleLoading({ value: !!tempLoading });

  const [activeRowKeys, setActiveRowKeys] = useState<string[]>([]);

  useEffect(() => {
    if (defaultActiveRowKeys) {
      setActiveRowKeys([...defaultActiveRowKeys]);
    }
  }, [defaultActiveRowKeys]);

  const isSelectedRow = (rowIndx: number) => selectedRowOnClick && selectedRow === rowIndx;

  const refObser = useCallback(
    (node: any) => {
      if (node !== null) {
        setObserverTarget(node);
      }
    },
    [rows]
  );
  useEffect(() => {
    if (infinitiLoad && page > 1) return setLocalRow((pre: any) => [...(pre || []), ...rows]);
    setLocalRow(rows);
  }, [JSON.stringify(rows)]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          onLoadMore();
        }
      },
      { threshold: 1 }
    );
    if (observerTarget) {
      observer.observe(observerTarget);
    }

    return () => {
      if (observerTarget) {
        observer.unobserve(observerTarget);
      }
    };
  }, [observerTarget]);

  const skeletonLoading = useMemo(() => {
    const result = new Array(5).fill(undefined)?.map(_ => (
      <TableRow key={Math.random()}>
        {showSTT && (
          <StyledHeadTableCell key={`${Math.random()}--stt`} component="th" scope="row">
            <Skeleton variant="text" fontSize={2} width={50} height={30} />
          </StyledHeadTableCell>
        )}
        {columns?.map(({ name, children = [] }, index) => {
          if (children.length === 0) {
            return (
              <StyledHeadTableCell key={name} component="th" scope="row">
                <Skeleton variant="text" fontSize={2} width={!showSTT && index === 0 ? 50 : 250} height={30} />
              </StyledHeadTableCell>
            );
          }
          return children.map(({ name }, index) => (
            <StyledHeadTableCell key={name} component="th" scope="row">
              <Skeleton variant="text" fontSize={2} width={!showSTT && index === 0 ? 50 : 250} height={30} />
            </StyledHeadTableCell>
          ));
        })}
      </TableRow>
    ));
    return result;
  }, [columns, isLoading]);

  const skeleton = <Skeleton variant="text" fontSize={2} width={50} height={30} />;
  const checkRow = localRow?.filter(Boolean) || [];

  const hasChildren = columns.findIndex(({ children = [] }) => children.length > 0) > -1;
  const usedGroupByColumns: string[] = [];

  const onToggle = (currKey: string) => {
    setActiveRowKeys(prev => (prev.includes(currKey) ? prev.filter(item => item !== currKey) : [...prev, currKey]));
  };

  if (localRow.length === 0 && !isLoading && showNoData)
    return (
      <Stack
        sx={{
          position: 'relative',
          width: 150,
          height: 140,
          left: '50%',
          transform: 'translate(-50%,-50%)',
          top: '50%',
        }}
      >
        <CustomImage src="/assets/images/empty-data.png" alt="empty" />
      </Stack>
    );

  return (
    <StyledTableContainer component={Paper} {...tableContainerProps}>
      <Table stickyHeader={stickyHeader} aria-label="sticky table">
        {!hideTableHead && (
          <StyledTableHead $stickyHeader={stickyHeader} $hasborder={!isLoading && hasBorder}>
            <TableRow>
              {showSTT && (
                <StyledSTTTableCell
                  key="stt"
                  rowSpan={hasChildren ? 2 : 0}
                  $hasheaderbackground={hasHeaderBackground}
                  {...sttProps}
                >
                  {isLoading ? skeleton : '#'}
                </StyledSTTTableCell>
              )}
              {columns?.map(
                ({
                  name,
                  ordering,
                  label,
                  activeSort,
                  tableCellProps,
                  hideOnMobile,
                  children = [],
                  sortColumnName,
                  renderLabel,
                  onSortClick,
                }) => {
                  const labelContent = renderLabel ? renderLabel(label) : label;
                  return (
                    <StyledHeadTableCell
                      key={name}
                      $hideOnMobile={hideOnMobile}
                      colSpan={children.length || 0}
                      rowSpan={children.length === 0 ? 2 : undefined}
                      $hasheaderbackground={hasHeaderBackground}
                      {...tableCellProps}
                    >
                      <Stack
                        direction="row"
                        gap={0.5}
                        alignItems="center"
                        sx={{ cursor: activeSort ? 'pointer' : 'auto' }}
                        onClick={() => {
                          if (activeSort && typeof onSortClick === 'function') {
                            onSortClick(sortColumnName || name);
                          }
                        }}
                      >
                        <Typography
                          component="h5"
                          variant="heading-xsmall-700"
                          textTransform="uppercase"
                          sx={{ userSelect: 'none' }}
                        >
                          {isLoading ? skeleton : labelContent}
                        </Typography>
                        {activeSort &&
                          (ordering === 'asc' ? (
                            <Icon variant="chevron_down_black" />
                          ) : (
                            <Icon variant="chevron_up_black" />
                          ))}
                      </Stack>
                    </StyledHeadTableCell>
                  );
                }
              )}
              {rowCollapsiable && (
                <StyledSTTTableCell
                  key="collapsiable"
                  rowSpan={hasChildren ? 2 : 0}
                  $hasheaderbackground={hasHeaderBackground}
                  {...sttProps}
                />
              )}
            </TableRow>
            {hasChildren && (
              <TableRow>
                {columns?.map(({ children = [] }) => {
                  if (children?.length === 0) {
                    return <></>;
                  }
                  return children?.map(({ name, ordering, label, activeSort, tableCellProps, hideOnMobile }) => (
                    <StyledHeadTableCell
                      key={name}
                      $hideOnMobile={hideOnMobile}
                      $hasheaderbackground={hasHeaderBackground}
                      $stickyHeader={stickyHeader}
                      {...tableCellProps}
                    >
                      <Typography component="h5" variant="heading-xsmall-700" textTransform="uppercase">
                        {isLoading ? skeleton : label}
                      </Typography>
                      {activeSort && (
                        <Box component="span" sx={visuallyHidden}>
                          {ordering === 'desc' ? 'sorted descending' : 'sorted ascending'}
                        </Box>
                      )}
                    </StyledHeadTableCell>
                  ));
                })}
              </TableRow>
            )}
          </StyledTableHead>
        )}
        <TableBody {...tableBodyProps}>
          {loadingType === 'skeleton' && isLoading
            ? skeletonLoading
            : checkRow?.map((row, index) => (
                <Fragment key={`table-rows-container-${index.toString()}`}>
                  <CustomTableRow
                    key={`table-rows-${index.toString()}`}
                    onClick={() => {
                      if (selectedRowOnClick) {
                        setSelectedRow(index);
                      }
                      if (onRowClick) {
                        onRowClick(row);
                      }
                    }}
                    onMouseOver={() => {
                      if (onRowHover) {
                        onRowHover(row);
                      }
                    }}
                    ref={infinitiLoad && index === checkRow.length - 1 ? refObser : null}
                    selected={isSelectedRow(index)}
                    {...rowProps}
                  >
                    {showSTT && (
                      <StyledBodyTableCell
                        key={`stt-${index.toString()}`}
                        component="th"
                        scope="row"
                        {...sttProps}
                        sx={{
                          borderBottom:
                            rowCollapsiable && activeRowKeys?.includes(index.toString()) ? 'unset !important' : 'auto',
                          paddingBottom:
                            rowCollapsiable && activeRowKeys?.includes(index.toString()) ? '0px !important' : 'inherit',
                        }}
                      >
                        {(index + 1).toLocaleString('en-US', {
                          minimumIntegerDigits: 1,
                          useGrouping: false,
                        })}
                      </StyledBodyTableCell>
                    )}
                    {columns?.map(
                      (
                        { name, render, tableCellProps, hideOnMobile, verticalAlign, children = [] }: TColumnTable,
                        columnIndex: number
                      ) => {
                        let rowSpan;
                        if (groupByColumns.includes(name) && !usedGroupByColumns.includes(row[name])) {
                          rowSpan = checkRow.reduce((total, item) => {
                            if (item?.[name] === row?.[name]) {
                              return total + 1;
                            }
                            return total;
                          }, 0);
                          usedGroupByColumns.push(row[name]);
                        }
                        if (!rowSpan && usedGroupByColumns.includes(row[name])) return null;
                        if (children.length === 0) {
                          return (
                            <StyledBodyTableCell
                              key={`${name as string}_${index + 1}_${columnIndex + 1}`}
                              component="th"
                              scope="row"
                              rowSpan={rowSpan}
                              $hideOnMobile={hideOnMobile}
                              $hasborder={hasBorder}
                              {...tableCellProps}
                              sx={
                                rowCollapsiable
                                  ? {
                                      borderBottom:
                                        rowCollapsiable && activeRowKeys?.includes(index.toString())
                                          ? 'none !important'
                                          : 'inherit',
                                      paddingBottom:
                                        rowCollapsiable && activeRowKeys?.includes(index.toString())
                                          ? '0px !important'
                                          : 'inherit',
                                      verticalAlign: verticalAlign ? `${verticalAlign} !important` : 'middle',
                                    }
                                  : tableCellProps?.sx
                              }
                            >
                              {render
                                ? render(row, index, onToggle, activeRowKeys?.includes(index.toString()))
                                : row[name]}
                            </StyledBodyTableCell>
                          );
                        }
                        return children?.map(
                          ({
                            name: childrenName,
                            render: childrenRender,
                            tableCellProps: childrenTableCellProps,
                            hideOnMobile: childrenHideOnMobile,
                          }: TColumnTable) => (
                            <StyledBodyTableCell
                              key={`${name as string}_${index + 1}_${columnIndex + 1}_${childrenName}`}
                              component="th"
                              scope="row"
                              $hideOnMobile={childrenHideOnMobile}
                              $hasborder={hasBorder}
                              {...childrenTableCellProps}
                            >
                              {childrenRender
                                ? childrenRender(row, index, onToggle, activeRowKeys?.includes(index.toString()))
                                : row[name][childrenName]}
                            </StyledBodyTableCell>
                          )
                        );
                      }
                    )}
                  </CustomTableRow>
                  {rowCollapsiable && (
                    <CustomTableRow
                      key={`table-expand-rows-${index.toString()}`}
                      sx={{
                        visibility: activeRowKeys?.includes(index.toString()) ? 'visible' : 'collapse',
                        transition: 'transform .4s cubic-bezier(.5,0,.3,1)',
                      }}
                    >
                      {showSTT && (
                        <StyledBodyTableCell
                          key={`stt-expand-${index.toString()}`}
                          component="th"
                          scope="row"
                          {...sttProps}
                        />
                      )}
                      {columns?.map(
                        ({ name, renderCollapseRow, tableCellProps, hideOnMobile, children = [] }: TColumnTable) => {
                          let rowSpan;
                          if (groupByColumns.includes(name) && !usedGroupByColumns.includes(row[name])) {
                            rowSpan = checkRow.reduce((total, item) => {
                              if (item?.[name] === row?.[name]) {
                                return total + 1;
                              }
                              return total;
                            }, 0);
                            usedGroupByColumns.push(row[name]);
                          }
                          if (!rowSpan && usedGroupByColumns.includes(row[name])) return <></>;
                          if (children.length === 0) {
                            return (
                              <StyledBodyTableCell
                                key={name}
                                component="th"
                                scope="row"
                                rowSpan={rowSpan}
                                $hideOnMobile={hideOnMobile}
                                $hasborder={hasBorder}
                                {...tableCellProps}
                                sx={{ paddingTop: '0px !important' }} // fix bug when use group by
                              >
                                {renderCollapseRow ? renderCollapseRow(row, index) : ''}
                              </StyledBodyTableCell>
                            );
                          }
                          return <></>;
                        }
                      )}
                      <StyledBodyTableCell
                        key={`collapse-row-${index.toString()}`}
                        component="th"
                        scope="row"
                        {...sttProps}
                      />
                    </CustomTableRow>
                  )}
                </Fragment>
              ))}
          {tableFooter}
          {isLoading && loadingType === 'endLine' && (
            <Stack justifyContent="center" alignItems="center" width="100%" pt="4px">
              <CircularProgress variant="determinate" />
            </Stack>
          )}
        </TableBody>
      </Table>
    </StyledTableContainer>
  );
};
