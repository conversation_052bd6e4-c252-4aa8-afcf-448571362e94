import { Meta, StoryObj } from '@storybook/react';
import ConfirmModal, { TConfirmModalProps } from '.';

export default {
  title: 'Atoms/ConfirmModal',
  component: ConfirmModal,
  tags: ['autodocs'],
} satisfies Meta<TConfirmModalProps>;

type Story = StoryObj<typeof ConfirmModal>;

export const Default: Story = {
  args: {
    isOpen: true,
    title: 'Confirm Assign Service',
    bodyContent: 'Do you want to add Gold member for this user',
    cancelLabel: 'cancel',
    confirmLabel: 'checkout',
    onCancel: () => console.log('cancel'),
    onConfirm: () => console.log('confirm'),
  },
  render: args => <ConfirmModal {...args} />,
};
