import styled from '@emotion/styled';
import { buttonClasses } from '@mui/material';
import Button from '../Button';
import Typography from '../Typography';

export const StyledTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(1.25),
  color: theme.palette.neutrals.N50.main,
  textAlign: 'center',
  ...theme.typography['heading-xlarge-700'],
}));

export const StyledMessage = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  color: theme.palette.neutrals.N50.main,
  textAlign: 'center',
  ...theme.typography['body-xlarge-400'],
}));

export const StyleButton = styled(Button)<{ isCancelButton?: boolean }>(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    width: theme.spacing(16.75),
    textTransform: 'capitalize',
    ...theme.typography['heading-medium-700'],
  },
}));
