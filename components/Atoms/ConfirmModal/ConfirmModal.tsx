import { dialogClasses, DialogProps, Theme, useMediaQuery, useTheme } from '@mui/material';
import Stack, { StackProps } from '@mui/material/Stack';
import React, { ReactNode } from 'react';
import { IButtonProps } from '../Button';
import ComponentPortal from '../ComponentPortal';
import Modal from '../Modal';
import { StyleButton, StyledMessage, StyledTitle } from './ConfirmModal.styled';

export type TConfirmModalProps = {
  isOpen: boolean;
  title: ReactNode;
  bodyContent: ReactNode;
  dialogProps?: Partial<DialogProps>;
  cancelLabel?: string;
  confirmLabel?: string;
  cancelButtonProps?: IButtonProps;
  confirmButtonProps?: IButtonProps;
  buttonGroupProps?: StackProps;
  onClose: () => void;
  onCancel?: () => void;
  onConfirm: () => void;
};

export const ConfirmModal: React.FC<TConfirmModalProps> = ({
  isOpen,
  title,
  bodyContent,
  cancelLabel = 'Cancel',
  confirmLabel = 'Confirm',
  cancelButtonProps,
  confirmButtonProps,
  onClose,
  onCancel,
  onConfirm,
  buttonGroupProps = {},
  dialogProps = {},
}) => {
  const theme = useTheme();
  const showFullScreen = useMediaQuery(theme.breakpoints.down('md'));
  return (
    <ComponentPortal>
      <Modal
        isOpen={isOpen}
        handleClose={onClose}
        dialogProps={{
          fullScreen: showFullScreen,
          sx: theme => ({
            color: theme.palette.primary.main,
            [`& .${dialogClasses.paper}`]: {
              padding: theme.spacing(3),
              margin: theme.spacing(3),
              height: 'fit-content',
              borderRadius: theme.spacing(1.25),
            },
          }),
          ...dialogProps,
        }}
      >
        <Stack
          sx={(theme: Theme) => ({
            width: theme.spacing(62.75),
            [theme.breakpoints.down('md')]: {
              width: 'auto',
            },
          })}
        >
          <StyledTitle component="h4">{title}</StyledTitle>
          <StyledMessage component="p" textAlign="center" variant="heading-xlarge-700">
            {bodyContent}
          </StyledMessage>
          <Stack justifyContent="center" alignItems="center" direction="row" gap={2.25} {...buttonGroupProps}>
            {cancelLabel && (
              <StyleButton
                label={cancelLabel}
                variant="contained"
                color="primary"
                onClick={() => {
                  if (onCancel) onCancel();
                }}
                {...cancelButtonProps}
              />
            )}
            <StyleButton
              label={confirmLabel}
              variant="contained"
              color="warning"
              onClick={onConfirm}
              {...confirmButtonProps}
            />
          </Stack>
        </Stack>
      </Modal>
    </ComponentPortal>
  );
};
