import { StackProps, TabProps, Tabs, TabsProps } from '@mui/material';
import React from 'react';
import { StyledContainer, StyledTab, StyledTabs } from './CustomTabs.styled';

export interface ITabs {
  containerProps?: StackProps;
  tabsProps: TabsProps;
  tabOptions: TabProps[];
}

export const CustomTab: React.FC<ITabs> = ({ containerProps, tabsProps, tabOptions }) => (
  <StyledContainer {...containerProps}>
    <StyledTabs {...tabsProps} TabIndicatorProps={{ sx: { background: 'white', height: '3px', marginTop: '8px' } }}>
      {tabOptions?.map((option, indx) => <StyledTab {...option} />)}
    </StyledTabs>
  </StyledContainer>
);
