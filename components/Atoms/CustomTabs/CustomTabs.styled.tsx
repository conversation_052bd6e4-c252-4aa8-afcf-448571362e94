import styled from '@emotion/styled';
import { Stack, Tab, Tabs } from '@mui/material';

export const StyledContainer = styled(Stack)(({ theme }) => ({
  width: '100%',
  background: theme.palette.neutrals.N50.main,
  borderRadius: theme.spacing(1.25),
  padding: theme.spacing(1.5, 3),
  height: theme.spacing(6.25),
}));

export const StyledTab = styled(Tab)(({ theme }) => ({
  color: theme.palette.neutrals.N190.main,
  [`&.Mui-selected`]: {
    color: theme.palette.common.white,
  },
}));

export const StyledTabs = styled(Tabs)(({ theme }) => ({
  minHeight: theme.spacing(3.625),
  [`& .MuiTabs-flexContainer`]: {
    gap: theme.spacing(6),
  },
  button: {
    minWidth: 'fit-content',
    minHeight: theme.spacing(3.625),
    padding: theme.spacing(0),
  },
}));
