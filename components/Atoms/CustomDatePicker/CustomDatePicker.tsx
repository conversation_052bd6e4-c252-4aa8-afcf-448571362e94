import { FC } from 'react';

import FormControl, { FormControlProps } from '@mui/material/FormControl';
import { DatePicker, DatePickerProps, pickersLayoutClasses } from '@mui/x-date-pickers';
import dayjs, { Dayjs } from 'dayjs';
import isEmpty from 'lodash/isEmpty';
import { StyledLabel } from '../TextField/TextField.styled';
import { CalendarIcon } from '../IconsComponent';
import { customTypography } from '@/theme/customTypography';

export type TCustomDatePicker = {
  formControlProps?: FormControlProps;
  required?: boolean;
} & DatePickerProps<Dayjs>;

export const CustomDatePicker: FC<TCustomDatePicker> = ({ formControlProps, value, label, required, ...props }) => {
  const transformValue = isEmpty(value) ? null : dayjs(value);
  return (
    <FormControl {...formControlProps}>
      {label && (
        <StyledLabel $isRequired={!!required} variant="heading-medium-700">
          {label}
        </StyledLabel>
      )}
      <DatePicker
        value={transformValue}
        format="DD/MM/YYYY"
        sx={[
          {
            background: theme => theme.palette.common.white,
            borderRadius: theme => theme.spacing(1.25),
            outline: 'none',
            height: theme => theme.spacing(42 / 8),
          },
          {
            '.MuiInputBase-root': {
              borderRadius: theme => theme.spacing(1.25),
              ...customTypography['body-xlarge-400'],
            },
            '.MuiInputBase-root input': {
              padding: 0,
              paddingLeft: theme => theme.spacing(1.5),
              paddingRight: theme => theme.spacing(1.5),
              height: theme => theme.spacing(42 / 8),
            },
            '.MuiOutlinedInput-notchedOutline': {
              top: theme => theme.spacing(-5 / 8),
              borderWidth: theme => theme.spacing(1 / 16),
              borderColor: theme => theme.palette.neutrals.N40.main,
            },
            '.MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline': {
              borderWidth: theme => `${theme.spacing(1 / 16)} !important`,
              borderColor: theme => theme.palette.neutrals.N50.main,
            },
            '.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderWidth: theme => `${theme.spacing(1 / 16)} !important`,
              borderColor: theme => theme.palette.neutrals.N50.main,
            },
          },
        ]}
        slotProps={{
          layout: {
            sx: {
              marginTop: theme => theme.spacing(0.5),
              [`.${pickersLayoutClasses.contentWrapper} .Mui-selected`]: {
                background: 'green',
                color: theme => theme.palette.common.white,
              },
            },
          },
        }}
        slots={{
          openPickerIcon: CalendarIcon,
        }}
        {...props}
      />
    </FormControl>
  );
};
