import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import dayjs from 'dayjs';
import { CustomDatePicker } from './CustomDatePicker';

const meta: Meta = {
  title: 'Atoms/CustomDatePicker',
  component: CustomDatePicker,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof CustomDatePicker>;

export const Default: Story = {
  args: { value: dayjs(), onChange: value => console.log(value) },
  render: args => (
    <Box width="fit-content">
      <CustomDatePicker {...args} />
    </Box>
  ),
};
