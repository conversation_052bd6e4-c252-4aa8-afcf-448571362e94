import React, { SVGProps } from 'react';

const SmallTimeIcon = (props: SVGProps<SVGSVGElement>) => (
  // eslint-disable-next-line react/jsx-filename-extension
  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <g clip-path="url(#clip0_4921_57148)">
      <path
        d="M6.5 3.5C6.5 3.22386 6.27614 3 6 3C5.72386 3 5.5 3.22386 5.5 3.5H6.5ZM7.41422 5.35355C7.60949 5.15829 7.60949 4.84171 7.41422 4.64645C7.21896 4.45119 6.90238 4.45119 6.70712 4.64645L7.41422 5.35355ZM1.18765 1.23457C0.972022 1.40707 0.937061 1.72172 1.10957 1.93735C1.28207 2.15298 1.59672 2.18794 1.81235 2.01543L1.18765 1.23457ZM3.06235 1.01543C3.27798 0.84293 3.31294 0.528283 3.14043 0.312652C2.96793 0.0970215 2.65328 0.0620608 2.43765 0.234566L3.06235 1.01543ZM10.1877 2.01543C10.4033 2.18794 10.7179 2.15298 10.8904 1.93735C11.0629 1.72172 11.028 1.40707 10.8123 1.23457L10.1877 2.01543ZM9.56235 0.234566C9.34672 0.0620608 9.03207 0.0970215 8.85957 0.312652C8.68706 0.528283 8.72202 0.84293 8.93765 1.01543L9.56235 0.234566ZM5.5 3.5V6H6.5V3.5H5.5ZM6.70712 4.64645L5.68935 5.66421L6.39646 6.37132L7.41422 5.35355L6.70712 4.64645ZM10.5 6C10.5 8.48528 8.48528 10.5 6 10.5V11.5C9.03757 11.5 11.5 9.03757 11.5 6H10.5ZM6 10.5C3.51472 10.5 1.5 8.48528 1.5 6H0.5C0.5 9.03757 2.96243 11.5 6 11.5V10.5ZM1.5 6C1.5 3.51472 3.51472 1.5 6 1.5V0.5C2.96243 0.5 0.5 2.96243 0.5 6H1.5ZM6 1.5C8.48528 1.5 10.5 3.51472 10.5 6H11.5C11.5 2.96243 9.03757 0.5 6 0.5V1.5ZM1.81235 2.01543L3.06235 1.01543L2.43765 0.234566L1.18765 1.23457L1.81235 2.01543ZM10.8123 1.23457L9.56235 0.234566L8.93765 1.01543L10.1877 2.01543L10.8123 1.23457ZM5.68935 5.66421C5.98848 5.36508 6.49995 5.5769 6.5 5.99993L5.5 6.00007C5.50006 6.46787 6.06567 6.70211 6.39646 6.37132L5.68935 5.66421Z"
        fill="#3C2313"
      />
    </g>
    <defs>
      <clipPath id="clip0_4921_57148">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default SmallTimeIcon;
