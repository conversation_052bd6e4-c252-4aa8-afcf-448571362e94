import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import { DateRangePicker } from './DateRangePicker';

const meta: Meta = {
  title: 'Atoms/DateRangePicker',
  component: DateRangePicker,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof DateRangePicker>;

export const Default: Story = {
  args: { value: new Date(), onChange: value => console.log(value) },
  render: args => (
    <Box width="fit-content">
      <DateRangePicker {...args} />
    </Box>
  ),
};
