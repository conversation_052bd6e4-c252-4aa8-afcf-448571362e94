import styled from '@emotion/styled';
import { buttonClasses, iconButtonClasses } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Button from '../Button';
import Typography from '../Typography';

export const StyledWrapper = styled(Stack)(({ theme }) => ({
  width: 'fit-content',
  flexDirection: 'row',
  justifyContent: 'flex-start',
  alignItems: 'center',
  gap: theme.spacing(1.5),
}));

export const StyledDateRangsPickerContainer = styled(Stack)(({ theme }) => ({
  width: 'fit-content',
  flexDirection: 'row',
  justifyContent: 'flex-start',
  alignItems: 'center',
  backgroundColor: theme.palette.common.white,
  borderRadius: theme.spacing(1.25),
  overflow: 'hidden',
  cursor: 'pointer',
  userSelect: 'none',
}));

export const StyledSwitcherButton = styled(IconButton)(({ theme }) => ({
  [`&.${iconButtonClasses.root}`]: {
    padding: theme.spacing(1.5, 1),
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
}));

export const StyledTodayButton = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    width: 'fit-content',
    height: 'fit-content',
    padding: theme.spacing(1.5),
    color: theme.palette.neutrals.N50.main,
    backgroundColor: theme.palette.common.white,
    borderWidth: theme.spacing(1 / 16),
    borderColor: 'transparent',
    ...theme.typography['body-xlarge-400'],
    '&:hover': {
      borderColor: theme.palette.neutrals.N50.main,
    },
  },
}));

export const StyledDateText = styled(Typography)(({ theme }) => ({
  padding: theme.spacing(1.5, 1),
  color: theme.palette.neutrals.N50.main,
  textTransform: 'capitalize',
  ...theme.typography['body-xlarge-400'],
}));

export const StyledDatePickerWrapper = styled(Stack)(({ theme }) => ({
  width: 'fit-content',
  flexDirection: 'row',
  justifyContent: 'flex-start',
  alignItems: 'center',
  backgroundColor: theme.palette.common.white,
  overflow: 'hidden',
  cursor: 'pointer',
  userSelect: 'none',
  borderRadius: theme.spacing(1.25),
}));
