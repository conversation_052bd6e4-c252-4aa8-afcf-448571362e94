import React, { useCallback, useId, useState } from 'react';
import { StackProps, TypographyProps } from '@mui/material';
import Popover from '@mui/material/Popover';
import dayjs from 'dayjs';
import Calendar from '../Calendar';
import {
  StyledDateRangsPickerContainer,
  StyledDateText,
  StyledSwitcherButton,
  StyledTodayButton,
  StyledWrapper,
} from './DateRangePicker.styled';
import Icon from '../Icon';

export interface IDateRangePickerProps {
  value: Date;
  containerProps?: StackProps;
  dateTextProps?: TypographyProps;
  onChange: (value: Date) => void;
}
export const DateRangePicker: React.FC<IDateRangePickerProps> = ({
  value,
  containerProps,
  dateTextProps,
  onChange,
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [open, setOpen] = useState<boolean>(false);

  const handleClose = () => {
    setAnchorEl(null);
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };

  // TODO: change to using Dayjs
  const handleNextDay = () => {
    const current = new Date(value.getTime());
    onChange(new Date(current.getTime() + 7 * 24 * 60 * 60 * 1000));
  };
  const handlePrevDay = () => {
    const current = new Date(value.getTime());
    onChange(new Date(current.getTime() - 7 * 24 * 60 * 60 * 1000));
  };
  const handleOnChange = useCallback(
    (newvalue: Date) => {
      onChange(newvalue);
      setOpen(false);
    },
    [onChange, setOpen]
  );
  const id = open ? `date-range-picker-popover-${useId}` : undefined;
  const startOfWeek = dayjs(value).startOf('week');
  const endOfWeek = dayjs(value).endOf('week');
  return (
    <>
      <StyledWrapper direction="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
        <StyledTodayButton variant="outlined" size="small" label="Today" onClick={() => onChange(new Date())} />
        <StyledDateRangsPickerContainer aria-describedby={id} {...containerProps}>
          <StyledSwitcherButton
            disableRipple
            onClick={handlePrevDay}
            ref={ref => {
              setAnchorEl(ref);
            }}
          >
            <Icon variant="left_arrow" size={2.625} />
          </StyledSwitcherButton>
          <StyledDateText onClick={handleOpen} {...dateTextProps}>
            {`${startOfWeek.format('DD MMM')} - ${endOfWeek.format('DD MMM, YYYY')}`}
          </StyledDateText>
          <StyledSwitcherButton disableRipple onClick={handleNextDay}>
            <Icon variant="right_arrow" size={2.625} />
          </StyledSwitcherButton>
        </StyledDateRangsPickerContainer>
      </StyledWrapper>
      <Popover
        id={id}
        open={open && Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={theme => ({
          marginTop: theme.spacing(0.625),
        })}
        disableRestoreFocus
      >
        <Calendar value={value} onChange={handleOnChange} />
      </Popover>
    </>
  );
};
