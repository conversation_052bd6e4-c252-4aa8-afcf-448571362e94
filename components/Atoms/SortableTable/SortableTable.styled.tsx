import styled from '@emotion/styled';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';

export const StyledSTTTableCell = styled(TableCell)<{ $hasheaderbackground?: boolean }>(
  ({ theme, $hasheaderbackground }) => ({
    width: theme.spacing(1.75),
    borderLeft: 0,
    padding: theme.spacing(1),
    backgroundColor: $hasheaderbackground ? theme.palette.neutrals.N50.main : theme.palette.common.white,
    verticalAlign: 'middle',
    ...theme.typography['body-medium-400'],
  })
);

export const StyledHeadTableCell = styled(TableCell)<{
  $hideOnMobile?: boolean;
  $hasheaderbackground?: boolean;
  $hasborder?: boolean;
}>(({ theme, $hideOnMobile, $hasheaderbackground }) => ({
  backgroundColor: $hasheaderbackground ? theme.palette.neutrals.N50.main : theme.palette.common.white,
  borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
  color: $hasheaderbackground ? theme.palette.common.white : theme.palette.neutrals.N50.main,
  padding: theme.spacing(1),
  [theme.breakpoints.down('lg')]: {
    display: $hideOnMobile ? 'none' : 'table-cell',
  },
  zIndex: 10,
}));

export const StyledBodyTableCell = styled(TableCell)<{
  $hideOnMobile?: boolean;
  $hasborder?: boolean;
}>(({ theme, $hideOnMobile, $hasborder }) => ({
  border: 'none',
  borderBottom: `1px solid ${theme.palette.neutrals.N40.main}`,
  borderLeft: $hasborder ? `1px solid ${theme.palette.neutrals.N40.main}` : 'none',
  padding: theme.spacing(1),
  verticalAlign: 'middle',
  ...theme.typography['body-xlarge-400'],
  [theme.breakpoints.down('lg')]: {
    display: $hideOnMobile ? 'none' : 'table-cell',
  },
}));

export const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  width: '100%',
  boxShadow: 'unset',
  borderRadius: theme.spacing(1.25),
  WebkitOverflowScrolling: 'unset',
  '&::-webkit-scrollbar': {
    width: '20px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: '#f1f1f1',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: '#888',
    borderRadius: '5px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    backgroundColor: '#555',
  },
  scrollbarColor: '#888 #f1f1f1',
  scrollbarWidth: 'auto',
  overflowX: 'hidden',
}));

export const StyledTableHead = styled(TableHead)<{ $hasborder?: boolean }>(({ theme, $hasborder }) => ({
  th: {
    borderLeft: $hasborder ? `1px solid ${theme.palette.neutrals.N10.main}` : 'none',
    ...theme.typography['heading-xsmall-700'],
  },
}));
