'use client';

import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody, { TableBodyProps } from '@mui/material/TableBody';
import { TableCellProps } from '@mui/material/TableCell';
import { TableContainerProps } from '@mui/material/TableContainer';
import TableRow, { TableRowProps } from '@mui/material/TableRow';
import { visuallyHidden } from '@mui/utils';
import React, { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
  ResponderProvided,
  DraggableProvided,
  DroppableProvided,
  DraggableStateSnapshot,
} from 'react-beautiful-dnd';
import Skeleton from '../Skeleton';
import {
  StyledBodyTableCell,
  StyledHeadTableCell,
  StyledSTTTableCell,
  StyledTableContainer,
  StyledTableHead,
} from './SortableTable.styled';
import CustomTableRow from '../CustomTableRow';
import Typography from '../Typography';
import Icon from '../Icon';

type TLoadingType = 'skeleton' | 'endLine';

export type TColumnSortableTable = {
  name: string;
  label: string | React.ReactNode;
  tableCellProps?: TableCellProps;
  activeSort?: boolean;
  orderBy?: string;
  ordering?: 'asc' | 'desc';
  hideOnMobile?: boolean;
  children?: TColumnSortableTable[];
  rowCollapsiable?: boolean;
  verticalAlign?: 'top' | 'middle' | 'bottom';
  renderCollapseRow?: (row: any, index?: number) => React.ReactNode;
  render?: (row: any, index?: number, onToggle?: (key: string) => void, collapsed?: boolean) => React.ReactNode;
  renderLabel?: (row: any, index?: number) => React.ReactNode;
  onSortClick?: (columnName: string) => void;
};

export type TSortableTableProps = {
  columns: TColumnSortableTable[];
  rows: any[];
  isLoading?: boolean;
  showSTT?: boolean;
  tableContainerProps?: TableContainerProps;
  tableBodyProps?: TableBodyProps;
  tableCellProps?: TableCellProps;
  sttProps?: TableCellProps;
  infinitiLoad?: boolean;
  rowProps?: TableRowProps;
  selectedRowOnClick?: boolean;
  hideTableHead?: boolean;
  loadingType?: TLoadingType;
  hasBorder?: boolean;
  hasHeaderBackground?: boolean;
  groupByColumns?: string[];
  stickyHeader?: boolean;
  tableFooter?: ReactNode;
  page?: number;
  rowCollapsiable?: boolean;
  defaultActiveRowKeys?: string[];
  onLoadMore?: () => void;
  onSort?: (id: string, newPosition: number, beforeId?: string) => void;
  onRowClick?: (row: any) => void;
  onRowHover?: (row: any) => void;
  sortColumnName?: string;
};

export const SortableTable: React.FC<TSortableTableProps> = ({
  columns,
  rows,
  showSTT = true,
  onRowClick,
  tableContainerProps,
  tableBodyProps,
  sttProps,
  isLoading,
  infinitiLoad = false,
  rowProps,
  onRowHover,
  selectedRowOnClick,
  hideTableHead = false,
  loadingType = 'skeleton',
  hasBorder = false,
  hasHeaderBackground = false,
  groupByColumns = [],
  stickyHeader = false,
  tableFooter = null,
  page = 1,
  rowCollapsiable = false,
  defaultActiveRowKeys,
  onLoadMore = () => undefined,
  sortColumnName,
  onSort,
}) => {
  const [observerTarget, setObserverTarget] = useState();
  const [selectedRow, setSelectedRow] = useState<any>();
  const [localRow, setLocalRow] = useState<any[]>([]);
  const [activeRowKeys, setActiveRowKeys] = useState<string[]>([]);

  useEffect(() => {
    if (defaultActiveRowKeys) {
      setActiveRowKeys([...defaultActiveRowKeys]);
    }
  }, [defaultActiveRowKeys]);

  const isSelectedRow = (rowIndx: number) => selectedRowOnClick && selectedRow === rowIndx;

  const refObser = useCallback(
    (node: any) => {
      if (node !== null) {
        setObserverTarget(node);
      }
    },
    [rows]
  );
  useEffect(() => {
    if (infinitiLoad && page > 1) return setLocalRow((pre: any) => [...(pre || []), ...rows]);
    setLocalRow(rows);
  }, [JSON.stringify(rows)]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          onLoadMore();
        }
      },
      { threshold: 1 }
    );
    if (observerTarget) {
      observer.observe(observerTarget);
    }

    return () => {
      if (observerTarget) {
        observer.unobserve(observerTarget);
      }
    };
  }, [observerTarget]);

  const skeletonLoading = useMemo(() => {
    const result = new Array(5).fill(undefined)?.map(_ => (
      <TableRow key={Math.random()}>
        {showSTT && (
          <StyledHeadTableCell key={`${Math.random()}--stt`} component="th" scope="row">
            <Skeleton variant="text" fontSize={2} width={50} height={30} />
          </StyledHeadTableCell>
        )}
        {columns?.map(({ name, children = [] }, index) => {
          if (children.length === 0) {
            return (
              <StyledHeadTableCell key={name} component="th" scope="row">
                <Skeleton variant="text" fontSize={2} width={!showSTT && index === 0 ? 50 : 250} height={30} />
              </StyledHeadTableCell>
            );
          }
          return children.map(({ name }, index) => (
            <StyledHeadTableCell key={name} component="th" scope="row">
              <Skeleton variant="text" fontSize={2} width={!showSTT && index === 0 ? 50 : 250} height={30} />
            </StyledHeadTableCell>
          ));
        })}
      </TableRow>
    ));
    return result;
  }, [columns, isLoading]);

  const skeleton = <Skeleton variant="text" fontSize={2} width={50} height={30} />;
  const checkRow = localRow?.filter(Boolean) || [];

  const hasChildren = columns.findIndex(({ children = [] }) => children.length > 0) > -1;
  const usedGroupByColumns: string[] = [];

  const onToggle = (currKey: string) => {
    setActiveRowKeys(prev => (prev.includes(currKey) ? prev.filter(item => item !== currKey) : [...prev, currKey]));
  };

  const handleDragEnd = (result: DropResult, provided?: ResponderProvided) => {
    if (!result.destination) {
      return;
    }

    if (result.destination.index === result.source.index) {
      return;
    }

    setLocalRow?.(prev => {
      const temp = Array.from(prev);
      const [movedItem] = temp.splice(result.source.index, 1);
      temp.splice(result.destination!.index, 0, movedItem);

      if (typeof onSort === 'function') {
        onSort(movedItem.id, result.destination!.index + 1, temp[result.destination!.index - 1]?.id);
      }

      return temp;
    });
  };

  return (
    <StyledTableContainer component={Paper} {...tableContainerProps}>
      <Table stickyHeader={stickyHeader} aria-label="sticky table">
        {!hideTableHead && (
          <StyledTableHead $hasborder={!isLoading && hasBorder}>
            <TableRow>
              {showSTT && (
                <StyledSTTTableCell
                  key="stt"
                  rowSpan={hasChildren ? 2 : 0}
                  $hasheaderbackground={hasHeaderBackground}
                  {...sttProps}
                >
                  {isLoading ? skeleton : '#'}
                </StyledSTTTableCell>
              )}
              {columns?.map(
                ({
                  name,
                  ordering,
                  label,
                  activeSort,
                  tableCellProps,
                  hideOnMobile,
                  children = [],
                  renderLabel,
                  onSortClick,
                }) => {
                  const labelContent = renderLabel ? renderLabel(label) : label;
                  return (
                    <StyledHeadTableCell
                      key={name}
                      $hideOnMobile={hideOnMobile}
                      colSpan={children.length || 0}
                      rowSpan={children.length === 0 ? 2 : undefined}
                      $hasheaderbackground={hasHeaderBackground}
                      {...tableCellProps}
                    >
                      <Stack
                        direction="row"
                        gap={0.5}
                        alignItems="center"
                        sx={{ cursor: activeSort ? 'pointer' : 'auto' }}
                        onClick={() => {
                          if (activeSort && typeof onSortClick === 'function') {
                            onSortClick(sortColumnName || name);
                          }
                        }}
                      >
                        <Typography component="h5" variant="heading-xsmall-700" textTransform="uppercase">
                          {isLoading ? skeleton : labelContent}
                        </Typography>
                        {activeSort &&
                          (ordering === 'asc' ? (
                            <Icon variant="chevron_down_black" />
                          ) : (
                            <Icon variant="chevron_up_black" />
                          ))}
                      </Stack>
                    </StyledHeadTableCell>
                  );
                }
              )}
            </TableRow>
            {hasChildren && (
              <TableRow>
                {columns?.map(({ children = [] }) => {
                  if (children?.length === 0) {
                    return <></>;
                  }
                  return children?.map(({ name, ordering, label, activeSort, tableCellProps, hideOnMobile }) => (
                    <StyledHeadTableCell
                      key={name}
                      $hideOnMobile={hideOnMobile}
                      $hasheaderbackground={hasHeaderBackground}
                      {...tableCellProps}
                    >
                      <Typography component="h5" variant="heading-xsmall-700" textTransform="uppercase">
                        {isLoading ? skeleton : label}
                      </Typography>
                      {activeSort && (
                        <Box component="span" sx={visuallyHidden}>
                          {ordering === 'desc' ? 'sorted descending' : 'sorted ascending'}
                        </Box>
                      )}
                    </StyledHeadTableCell>
                  ));
                })}
              </TableRow>
            )}
          </StyledTableHead>
        )}

        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="droppable" direction="vertical">
            {(droppableProvided: DroppableProvided) => (
              <TableBody ref={droppableProvided.innerRef} {...droppableProvided.droppableProps} {...tableBodyProps}>
                {loadingType === 'skeleton' && isLoading
                  ? skeletonLoading
                  : checkRow?.map((row, index) => (
                      <Draggable
                        key={`table-rows-${index.toString()}`}
                        draggableId={`table-rows-${index.toString()}`}
                        index={index}
                      >
                        {(draggableProvided: DraggableProvided, snapshot: DraggableStateSnapshot) => (
                          <CustomTableRow
                            key={`table-rows-${index.toString()}`}
                            onClick={() => {
                              if (selectedRowOnClick) {
                                setSelectedRow(index);
                              }
                              if (onRowClick) {
                                onRowClick(row);
                              }
                            }}
                            onMouseOver={() => {
                              if (onRowHover) {
                                onRowHover(row);
                              }
                            }}
                            selected={isSelectedRow(index)}
                            ref={draggableProvided.innerRef}
                            {...draggableProvided.draggableProps}
                            style={{
                              ...draggableProvided.draggableProps.style,
                              background: snapshot.isDragging ? 'rgba(245,245,245, 0.75)' : 'none',
                            }}
                            {...draggableProvided.dragHandleProps}
                          >
                            {showSTT && (
                              <StyledBodyTableCell
                                key={`stt-${index.toString()}`}
                                component="th"
                                scope="row"
                                ref={infinitiLoad && index === checkRow.length - 1 ? refObser : null}
                                {...sttProps}
                                sx={{
                                  borderBottom:
                                    rowCollapsiable && activeRowKeys?.includes(index.toString())
                                      ? 'unset !important'
                                      : 'auto',
                                  paddingBottom:
                                    rowCollapsiable && activeRowKeys?.includes(index.toString())
                                      ? '0px !important'
                                      : 'inherit',
                                }}
                              >
                                {(index + 1).toLocaleString('en-US', {
                                  minimumIntegerDigits: 1,
                                  useGrouping: false,
                                })}
                              </StyledBodyTableCell>
                            )}
                            {columns?.map(
                              (
                                {
                                  name,
                                  render,
                                  tableCellProps,
                                  hideOnMobile,
                                  verticalAlign,
                                  children = [],
                                }: TColumnSortableTable,
                                columnIndex: number
                              ) => {
                                let rowSpan;
                                if (groupByColumns.includes(name) && !usedGroupByColumns.includes(row[name])) {
                                  rowSpan = checkRow.reduce((total, item) => {
                                    if (item?.[name] === row?.[name]) {
                                      return total + 1;
                                    }
                                    return total;
                                  }, 0);
                                  usedGroupByColumns.push(row[name]);
                                }
                                if (!rowSpan && usedGroupByColumns.includes(row[name])) return null;
                                if (children.length === 0) {
                                  return (
                                    <StyledBodyTableCell
                                      key={`${name as string}_${index + 1}_${columnIndex + 1}`}
                                      component="th"
                                      scope="row"
                                      rowSpan={rowSpan}
                                      $hideOnMobile={hideOnMobile}
                                      $hasborder={hasBorder}
                                      {...tableCellProps}
                                      sx={
                                        rowCollapsiable
                                          ? {
                                              borderBottom:
                                                rowCollapsiable && activeRowKeys?.includes(index.toString())
                                                  ? 'none !important'
                                                  : 'inherit',
                                              paddingBottom:
                                                rowCollapsiable && activeRowKeys?.includes(index.toString())
                                                  ? '0px !important'
                                                  : 'inherit',
                                              verticalAlign: verticalAlign ? `${verticalAlign} !important` : 'middle',
                                            }
                                          : tableCellProps?.sx
                                      }
                                    >
                                      {render
                                        ? render(row, index, onToggle, activeRowKeys?.includes(index.toString()))
                                        : row[name]}
                                    </StyledBodyTableCell>
                                  );
                                }
                                return children?.map(
                                  ({
                                    name: childrenName,
                                    render: childrenRender,
                                    tableCellProps: childrenTableCellProps,
                                    hideOnMobile: childrenHideOnMobile,
                                  }: TColumnSortableTable) => (
                                    <StyledBodyTableCell
                                      key={`${name as string}_${index + 1}_${columnIndex + 1}_${childrenName}`}
                                      component="th"
                                      scope="row"
                                      $hideOnMobile={childrenHideOnMobile}
                                      $hasborder={hasBorder}
                                      {...childrenTableCellProps}
                                    >
                                      {childrenRender
                                        ? childrenRender(
                                            row,
                                            index,
                                            onToggle,
                                            activeRowKeys?.includes(index.toString())
                                          )
                                        : row[name][childrenName]}
                                    </StyledBodyTableCell>
                                  )
                                );
                              }
                            )}
                          </CustomTableRow>
                        )}
                      </Draggable>
                    ))}
                {tableFooter}
                {isLoading && loadingType === 'endLine' && (
                  <Stack justifyContent="center" alignItems="center" width="100%" pt="4px">
                    <CircularProgress variant="determinate" />
                  </Stack>
                )}
              </TableBody>
            )}
          </Droppable>
        </DragDropContext>
      </Table>
    </StyledTableContainer>
  );
};
