import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import { Meta, StoryObj } from '@storybook/react';

import Icon, { iconVariantTypes } from './Icon';

const meta: Meta = {
  title: 'Atoms/Icon',
  component: Icon,
  tags: ['autodocs'],
  parameters: {
    controls: {
      exclude: /^(?!variant|size|Enable dynamic color|Dynamic color code).*/g,
    },
  },
  argTypes: {
    variant: {
      options: iconVariantTypes,
      control: { type: 'select' },
    },
    dynamicColor: {
      name: 'Enable dynamic color',
    },
    dynamicColorCode: {
      name: 'Dynamic color code',
      control: { type: 'color' },
    },
    size: {
      control: {
        type: 'range',
        min: 0,
        max: 10,
        step: 1,
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof Icon>;

export const Default: Story = {
  args: {
    variant: 'close',
    size: undefined,
    dynamicColor: false,
  },
  render: (args: any) => (
    <div style={{ color: args?.dynamicColorCode }}>
      <Icon variant={args?.variant} {...args} />
    </div>
  ),
};

export const AllVariant: Story = {
  args: {
    size: undefined,
    dynamicColor: false,
  },
  render: (args: any) => (
    <Stack gap={2}>
      {iconVariantTypes?.map(variant => (
        <Stack key={variant} flexDirection="row" gap={2} alignItems="center">
          <Typography>{`${variant} : `}</Typography>
          <Icon variant={variant} {...args} />
        </Stack>
      ))}
    </Stack>
  ),
};
