import { Fragment, MouseEventHandler } from 'react';
import clsx from 'clsx';

import Image from 'next/image';
import SvgInline from '../SvgInline/SvgInline';

import { IconClasses as classes, StyledSpan, StyledStackBackgroundWrapper } from './Icon.styled';
import { themeSpacingValue } from '@/theme/ThemeRegistry';

// Add icon name here to render icon
export const iconVariantTypes = [
  'availability',
  'camera',
  'check',
  'uncheck',
  'close',
  'sales',
  'services',
  'settings',
  'employee',
  'home',
  'appointment',
  'client',
  'credits',
  'inventory',
  'licence',
  'menu',
  'outlets',
  'package',
  'reports',
  'sales',
  'services',
  'settings',
  'staff',
  'admin',
  'customer',
  'product_service',
  'fb',
  'cashier',
  'survey',
  'setting',
  'rfid',
  'eye',
  'eye-close',
  'print',
  'add_appointment',
  'add_services',
  'add_customer',
  'plus',
  'search',
  'left_arrow',
  'right_arrow',
  'dropdown_arrow',
  'circle_plus',
  'edit',
  'delete',
  'back',
  'chevron_down',
  'warning',
  'green_plus',
  'times',
  'bold_plus',
  'branch',
  'plus-green',
  'reference_code',
  'charge',
  'credit_setting',
  'employee_off_reason',
  'facility',
  'payment_method',
  'print_outline',
  'promotion',
  'reference_code',
  'sell_setting',
  'shortcut',
  'tax',
  'confirm',
  'invoice',
  'circle_checked',
  'completed',
  'view',
  'phone',
  'plus_blue',
  'search_user',
  'arrow_down',
  'clock',
  'bookmark',
  'cancel',
  'started',
  'no_show',
  'done',
  'new',
  'customer_black',
  'profile',
  'red_delete',
  'bold_edit',
  'cashier_product_default',
  'cashier_product_selected',
  'cashier_service_default',
  'cashier_service_selected',
  'cashier_package_default',
  'cashier_package_selected',
  'cashier_fb_default',
  'cashier_fb_selected',
  'plus_round',
  'subtract_round',
  'cart',
  'chevron-left',
  'filter',
  'gray_close',
  'green_checked',
  'printer',
  'percent',
  'dark_plus',
  'dark_minus',
  'download',
  'bell',
  'print_black',
  'back_white',
  'success',
  'color_wheel',
  'white_camera',
  'calendar',
  'location',
  'calendar_black',
  'arrow_left_blue',
  'close_white',
  'clipboard',
  'blue_circle_star',
  'bold_print',
  'white_cart',
  'employee_black',
  'email',
  'star',
  'bold_eye',
  'report_problem',
  'male',
  'female',
  'booking',
  'service_report',
  'membership_report',
  'coupon_report',
  'product_report',
  'user_report',
  'accordion',
  'credit_card',
  'checklist',
  'bar_chart',
  'sale_detail',
  'sale_tax_report',
  'calendar_report',
  'cashier_search',
  'active_cashier_search',
  'star_report',
  'chevron_up_black',
  'chevron_down_black',
  'external',
  'cashier_coupon_default',
  'cashier_coupon_selected',
  'sale_ticket_audit_trail_report',
  'foc_item_report',
  'serial_number_tracking_report',
  'appointment_list_report',
  'no_show_appointment_report',
  'appointment_check_in_out_report',
  'employee_performance_report',
  'new_customer_sale_report',
  'prepaid_service_sale_report',
  'employee_service_detail_report',
  'customer_list_report',
  'customer_membership_report',
  'customer_spending_report',
  'customer_prepaid_detail_report',
  'credit_history_report',
  'customer_credit_report',
  'open_closing_credit_report',
  'treatment_history_report',
  'new_customer_report',
  'user_logon_report',
  'cashier_beverage_default',
  'cashier_beverage_selected',
  'employee_list_report',
  'return_item_report',
  'revenue_conversion_report',
  'voided_sale_report',
  'sale_ranking_report',
  'request',
  'arrived',
  'facial_ipl',
  'request_arrived',
  'white_edit',
  'bold_delete',
  'warning_yellow',
  'black_calendar_report',
  'daily_received_report',
  'daily_received_summary_report',
  'sale_summary_report',
  'credit_purchase_report',
  'credit_consumption_report',
  'calendar_black',
  'cup_of_tea',
  'lock_black',
  'food_and_beverage_report',
  'report_sale_profit',
] as const;

export type IconVariantTypes = (typeof iconVariantTypes)[number];

export interface IconProps {
  /**
   * The variant of the icon.
   */
  variant?: IconVariantTypes;

  /**
   * Background variant of the icon.
   * Defaults to none
   */
  backgroundVariant?: 'primary';

  /**
   * The size of the icon.
   * Used to override the default size of the icon.
   * If not provided, the default size of the icon will be used, according to DLS.
   */
  size?: number | undefined;

  /**
   * Whether the icon should be colored dynamically.
   * If true, the icon will be colored according to the parent component.
   */
  dynamicColor?: boolean;

  /**
   * Opptional class name of the icon.
   */
  className?: React.HTMLAttributes<HTMLDivElement>['className'];

  /**
   * The color to be applied to the icon.
   */
  color?: string;

  /**
   * Optional click handler of the icon
   */
  onClick?: MouseEventHandler<HTMLDivElement>;
}

interface VariantData {
  src: string;
  alt: string;
  width: number;
  height: number;
}

const Icon = ({ variant, backgroundVariant, size, dynamicColor, onClick, ...otherProps }: IconProps) => {
  /**
   * Calculate the size of the icon.
   * If size is not provided, use the default size of the icon
   * @param defaultIconSize
   * @returns
   */
  const calcSize = (defaultIconSize: number = 2) => ({
    width: size ? size * themeSpacingValue : defaultIconSize * themeSpacingValue,
    height: size ? size * themeSpacingValue : defaultIconSize * themeSpacingValue,
  });

  /**
   * Get the icon data based on the variant type
   * @param variantType
   * @returns VariantData
   */
  const getIconData = (variantType: IconVariantTypes): VariantData => {
    // Determine the svg item based on the dynamicColor prop
    // const determineSvgItem = (srcUrl: string) => (dynamicColor ? srcUrl.replace('.svg', '') : srcUrl);

    // Return the icon data based on the variant type
    // Consider switching to switch statement if more variants are added

    if (variantType) {
      return {
        src: `/assets/icons/${variantType}.svg`,
        alt: variantType.replace(/-/g, ''),
        ...calcSize(),
      };
    }
    return {
      src: '',
      alt: '',
      width: 0,
      height: 0,
    };
  };

  // null escape
  if (!variant) {
    console.warn('Icon: Missing variant prop');
    return null;
  }

  const ConditionalSpan = onClick ? StyledSpan : Fragment;

  return (
    <ConditionalSpan {...(onClick && { onClick })}>
      <StyledStackBackgroundWrapper
        className={clsx({
          'util--display-contents': !backgroundVariant,
        })}
        {...(!!backgroundVariant && {
          justifyContent: 'center',
          alignItems: 'center',
          flexShrink: 0,
          className: clsx({
            [classes.class_backgroundVariantPrimary]: backgroundVariant === 'primary',
          }),
        })}
      >
        {dynamicColor ? (
          <SvgInline {...getIconData(variant)} {...otherProps} />
        ) : (
          <Image {...getIconData(variant)} style={{ objectFit: 'contain' }} {...otherProps} />
        )}
      </StyledStackBackgroundWrapper>
    </ConditionalSpan>
  );
};

export default Icon;
