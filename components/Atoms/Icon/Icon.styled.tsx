'use client';

import Stack from '@mui/material/Stack';
import styled from '@emotion/styled';
import { css } from '@emotion/css';
import { alpha } from '@mui/material/styles';

export const IconClasses = {
  class_backgroundVariantPrimary: 'backgroundVariant--primary',
};
const classes = IconClasses;

export const StyledSpan = styled.span`
  display: inline-flex;
`;

export const StyledStackBackgroundWrapper = styled(Stack)`
  ${({ theme }) => css`
    &.${classes.class_backgroundVariantPrimary} {
      width: ${theme.spacing(4)};
      height: ${theme.spacing(4)};
      border-radius: ${theme.spacing(12.5)};
      background-color: ${alpha(theme.palette.neutrals.N80.main, 0.4)};
    }
  `}
`;
