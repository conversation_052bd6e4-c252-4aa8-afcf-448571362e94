import { Theme } from '@mui/material';
import FormControl, { FormControlProps } from '@mui/material/FormControl';
import { menuClasses, MenuProps } from '@mui/material/Menu';
import MenuItem, { menuItemClasses } from '@mui/material/MenuItem';
import OutlinedInput from '@mui/material/OutlinedInput';
import { SelectProps } from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import React, { useCallback, useEffect, useId, useState } from 'react';
import Checkbox from '../Checkbox';
import Icon from '../Icon';
import { ChevronDownIcon } from '../IconsComponent';
import {
  StyledFormHelperText,
  StyledLabel,
  StyledListItemText,
  StyledListSubheader,
  StyledSelect,
} from './Select.styled';
import { ALL_OPTION } from '@/lib/types/entities/utils';
import { TSelectOption } from '@/components/Molecules/RHFItems/RHFSelect/RHFSelect';
import Typography from '@/components/Atoms/Typography';

export type TSelectProps = {
  error?: boolean;
  helperText?: string;
  options?: TSelectOption[];
  formControlProps?: FormControlProps;
  value?: string | string[];
  handleOnChange?: (value?: string | string[]) => void;
  customOnChange?: (value?: string | string[]) => string | string[] | undefined;
  selectProps?: SelectProps;
  showCheckbox?: boolean;
  menuProps?: Omit<MenuProps, 'open'>;
  label?: string;
  showHelperText?: boolean;
  isGrouping?: boolean;
  dropdownIcon?: React.FC;
  showAllOption?: boolean;
  onLoadMore?: () => void;
  page?: number;
  runEffectMultiple?: boolean;
} & SelectProps;

const defaultMenuProps = {
  sx: (theme: Theme) => ({
    marginTop: theme.spacing(0.5),
    maxHeight: 'calc(70% - 96px)',
    [`& .${menuClasses.list}`]: {
      padding: 0,
    },
    [`& .${menuItemClasses.root}`]: {
      justifyContent: 'center',
      padding: theme.spacing(1, 3),
      gap: theme.spacing(1.25),
      ...theme.typography['body-xlarge-400'],
      '&:hover': {
        backgroundColor: theme.palette.neutrals.N150.main,
      },
      [`&.${menuItemClasses.selected}`]: {
        backgroundColor: theme.palette.neutrals.N150.main,
        '&:hover': {
          backgroundColor: theme.palette.neutrals.N150.main,
        },
      },
    },
  }),
};

export const Select: React.FC<TSelectProps> = ({
  options,
  formControlProps,
  value,
  selectProps,
  handleOnChange,
  showCheckbox = false,
  menuProps,
  multiple = false,
  label,
  helperText = '',
  showHelperText = true,
  isGrouping,
  dropdownIcon,
  showAllOption = true,
  onLoadMore,
  page,
  required,
  runEffectMultiple = true,
  customOnChange,
  ...otherProps
}) => {
  const id = `multiple-checkbox-${useId()}`;
  const labelId = `multiple-checkbox-label-${useId()}`;
  const [checked, setChecked] = useState<boolean>(
    options?.every(option => {
      if (typeof value === 'string') return value === option.id;
      return value?.includes(option.id);
    }) || false
  );
  useEffect(() => {
    if (multiple && runEffectMultiple && handleOnChange) {
      handleOnChange(checked ? options?.map(o => o?.id) || [] : []);
    }
  }, [checked]);

  const [observerTarget, setObserverTarget] = useState();

  const [localOptions, setLocalOptions] = useState<TSelectOption[] | undefined>([]);
  const lastOption = localOptions?.at(localOptions.length - 1);

  const refObser = useCallback(
    (node: any) => {
      if (node !== null) {
        setObserverTarget(node);
      }
    },
    [JSON.stringify(options)]
  );

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          if (onLoadMore) onLoadMore();
        }
      },
      { threshold: 1 }
    );
    if (observerTarget) {
      observer.observe(observerTarget);
    }

    return () => {
      if (observerTarget) {
        observer.unobserve(observerTarget);
      }
    };
  }, [observerTarget]);

  useEffect(() => {
    if (onLoadMore && (page || 1) > 1) return setLocalOptions((pre: any) => [...(pre || []), ...(options || [])]);
    setLocalOptions(options);
  }, [JSON.stringify(options)]);

  return (
    <FormControl {...formControlProps} variant="filled" error={!!helperText}>
      {label && (
        <StyledLabel $isRequired={!!required} variant="heading-medium-700">
          {label}
        </StyledLabel>
      )}
      <StyledSelect
        id={id}
        labelId={labelId}
        multiple={multiple}
        input={<OutlinedInput />}
        renderValue={(selected: any) => {
          if (typeof selected === 'string') {
            const findVal = options?.find(o => o.id === selected);
            return findVal?.icon ? (
              <Stack direction="row" gap={1.5} alignItems="center">
                <Icon variant={findVal?.icon} />
                <Typography component="p" variant="body-medium-400">
                  {findVal?.name}
                </Typography>
              </Stack>
            ) : (
              findVal?.name
            );
          }
          return selected
            ?.map((s: string | number) => {
              const findVal = options?.find(o => o?.id === s);
              return findVal?.name;
            })
            ?.filter(Boolean)
            ?.join(', ');
        }}
        value={multiple && !Array.isArray(value) ? [value] : value}
        onChange={e => {
          let valueInput = e.target.value as string | string[];
          if (customOnChange && handleOnChange) {
            const newValueInput = customOnChange(valueInput);
            return handleOnChange?.(newValueInput);
          }
          if (Array.isArray(valueInput) && valueInput.find(item => item === ALL_OPTION['id'])) {
            valueInput = valueInput.filter(item => item !== 'all');
          }
          if (handleOnChange) {
            handleOnChange(valueInput);
          }
        }}
        sx={theme => ({ minWidth: '200px' })}
        MenuProps={{
          ...defaultMenuProps,
          ...menuProps,
        }}
        IconComponent={dropdownIcon || ChevronDownIcon}
        {...selectProps}
        {...otherProps}
      >
        {multiple && showAllOption && (
          <MenuItem
            key={ALL_OPTION['id']}
            value={ALL_OPTION['id']}
            onClick={e => {
              setChecked(!checked);
            }}
          >
            {showCheckbox && (
              <Checkbox
                size="small"
                checked={options?.every(option => value?.includes(option.id)) || false}
                color="success"
              />
            )}
            <StyledListItemText primary={ALL_OPTION['name']} />
          </MenuItem>
        )}
        {(options || []).map(({ name, id: optionValue, parentId, icon, renderLabel }) => {
          const isChecked = Array.isArray(value) ? value?.includes(optionValue) : value === optionValue;
          if (!isGrouping) {
            return (
              <MenuItem
                ref={onLoadMore && optionValue === lastOption?.id ? refObser : null}
                key={optionValue}
                value={optionValue}
                sx={{ width: '100%' }}
              >
                {showCheckbox && multiple && <Checkbox size="small" checked={isChecked} color="success" />}
                {icon && !showCheckbox && <Icon variant={icon} size={2.25} />}
                {renderLabel ? renderLabel() : <StyledListItemText primary={name} />}
                {icon && showCheckbox && <Icon variant={icon} size={2.25} />}
              </MenuItem>
            );
          }
          return parentId ? (
            <MenuItem key={optionValue} value={optionValue} sx={{ width: '100%' }}>
              {showCheckbox && multiple && <Checkbox size="small" checked={isChecked} color="success" />}
              {renderLabel ? renderLabel() : <StyledListItemText primary={name} />}
            </MenuItem>
          ) : (
            <StyledListSubheader> {name}</StyledListSubheader>
          );
        })}
      </StyledSelect>
      {showHelperText && (
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
          {helperText && (
            <>
              <Icon variant="warning" size={1.5} />
              <StyledFormHelperText>{helperText}</StyledFormHelperText>
            </>
          )}
        </Stack>
      )}
    </FormControl>
  );
};
