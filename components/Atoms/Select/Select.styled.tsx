import styled from '@emotion/styled';
import { typographyClasses } from '@mui/material/Typography';
import ListItemText from '@mui/material/ListItemText';
import { outlinedInputClasses } from '@mui/material/OutlinedInput';
import Select, { selectClasses } from '@mui/material/Select';
import FormHelperText, { formHelperTextClasses } from '@mui/material/FormHelperText';
import ListSubheader, { listSubheaderClasses } from '@mui/material/ListSubheader';
import Typography from '../Typography';

export const StyledSelect = styled(Select)(({ theme }) => ({
  [`& .${selectClasses.select}`]: {
    textTransform: 'capitalize',
    minHeight: `${theme.spacing(2.125)} !important`,
    padding: theme.spacing(1.5),
    paddingRight: `${theme.spacing(5.375)} !important`,
    '&:focus': {
      backgroundColor: 'transparent',
    },
  },
  [`&.${outlinedInputClasses.root}`]: {
    padding: 0,
    backgroundColor: theme.palette.common.white,
    borderRadius: theme.spacing(1.25),
    color: theme.palette.neutrals.N50.main,
    ...theme.typography['body-xlarge-400'],
  },

  [`&.${outlinedInputClasses.root} .${outlinedInputClasses.notchedOutline}`]: {
    top: theme.spacing(-5 / 8),
    padding: 0,
    borderWidth: theme.spacing(1 / 16),
    borderColor: theme.palette.neutrals.N180.main,
  },

  [`&.${outlinedInputClasses.root}:hover .${outlinedInputClasses.notchedOutline}`]: {
    borderColor: theme.palette.neutrals.N50.main,
  },

  [`&.${outlinedInputClasses.root}.${outlinedInputClasses.focused} .${outlinedInputClasses.notchedOutline}`]: {
    borderColor: theme.palette.neutrals.N50.main,
  },

  [`&.${outlinedInputClasses.root}.${outlinedInputClasses.error} .${outlinedInputClasses.notchedOutline}`]: {
    borderColor: theme.palette.neutrals.N110.main,
  },

  [`& > svg`]: {
    color: theme.palette.neutrals.N50.main,
    position: 'absolute',
    right: theme.spacing(1.5),
    top: '50%',
    transform: 'translateY(-50%)',
  },
}));

export const StyledListItemText = styled(ListItemText)(({ theme }) => ({
  [`& .${typographyClasses.root}`]: {
    textTransform: 'capitalize',
    color: theme.palette.neutrals.N50.main,
    ...theme.typography['body-xlarge-400'],
  },
}));

export const StyledListSubheader = styled(ListSubheader)(({ theme }) => ({
  [`&.${listSubheaderClasses.root}`]: {
    padding: theme.spacing(0.75, 1.5),
    textTransform: 'capitalize',
    color: theme.palette.neutrals.N50.main,
    ...theme.typography['heading-medium-700'],
  },
}));

export const StyledFormHelperText = styled(FormHelperText)(({ theme }) => ({
  [`&.${formHelperTextClasses.root}`]: {
    padding: 0,
    margin: 0,
    color: theme.palette.neutrals.N110.main,
    ...theme.typography['body-small-400'],
  },
}));

export const StyledLabel = styled(Typography)<{ $isRequired: boolean }>(({ theme, $isRequired }) => ({
  marginBottom: theme.spacing(1),
  color: theme.palette.neutrals.N50.main,
  ...($isRequired
    ? {
        ':before': {
          content: `"*"`,
          color: 'red',
        },
      }
    : {}),
}));
