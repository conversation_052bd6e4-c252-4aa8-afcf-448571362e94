import { ListSubheader, Stack, Theme, typographyClasses } from '@mui/material';
import { Meta, StoryObj } from '@storybook/react';
import Select, { TSelectProps } from '.';
import Typography from '../Typography';

export default {
  title: 'Atoms/Select',
  component: Select,
  tags: ['autodocs'],
} satisfies Meta<TSelectProps>;

type Story = StoryObj<typeof Select>;

export const Default: Story = {
  args: {
    label: 'Employee',
    value: '<PERSON>',
    helperText: 'Invalid',
    options: [
      {
        id: '<PERSON>',
        name: '<PERSON>',
      },
      {
        id: '<PERSON>',
        name: '<PERSON>',
      },
      {
        id: '<PERSON>',
        name: '<PERSON>',
      },
    ],
    formControlProps: {
      error: true,
    },
  },
  render: args => (
    <Stack
      sx={{
        width: '300px',
        height: '300px',
        padding: '20px',
        justifyContent: 'center',
        backgroundColor: '#F2EADC',
      }}
    >
      <Select {...args} />
    </Stack>
  ),
};

export const Multiple: Story = {
  args: {
    label: 'Employee',
    value: ['<PERSON>', '<PERSON>'],
    multiple: true,
    showCheckbox: true,
    helperText: 'invalid',
    options: [
      {
        id: '<PERSON> <PERSON>',
        name: '<PERSON> <PERSON>',
      },
      {
        id: '<PERSON> <PERSON>',
        name: '<PERSON> <PERSON>',
      },
      {
        id: 'April <PERSON>',
        name: 'April <PERSON>',
      },
    ],
  },
  render: args => (
    <Stack
      sx={{
        width: '300px',
        height: '300px',
        padding: '20px',
        justifyContent: 'center',
        backgroundColor: '#F2EADC',
      }}
    >
      <Select {...args} />
    </Stack>
  ),
};

export const Grouping: Story = {
  args: {
    label: 'Employee',
    value: 'Oliver Hansen',
    multiple: false,
    showCheckbox: true,
    helperText: 'invalid',
    isGrouping: true,
    menuProps: {
      sx: {
        display: 'block',
        width: '100%',
      },
    },
    options: [
      {
        id: 'Deep Sleep Head Massage',
        name: 'Deep Sleep Head Massage',
        renderLabel: () => (
          <ListSubheader
            sx={(theme: Theme) => ({
              position: 'relative',
              padding: theme.spacing(1, 3),
              textTransform: 'capitalize',
              color: theme.palette.neutrals.N50.main,
              ...theme.typography['heading-small-700'],
              '&::before': {
                content: "''",
                width: theme.spacing(3 / 8),
                height: theme.spacing(3 / 8),
                position: 'absolute',
                top: '50%',
                left: theme.spacing(1.5),
                transform: 'translateY(-50%)',
                borderRadius: '50%',
                backgroundColor: theme.palette.neutrals.N50.main,
              },
            })}
          >
            Deep Sleep Head Massage
          </ListSubheader>
        ),
      },
      {
        id: 'Onsen + Deep Sleep Head Massage 60 mins',
        name: '',
        renderLabel: () => (
          <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
            <Typography
              variant="body-large-400"
              sx={{
                maxWidth: '80%',
                whiteSpace: 'pre-wrap',
              }}
            >
              Onsen + Deep Sleep Head Massage 60 mins
            </Typography>
            <Typography variant="heading-small-700">$ 300</Typography>
          </Stack>
        ),
        parentId: 'Deep Sleep Head Massage',
      },
      {
        id: 'Onsen + Deep Sleep Head Massage 60 Mins & Foot Massage 60 Mins (4 Hands)',
        name: (
          <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
            <Typography
              variant="body-large-400"
              sx={{
                maxWidth: '80%',
                whiteSpace: 'pre-wrap',
              }}
            >
              Onsen + Deep Sleep Head Massage 60 Mins & Foot Massage 60 Mins (4 Hands)
            </Typography>
            <Typography variant="heading-small-700">$ 300</Typography>
          </Stack>
        ),
        // renderLabel: () => (
        //    <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
        //     <Typography
        //       variant="body-large-400"
        //       sx={{
        //         maxWidth: '80%',
        //         whiteSpace: 'pre-wrap',
        //       }}
        //     >
        //       Onsen + Deep Sleep Head Massage 60 Mins & Foot Massage 60 Mins (4 Hands)
        //     </Typography>
        //     <Typography variant="heading-small-700">$ 300</Typography>
        //   </Stack>
        // ),
        parentId: 'Deep Sleep Head Massage',
      },
      {
        id: 'Onsen + Deep Tissue Massage 90 Mins',
        name: '',
        renderLabel: () => (
          <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
            <Typography
              variant="body-large-400"
              sx={{
                maxWidth: '80%',
                whiteSpace: 'pre-wrap',
              }}
            >
              Onsen + Deep Tissue Massage 90 Mins
            </Typography>
            <Typography variant="heading-small-700">$ 300</Typography>
          </Stack>
        ),
        parentId: 'Deep Sleep Head Massage',
      },
      {
        id: 'Onsen + Deep Tissue Massage 120 Mins',
        name: '',
        renderLabel: () => (
          <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
            <Typography
              variant="body-large-400"
              sx={{
                maxWidth: '80%',
                whiteSpace: 'pre-wrap',
              }}
            >
              Onsen + Deep Tissue Massage 120 Mins
            </Typography>
            <Typography variant="heading-small-700">$ 300</Typography>
          </Stack>
        ),
        parentId: 'Deep Sleep Head Massage',
      },
      {
        id: 'Onsen + Deep Tissue Massage 90 Mins + Herbal Compress 30 mins $ 300',
        name: '',
        renderLabel: () => (
          <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
            <Typography
              variant="body-large-400"
              sx={{
                maxWidth: '80%',
                whiteSpace: 'pre-wrap',
              }}
            >
              Onsen + Deep Tissue Massage 90 Mins + Herbal Compress 30 mins $ 300
            </Typography>
            <Typography variant="heading-small-700">$ 300</Typography>
          </Stack>
        ),
        parentId: 'Deep Sleep Head Massage',
      },
      {
        id: 'Onsen + Deep Tissue Massage 120 Mins + Herbal Compress 30 mins',
        name: '',
        renderLabel: () => (
          <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
            <Typography
              variant="body-large-400"
              sx={{
                maxWidth: '80%',
                whiteSpace: 'pre-wrap',
              }}
            >
              Onsen + Deep Tissue Massage 120 Mins + Herbal Compress 30 mins
            </Typography>
            <Typography variant="heading-small-700">$ 300</Typography>
          </Stack>
        ),
        parentId: 'Deep Sleep Head Massage',
      },
      {
        id: 'Thai Massage',
        name: 'Thai Massage',
        renderLabel: () => (
          <ListSubheader
            sx={(theme: Theme) => ({
              position: 'relative',
              padding: theme.spacing(1, 3),
              textTransform: 'capitalize',
              color: theme.palette.neutrals.N50.main,
              ...theme.typography['heading-small-700'],
              '&::before': {
                content: "''",
                width: theme.spacing(3 / 8),
                height: theme.spacing(3 / 8),
                position: 'absolute',
                top: '50%',
                left: theme.spacing(1.5),
                transform: 'translateY(-50%)',
                borderRadius: '50%',
                backgroundColor: theme.palette.neutrals.N50.main,
              },
            })}
          >
            Thai Massage
          </ListSubheader>
        ),
      },
      {
        id: 'Onsen + Thai Massage 60 mins $ 300',
        name: '',
        renderLabel: () => (
          <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
            <Typography
              variant="body-large-400"
              sx={{
                maxWidth: '80%',
                whiteSpace: 'pre-wrap',
              }}
            >
              Onsen + Thai Massage 60 mins $ 300
            </Typography>
            <Typography variant="heading-small-700">$ 300</Typography>
          </Stack>
        ),
        parentId: 'Thai Massage',
      },
      {
        id: 'Onsen + Thai Massage 120 mins',
        name: '',
        renderLabel: () => (
          <Stack direction="row" justifyContent="space-between" sx={{ width: '100%', padding: '8px: 24px' }}>
            <Typography
              variant="body-large-400"
              sx={{
                maxWidth: '80%',
                whiteSpace: 'pre-wrap',
              }}
            >
              Onsen + Thai Massage 120 mins
            </Typography>
            <Typography variant="heading-small-700">$ 300</Typography>
          </Stack>
        ),
        parentId: 'Thai Massage',
      },
      {
        id: 'SPA',
        name: 'SPA',
        renderLabel: () => (
          <ListSubheader
            sx={(theme: Theme) => ({
              position: 'relative',
              width: '100%',
              padding: theme.spacing(1, 3),
              textTransform: 'capitalize',
              color: theme.palette.neutrals.N50.main,
              ...theme.typography['heading-small-700'],
              '&::before': {
                content: "''",
                width: theme.spacing(3 / 8),
                height: theme.spacing(3 / 8),
                position: 'absolute',
                top: '50%',
                left: theme.spacing(1.5),
                transform: 'translateY(-50%)',
                borderRadius: '50%',
                backgroundColor: theme.palette.neutrals.N50.main,
              },
            })}
          >
            SPA
          </ListSubheader>
        ),
      },
    ],
  },
  render: args => (
    <Stack
      sx={{
        width: '300px',
        height: '300px',
        padding: '20px',
        justifyContent: 'center',
        backgroundColor: '#F2EADC',
      }}
    >
      <Select {...args} />
    </Stack>
  ),
};
