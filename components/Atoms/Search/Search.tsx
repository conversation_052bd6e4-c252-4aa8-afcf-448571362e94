import { AutocompleteProps, SxProps, Theme } from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import TextField, { TextFieldProps } from '@mui/material/TextField';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import Icon from '../Icon';
import { StyledAutocomplete, StyledPopper } from './Search.styled';

export interface IOptionItem {
  label: string;
  value: string;
}

export type ISearchProps = {
  options?: IOptionItem[];
  noOptionsText?: string;
  placeholder: string;
  isSearching?: boolean;
  isOpenPopupSearch?: boolean;
  textFieldProps?: TextFieldProps;
  onSearch: (keyword: string) => void;
  onSelect?: (value: string) => void;
  sx?: ((theme: Theme) => object) | object;
};

export const Search: React.FC<ISearchProps> = ({
  options = [],
  noOptionsText = '',
  placeholder = '',
  isSearching = false,
  textFieldProps,
  isOpenPopupSearch = true,
  onSearch,
  onSelect,
  sx,
}) => {
  const [inputValue, setInputValue] = useState<string>('');

  const debounceSearch = _.debounce(onSearch, 300);

  useEffect(() => {
    debounceSearch(inputValue);
  }, [inputValue]);
  return (
    <StyledAutocomplete
      sx={theme => ({ minWidth: '300px', ...(typeof sx === 'function' ? sx?.(theme) : sx) })}
      freeSolo
      open={isOpenPopupSearch}
      disableClearable
      autoComplete
      includeInputInList
      filterSelectedOptions
      options={options}
      filterOptions={x => x}
      noOptionsText={noOptionsText}
      loading={isSearching}
      PopperComponent={StyledPopper}
      getOptionLabel={(option: unknown) => {
        if (!option || typeof option !== 'object') {
          return '';
        }
        const resultItem = option as IOptionItem;
        if (typeof resultItem.label !== 'string') {
          return '';
        }
        return resultItem.label;
      }}
      onInputChange={(_, value) => {
        setInputValue(value);
      }}
      onChange={(_, value) => {
        if (typeof value === 'string') {
          if (onSelect) {
            onSelect(value);
          }
        }
      }}
      renderInput={params => (
        <TextField
          {...params}
          placeholder={placeholder}
          variant="outlined"
          InputLabelProps={{ shrink: true }}
          InputProps={{
            ...params.InputProps,
            type: 'search',
            startAdornment: (
              <>
                <Icon variant="search" />
                {params.InputProps.startAdornment}
              </>
            ),
            endAdornment: (
              <>
                {isSearching ? <CircularProgress color="inherit" size={15} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
          {...textFieldProps}
        />
      )}
    />
  );
};
