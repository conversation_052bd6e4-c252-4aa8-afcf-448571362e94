import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import Search from '.';

const meta: Meta = {
  title: 'Atoms/Search',
  component: Search,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof Search>;

const SIMPLE_DATA = [
  { label: 'The Shawshank Redemption', value: '1994' },
  { label: 'The Godfather', value: '1972' },
  { label: 'The Godfather: Part II', value: '1974' },
  { label: 'The Dark Knight', value: '2008' },
  { label: '12 Angry Men', value: '1957' },
  { label: "Schindler's List", value: '1993' },
  { label: 'Pulp Fiction', value: '1994' },
];

export const Default: Story = {
  args: {
    options: SIMPLE_DATA,
    noOptionsText: 'No branches',
    placeholder: 'Search branch',
    onSearch: (keyword: string) => {
      console.log(keyword, 'onSearch');
    },
    onSelect: (newValue: string) => {
      console.log(newValue, 'onSelect');
    },
  },
  render: args => (
    <Box
      width="100%"
      p={2}
      sx={{
        backgroundColor: '#EADFCE',
      }}
    >
      <Search {...args} />
    </Box>
  ),
};
