import styled from '@emotion/styled';
import Autocomplete, { autocompleteClasses } from '@mui/material/Autocomplete';
import Popper from '@mui/material/Popper';
import { outlinedInputClasses } from '@mui/material/OutlinedInput';

export const StyledAutocomplete = styled(Autocomplete)(({ theme }) => ({
  [`&.${autocompleteClasses.root} .${outlinedInputClasses.root}`]: {
    padding: theme.spacing(1.5),
  },

  [`&.${autocompleteClasses.root} .${autocompleteClasses.inputRoot}`]: {
    gap: theme.spacing(1.25),
  },

  [`&.${autocompleteClasses.root} .${outlinedInputClasses.root} .${autocompleteClasses.input}`]: {
    height: theme.spacing(2.25),
    padding: 0,
    ...theme.typography['body-xlarge-400'],
    color: theme.palette.neutrals.N50.main,
    '&::placeholder': {
      opacity: 1,
      color: theme.palette.neutrals.N50.main,
    },
  },

  [`& .${outlinedInputClasses.root}`]: {
    border: `${theme.spacing(1 / 16)} solid transparent`,
    borderRadius: theme.spacing(1.25),
    backgroundColor: theme.palette.common.white,
  },

  [`& .${outlinedInputClasses.root}.${outlinedInputClasses.focused} .${outlinedInputClasses.notchedOutline}`]: {
    border: 'none',
  },

  [`& .${outlinedInputClasses.notchedOutline}`]: {
    display: 'none',
  },
}));

export const StyledPopper = styled(Popper)(({ theme }) => ({
  [`& .${autocompleteClasses.paper}`]: {
    marginTop: theme.spacing(1),
    borderRadius: theme.spacing(1.25),
  },
  [`& .${autocompleteClasses.listbox}`]: {
    boxSizing: 'border-box',
    '& ul': {
      padding: 0,
      margin: 0,
    },
    '& li': {
      color: theme.palette.neutrals.N50.main,
      ...theme.typography['body-xlarge-400'],
    },
  },
}));
