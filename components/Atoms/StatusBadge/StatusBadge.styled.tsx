import styled from '@emotion/styled';
import Typography from '../Typography';

export const StyledStatusBadge = styled(Typography)<{ backgroundColor?: string }>(({ theme, backgroundColor }) => ({
  width: 'fit-content',
  minWidth: theme.spacing(14),
  height: 'fit-content',
  textAlign: 'center',
  textTransform: 'capitalize',
  padding: theme.spacing(0.25, 1.5),
  borderRadius: theme.spacing(0.625),
  color: theme.palette.common.white,
  backgroundColor: backgroundColor || theme.palette.success?.main,
  ...theme.typography['heading-medium-700'],
}));
