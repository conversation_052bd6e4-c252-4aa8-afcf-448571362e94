import { TypographyProps } from '@mui/material/Typography';
import { APPOINTMENT_STATUS_COLORS, INVOICE_STATUS_COLORS, ORDER_STATUS_COLORS } from '@/lib/constants/status';
import { EAppointmentStatus } from '@/lib/types/enum/appointment';
import { EInvoiceStatus } from '@/lib/types/enum/invoice';
import { EOrderStatus } from '@/lib/types/enum/order';
import { StyledStatusBadge } from './StatusBadge.styled';

export interface IStatusBadgeProps {
  status: EInvoiceStatus | EAppointmentStatus | EOrderStatus;
  typographyProps?: TypographyProps;
}

const STATUS_COLORS = {
  ...INVOICE_STATUS_COLORS,
  ...APPOINTMENT_STATUS_COLORS,
  ...ORDER_STATUS_COLORS,
};

export const StatusBadge: React.FC<IStatusBadgeProps> = ({ status, typographyProps }) => (
  <StyledStatusBadge backgroundColor={STATUS_COLORS?.[status]?.['color']} {...typographyProps}>
    {STATUS_COLORS?.[status]?.['label'] || ''}
  </StyledStatusBadge>
);
