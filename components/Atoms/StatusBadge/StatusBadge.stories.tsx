import { Meta, StoryObj } from '@storybook/react';
import { EInvoiceStatus } from '@/lib/types/enum/invoice';
import StatusBadge, { IStatusBadgeProps } from '.';

export default {
  title: 'Atoms/StatusBadge',
  component: StatusBadge,
  tags: ['autodocs'],
} satisfies Meta<IStatusBadgeProps>;

type Story = StoryObj<typeof StatusBadge>;

export const Default: Story = {
  args: { status: EInvoiceStatus['PAID'] },
  render: args => <StatusBadge {...args} />,
};
