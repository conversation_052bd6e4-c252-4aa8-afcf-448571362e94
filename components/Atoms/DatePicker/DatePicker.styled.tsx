import styled from '@emotion/styled';
import { buttonClasses } from '@mui/material/Button';
import IconButton, { iconButtonClasses } from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Button from '../Button';

export const StyledWrapper = styled(Stack)(({ theme }) => ({
  width: 'fit-content',
  flexDirection: 'row',
  justifyContent: 'flex-start',
  alignItems: 'center',
  gap: theme.spacing(1.5),
}));

export const StyledDatePickerWrapper = styled(Stack)(({ theme }) => ({
  width: 'fit-content',
  minWidth: theme.spacing(272 / 8),
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  backgroundColor: theme.palette.common.white,
  overflow: 'hidden',
  cursor: 'pointer',
  userSelect: 'none',
  borderRadius: theme.spacing(1.25),
}));

export const StyledSwitcherButton = styled(IconButton)(({ theme }) => ({
  [`&.${iconButtonClasses.root}`]: {
    padding: theme.spacing(1.5, 1),
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
}));

export const StyledDateText = styled(Stack)(({ theme }) => ({
  width: 'fit-content',
  justifyContent: 'flex-start',
  alignItems: 'center',
  flexDirection: 'row',
  gap: theme.spacing(1.25),
  padding: theme.spacing(1.5, 1),
  color: theme.palette.neutrals.N50.main,
  textTransform: 'capitalize',
  whiteSpace: 'nowrap',
}));

export const StyledTodayButton = styled(Button)(({ theme }) => ({
  [`&.${buttonClasses.root}`]: {
    width: 'fit-content',
    height: 'fit-content',
    padding: theme.spacing(1.5),
    color: theme.palette.neutrals.N50.main,
    backgroundColor: theme.palette.common.white,
    borderWidth: theme.spacing(1 / 16),
    borderColor: 'transparent',
    borderRadius: theme.spacing(1.25),
    overflow: 'hidden',
    ...theme.typography['body-xlarge-400'],
    '&:hover': {
      borderColor: theme.palette.neutrals.N50.main,
    },
  },
}));
