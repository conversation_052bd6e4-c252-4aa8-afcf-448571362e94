import Box from '@mui/material/Box';
import { Meta, StoryObj } from '@storybook/react';
import DatePicker from '.';

const meta: Meta = {
  title: 'Atoms/DatePicker',
  component: DatePicker,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof DatePicker>;

export const Default: Story = {
  args: {},
  render: args => (
    <Box width="fit-content">
      <DatePicker {...args} />
    </Box>
  ),
};
