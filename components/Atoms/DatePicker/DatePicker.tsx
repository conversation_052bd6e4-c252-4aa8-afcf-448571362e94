import React, { useCallback, useId, useState } from 'react';
import { StackProps } from '@mui/material/Stack';
import Popover from '@mui/material/Popover';
import dayjs from 'dayjs';
import Calendar from '../Calendar';
import {
  StyledDatePickerWrapper,
  StyledDateText,
  StyledSwitcherButton,
  StyledTodayButton,
  StyledWrapper,
} from './DatePicker.styled';
import Icon from '../Icon';
import Typography from '../Typography';

export interface IDatePickerProps {
  value: Date;
  containerProps?: StackProps;
  dateTextProps?: StackProps;
  onChange: (value: Date) => void;
  showTodayButton?: boolean;
}
export const DatePicker: React.FC<IDatePickerProps> = ({
  value,
  containerProps,
  dateTextProps,
  onChange,
  showTodayButton = true,
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [open, setOpen] = useState<boolean>(false);
  const uniqueId = useId();
  const handleClose = () => {
    setAnchorEl(null);
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };

  // TODO: change to using Dayjs
  const handleNextDay = () => {
    const current = new Date(value.getTime());
    onChange(new Date(current.getTime() + 24 * 60 * 60 * 1000));
  };
  const handlePrevDay = () => {
    const current = new Date(value.getTime());
    onChange(new Date(current.getTime() - 24 * 60 * 60 * 1000));
  };
  const handleOnChange = useCallback(
    (newvalue: Date) => {
      onChange(newvalue);
      setOpen(false);
    },
    [onChange, setOpen]
  );
  const id = open ? `date-picker-popover-${uniqueId}` : undefined;
  return (
    <>
      <StyledWrapper direction="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
        {showTodayButton && (
          <StyledTodayButton variant="outlined" size="small" label="Today" onClick={() => onChange(new Date())} />
        )}
        <StyledDatePickerWrapper aria-describedby={id} {...containerProps}>
          <StyledSwitcherButton
            disableRipple
            onClick={handlePrevDay}
            ref={ref => {
              setAnchorEl(ref);
            }}
          >
            <Icon variant="left_arrow" size={2.625} />
          </StyledSwitcherButton>
          <StyledDateText onClick={handleOpen} {...dateTextProps}>
            <Typography variant="heading-medium-700">{dayjs(value).format('dddd')}</Typography>
            <Typography variant="body-xlarge-400">{dayjs(value).format('DD MMM, YYYY')}</Typography>
          </StyledDateText>
          <StyledSwitcherButton disableRipple onClick={handleNextDay}>
            <Icon variant="right_arrow" size={2.625} />
          </StyledSwitcherButton>
        </StyledDatePickerWrapper>
      </StyledWrapper>
      <Popover
        id={id}
        open={open && Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={theme => ({
          marginTop: theme.spacing(0.625),
        })}
        disableRestoreFocus
      >
        <Calendar value={value} onChange={handleOnChange} />
      </Popover>
    </>
  );
};
