import { Meta, StoryObj } from '@storybook/react';
import { customTypography } from '@/theme/customTypography';
import ReportTable, { TReportTableProps } from '.';
import { TColumnTableReport } from './ReportTable';

export default {
  title: 'Atoms/ReportTable',
  component: ReportTable,
  tags: ['autodocs'],
} satisfies Meta<TReportTableProps<any>>;

type Story = StoryObj<typeof ReportTable<any>>;

const COLUMN_SAMPLE: TColumnTableReport[] = [
  {
    name: 'item',
    label: 'Item',
    tableCellProps: { align: 'left', sx: { fontWeight: customTypography['fontWeightBold'] } },
  },
  { name: 'qty', label: 'QTY', tableCellProps: { align: 'left' } },
  { name: 'total', label: 'TOTAL (sgd)', tableCellProps: { align: 'right' } },
];

const SAMPLE_DATA = [
  { item: 'Small white towel (16x32)', qty: '6', total: '12.00' },
  { item: 'Bath white towel (27x54)', qty: '6', total: '12.00' },
  { item: 'Yukata Set', qty: '6', total: '12.00' },
  { item: 'Toothbrush Set (M)', qty: '6', total: '12.00' },
  { item: 'Shaver Set (M)', qty: '6', total: '12.00' },
  { item: 'Small white towel (16x32)', qty: '6', total: '12.00' },
];

export const Default: Story = {
  args: { columns: COLUMN_SAMPLE, rows: SAMPLE_DATA, showSTT: false },
  render: args => (
    <div style={{ width: '612px' }}>
      <ReportTable {...args} />
    </div>
  ),
};

const COLUMN_GROUPING_SAMPLE: TColumnTableReport<any>[] = [
  {
    name: 'time',
    label: '',
  },
  {
    name: 'qty',
    label: 'QTY',
    children: [
      {
        name: 'opening',
        label: 'Opening',
        tableCellProps: { align: 'center' },
      },
      {
        name: 'purchase',
        label: 'Purchase',
        tableCellProps: { align: 'center' },
      },
      {
        name: 'use',
        label: 'Use',
        tableCellProps: { align: 'center' },
      },
      {
        name: 'closing',
        label: 'Closing',
        tableCellProps: { align: 'center' },
      },
    ],
    tableCellProps: { align: 'center' },
  },
  {
    name: 'value',
    label: 'Value',
    children: [
      {
        name: 'opening',
        label: 'Opening',
        tableCellProps: { align: 'center' },
      },
      {
        name: 'purchase',
        label: 'Purchase',
        tableCellProps: { align: 'center' },
      },
      {
        name: 'use',
        label: 'Use',
        tableCellProps: { align: 'center' },
      },
      {
        name: 'closing',
        label: 'Closing',
        tableCellProps: { align: 'center' },
      },
    ],
    tableCellProps: { align: 'center' },
  },
];

const COLUMN_GROUPING_SAMPLE_DATA = [
  {
    time: 'January 2023',
    qty: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
    value: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
  },
  {
    time: 'February 2023',
    qty: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
    value: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
  },
  {
    time: 'March 2023',
    qty: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
    value: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
  },
  {
    time: 'April 2023',
    qty: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
    value: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
  },
  {
    time: 'May 2023',
    qty: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
    value: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
  },
  {
    time: 'June 2023',
    qty: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
    value: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
  },
  {
    time: 'July 2023',
    qty: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
    value: {
      opening: '1628471.68',
      purchase: '1628471.68',
      use: '1628471.68',
      closing: '1628471.68',
    },
  },
];

export const ColumnGrouping: Story = {
  args: {
    columns: COLUMN_GROUPING_SAMPLE,
    rows: COLUMN_GROUPING_SAMPLE_DATA,
    showSTT: false,
    hasBorder: true,
    hasHeaderBackground: true,
  },
  render: args => <ReportTable {...args} />,
};
