'use client';

import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody, { TableBodyProps } from '@mui/material/TableBody';
import { TableCellProps } from '@mui/material/TableCell';
import { TableContainerProps } from '@mui/material/TableContainer';
import TableRow, { TableRowProps } from '@mui/material/TableRow';
import { visuallyHidden } from '@mui/utils';
import React, { Fragment, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import Skeleton from '../Skeleton';
import {
  StyledBodyTableCell,
  StyledHeadTableCell,
  StyledSTTTableCell,
  StyledTableContainer,
  StyledTableHead,
} from './ReportTable.styled';

import CustomTableRow from '../CustomTableRow';
import Typography from '../Typography';

type TLoadingType = 'skeleton' | 'endLine';

export type TColumnTableReport<T = Record<string, string>> = {
  name: keyof T | 'actions';
  label: string | React.ReactNode;
  tableCellProps?: TableCellProps;
  activeSort?: boolean;
  orderBy?: string;
  ordering?: 'asc' | 'desc';
  hideOnMobile?: boolean;
  hasBorder?: (row: T) => boolean;
  hasBackground?: (row: T) => boolean;
  children?: TColumnTableReport<T>[];
  render?: (row: T, index?: number) => React.ReactNode;
  renderLabel?: (row: any, index?: number) => React.ReactNode;
  onSortClick?: (columnName: string) => void;
};

export type TReportTableProps<T = any> = {
  columns: TColumnTableReport<T>[];
  tableName?: string;
  rows: T[];
  isLoading?: boolean;
  showSTT?: boolean;
  tableContainerProps?: TableContainerProps;
  tableBodyProps?: TableBodyProps;
  tableCellProps?: TableCellProps;
  sttProps?: TableCellProps;
  infinitiLoad?: boolean;
  rowProps?: TableRowProps;
  selectedRowOnClick?: boolean;
  hideTableHead?: boolean;
  loadingType?: TLoadingType;
  hasBorder?: boolean;
  hasHeaderBackground?: boolean;
  groupByColumns?: string[];
  stickyHeader?: boolean;
  tableFooter?: ReactNode;
  onLoadMore?: () => void;
  onRowClick?: (row: T) => void;
  onRowHover?: (row: T) => void;
  page?: number;
};

// eslint-disable-next-line react/function-component-definition
export function ReportTable<T>({
  columns,
  rows,
  showSTT = true,
  onRowClick,
  tableContainerProps,
  tableBodyProps,
  sttProps,
  isLoading,
  infinitiLoad = false,
  rowProps,
  onRowHover,
  selectedRowOnClick,
  hideTableHead = false,
  loadingType = 'skeleton',
  hasBorder = false,
  hasHeaderBackground = false,
  groupByColumns = [],
  stickyHeader = false,
  tableFooter = null,
  onLoadMore = () => undefined,
  page = 1,
}: TReportTableProps<T>) {
  const [observerTarget, setObserverTarget] = useState();
  const [selectedRow, setSelectedRow] = useState<any>();
  const [localRow, setLocalRow] = useState<any[]>([]);

  const isSelectedRow = (rowIndx: number) => selectedRowOnClick && selectedRow === rowIndx;

  const refObser = useCallback(
    (node: any) => {
      if (node !== null) {
        setObserverTarget(node);
      }
    },
    [rows]
  );
  useEffect(() => {
    if (infinitiLoad && page > 1) return setLocalRow((pre: any) => [...(pre || []), ...rows]);
    setLocalRow(rows);
  }, [JSON.stringify(rows)]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          onLoadMore();
        }
      },
      {
        threshold: 0.75,
      }
    );
    if (observerTarget) {
      observer.observe(observerTarget);
    }

    return () => {
      if (observerTarget) {
        observer.unobserve(observerTarget);
      }
    };
  }, [observerTarget]);

  const skeletonLoading = useMemo(() => {
    const result = new Array(5).fill(undefined)?.map(_ => (
      <TableRow key={Math.random()}>
        {showSTT && (
          <StyledHeadTableCell key={`${Math.random()}--stt`} component="th" scope="row">
            <Skeleton variant="text" fontSize={2} width={50} height={30} />
          </StyledHeadTableCell>
        )}
        {columns?.map(({ name, children = [] }, index) => {
          if (children.length === 0) {
            return (
              <StyledHeadTableCell key={`${name as string}_${index + 1}`} component="th" scope="row">
                <Skeleton variant="text" fontSize={2} width={!showSTT && index === 0 ? 50 : 250} height={30} />
              </StyledHeadTableCell>
            );
          }
          return children.map(({ name }, childIndex) => (
            <StyledHeadTableCell key={`${name as string}_${index + 1}_${childIndex + 1}`} component="th" scope="row">
              <Skeleton variant="text" fontSize={2} width={!showSTT && childIndex === 0 ? 50 : 250} height={30} />
            </StyledHeadTableCell>
          ));
        })}
      </TableRow>
    ));
    return result;
  }, [columns, isLoading]);

  const skeleton = <Skeleton variant="text" fontSize={2} width={50} height={30} />;
  const checkRow = localRow?.filter(Boolean) || [];
  const hasChildren = columns.findIndex(({ children = [] }) => children.length > 0) > -1;

  return (
    <>
      <StyledTableContainer component={Paper} {...tableContainerProps}>
        <Table stickyHeader={stickyHeader} aria-label="sticky table">
          {!hideTableHead && (
            <StyledTableHead
              $hasborder={!isLoading && hasBorder}
              sx={
                stickyHeader
                  ? {
                      position: 'sticky',
                      top: 0,
                      zIndex: 1,
                    }
                  : {}
              }
            >
              <TableRow>
                {showSTT && (
                  <StyledSTTTableCell
                    key="stt"
                    rowSpan={hasChildren ? 2 : 0}
                    $hasheaderbackground={hasHeaderBackground}
                    {...sttProps}
                  >
                    {isLoading ? skeleton : '#'}
                  </StyledSTTTableCell>
                )}
                {columns?.map(
                  (
                    { name, ordering, label, activeSort, tableCellProps, hideOnMobile, children = [], renderLabel },
                    index
                  ) => {
                    const labelContent = renderLabel ? renderLabel(label) : label;
                    return (
                      <StyledHeadTableCell
                        key={`${name as string}_${index + 1}`}
                        $hideOnMobile={hideOnMobile}
                        colSpan={children.length || 0}
                        rowSpan={children.length === 0 ? 2 : undefined}
                        $hasheaderbackground={hasHeaderBackground}
                        {...tableCellProps}
                      >
                        {/* Header */}
                        <Typography component="h5" variant="heading-xxsmall-700" textTransform="uppercase">
                          {isLoading ? skeleton : labelContent}
                        </Typography>
                        {activeSort && (
                          <Box component="span" sx={visuallyHidden}>
                            {ordering === 'desc' ? 'sorted descending' : 'sorted ascending'}
                          </Box>
                        )}
                      </StyledHeadTableCell>
                    );
                  }
                )}
              </TableRow>
              {hasChildren && (
                <TableRow>
                  {columns?.map(({ children = [] }, index) => {
                    if (children?.length === 0) {
                      return <></>;
                    }
                    return children?.map(
                      ({ name, ordering, label, activeSort, tableCellProps, hideOnMobile }, childIndex) => (
                        <StyledHeadTableCell
                          key={`${name as string}_${index + 1}_${childIndex + 1}`}
                          $hideOnMobile={hideOnMobile}
                          $hasheaderbackground={hasHeaderBackground}
                          {...tableCellProps}
                        >
                          {/* header group */}
                          <Typography component="h5" variant="heading-xxsmall-700" textTransform="uppercase">
                            {isLoading ? skeleton : label}
                          </Typography>
                          {activeSort && (
                            <Box component="span" sx={visuallyHidden}>
                              {ordering === 'desc' ? 'sorted descending' : 'sorted ascending'}
                            </Box>
                          )}
                        </StyledHeadTableCell>
                      )
                    );
                  })}
                </TableRow>
              )}
            </StyledTableHead>
          )}
          <TableBody {...tableBodyProps}>
            {loadingType === 'skeleton' && isLoading
              ? skeletonLoading
              : checkRow?.map((row, index) => (
                  <Fragment key={`table-rows-container-${index.toString()}`}>
                    <CustomTableRow
                      key={`table-rows-${index.toString()}`}
                      onClick={() => {
                        if (selectedRowOnClick) {
                          setSelectedRow(index);
                        }
                        if (onRowClick) {
                          onRowClick(row);
                        }
                      }}
                      onMouseOver={() => {
                        if (onRowHover) {
                          onRowHover(row);
                        }
                      }}
                      ref={infinitiLoad && index === checkRow.length - 1 ? refObser : null}
                      selected={isSelectedRow(index)}
                      {...rowProps}
                    >
                      {showSTT && (
                        <StyledBodyTableCell key={`stt-${index.toString()}`} component="th" scope="row" {...sttProps}>
                          {(index + 1).toLocaleString('en-US', {
                            minimumIntegerDigits: 1,
                            useGrouping: false,
                          })}
                        </StyledBodyTableCell>
                      )}
                      {columns?.map(
                        (
                          {
                            name,
                            render,
                            tableCellProps,
                            hideOnMobile,
                            children = [],
                            hasBorder: hasBorderRow,
                            hasBackground: hasBackgroundRow,
                          }: TColumnTableReport<T>,
                          columnIndex: number
                        ) => {
                          let rowSpan = 1;
                          let shouldRender = true;

                          if (groupByColumns.includes(name as string)) {
                            const currentColIndex = groupByColumns.indexOf(name as string);

                            // Get all columns up to and including current one
                            const columnsToCheck = groupByColumns.slice(0, currentColIndex + 1);

                            // Check if this is the first occurrence of this combination
                            const isFirstOccurrence =
                              index === 0 || !columnsToCheck.every(col => checkRow[index - 1][col] === row[col]);

                            if (isFirstOccurrence) {
                              // Count how many subsequent rows share the same values
                              let count = 1;
                              for (let i = index + 1; i < checkRow.length; i += 1) {
                                if (columnsToCheck.every(col => checkRow[i][col] === row[col])) {
                                  count += 1;
                                } else {
                                  break;
                                }
                              }
                              rowSpan = count;
                            } else {
                              shouldRender = false;
                            }
                          }

                          if (!shouldRender) {
                            return null;
                          }

                          if (children.length === 0) {
                            return (
                              <StyledBodyTableCell
                                key={`${name as string}_${index + 1}_${columnIndex + 1}`}
                                component="th"
                                scope="row"
                                rowSpan={rowSpan}
                                $hideOnMobile={hideOnMobile}
                                $hasborder={typeof hasBorderRow === 'function' ? hasBorderRow(row) : hasBorder}
                                $hasBackground={typeof hasBackgroundRow === 'function' ? hasBackgroundRow(row) : false}
                                {...tableCellProps}
                              >
                                {render ? render(row, index) : row?.[name]}
                              </StyledBodyTableCell>
                            );
                          }
                          return children?.map(
                            (
                              {
                                name: childrenName,
                                render: childrenRender,
                                tableCellProps: childrenTableCellProps,
                                hideOnMobile: childrenHideOnMobile,
                              }: TColumnTableReport<T>,
                              index: number
                            ) => (
                              <StyledBodyTableCell
                                key={`${name as string}_${index + 1}_${columnIndex + 1}_${childrenName as string}`}
                                component="th"
                                scope="row"
                                $hideOnMobile={childrenHideOnMobile}
                                $hasborder={hasBorder}
                                {...childrenTableCellProps}
                              >
                                {childrenRender ? childrenRender(row, index) : row?.[name]?.[childrenName]}
                              </StyledBodyTableCell>
                            )
                          );
                        }
                      )}
                    </CustomTableRow>
                  </Fragment>
                ))}
            {tableFooter}
            {isLoading && loadingType === 'endLine' && (
              <Stack justifyContent="center" alignItems="center" width="100%" pt="4px">
                <CircularProgress variant="determinate" />
              </Stack>
            )}
          </TableBody>
        </Table>
      </StyledTableContainer>
    </>
  );
}

// export const ReportTable: React.FC<TReportTableProps<T>> = ({
//   columns,
//   rows,
//   showSTT = true,
//   onRowClick,
//   tableContainerProps,
//   tableBodyProps,
//   sttProps,
//   isLoading,
//   infinitiLoad = false,
//   rowProps,
//   onRowHover,
//   selectedRowOnClick,
//   hideTableHead = false,
//   loadingType = 'skeleton',
//   hasBorder = false,
//   hasHeaderBackground = false,
//   groupByColumns = [],
//   stickyHeader = false,
//   tableFooter = null,
//   onLoadMore = () => undefined,
//   page = 1,
// }) => {};
