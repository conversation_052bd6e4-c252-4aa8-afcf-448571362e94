import styled from '@emotion/styled';

import Box from '@mui/material/Box';
import DialogActions, { dialogActionsClasses } from '@mui/material/DialogActions';
import DialogContent, { dialogContentClasses } from '@mui/material/DialogContent';
import DialogTitle, { dialogTitleClasses } from '@mui/material/DialogTitle';

import Dialog, { dialogClasses } from '@mui/material/Dialog';
import Icon from '../Icon';
import Typography from '../Typography';

export const StyledWrapperTitle = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  width: '100%',
  position: 'relative',
}));

export const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  [`&.${dialogTitleClasses.root}`]: {
    padding: 0,
  },
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.neutrals.N50.main,
}));

export const StyledCloseIcon = styled(Icon)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  right: 0,
  cursor: 'pointer',
}));

export const StyledDialog = styled(Dialog)(({ theme }) => ({
  [`& .${dialogClasses.paper}`]: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(2.25),
    width: 'fit-content',
    maxWidth: 'none',
  },
}));

export const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  [`&.${dialogContentClasses.root}`]: {
    padding: 0,
  },
}));

export const StyledDialogAction = styled(DialogActions)(({ theme }) => ({
  [`&.${dialogActionsClasses.root}`]: {
    justifyContent: 'center',
    padding: theme.spacing(2),
    paddingBottom: theme.spacing(3),
  },
}));
