import { Theme, useTheme } from '@mui/material';
import { dialogClasses, DialogProps } from '@mui/material/Dialog';
import { DialogActionsProps } from '@mui/material/DialogActions';
import { DialogContentProps } from '@mui/material/DialogContent';
import { DialogTitleProps } from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import React, { useId } from 'react';
import { ITypographyProps } from '../Typography';
import {
  StyledCloseIcon,
  StyledDialog,
  StyledDialogAction,
  StyledDialogContent,
  StyledDialogTitle,
  StyledTitle,
  StyledWrapperTitle,
} from './Modal.styled';

export type TModalProps = {
  isOpen?: boolean;
  isFullScreen?: boolean;
  handleClose: () => void;
  title?: string | React.ReactNode;
  titleProps?: ITypographyProps;
  children?: React.ReactNode;
  showCloseButton?: boolean;
  showDivider?: boolean;
  action?: React.ReactNode;
  contentProps?: DialogContentProps;
  dialogTitleProps?: DialogTitleProps;
  dialogActionsProps?: DialogActionsProps;
  dialogProps?: Omit<DialogProps, 'open'>;
  allowOutSideClick?: boolean;
};

export const Modal: React.FC<TModalProps> = ({
  isOpen = false,
  isFullScreen = false,
  handleClose,
  showCloseButton = true,
  showDivider = false,
  title,
  titleProps,
  children,
  action,
  contentProps,
  dialogTitleProps,
  dialogActionsProps,
  dialogProps,
  allowOutSideClick = true,
}) => {
  const theme = useTheme();
  const dataTestId = `custom-modal-${useId()}`;
  const showFullScreen = isFullScreen || useMediaQuery(theme.breakpoints.down('md'));
  const defaultDialogProps = {
    sx: (theme: Theme) => ({
      [`& .${dialogClasses.paper}`]: {
        padding: theme.spacing(3),
        margin: theme.spacing(3),
        height: 'fit-content',
        maxHeight: 'calc(100% - 48px)',
        borderRadius: theme.spacing(1.25),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(2),
        },
      },
    }),
  };
  return (
    <StyledDialog
      open={isOpen}
      onClose={() => (allowOutSideClick ? handleClose() : undefined)}
      aria-labelledby="responsive-dialog-title"
      data-testid={dataTestId}
      fullScreen={showFullScreen}
      fullWidth
      {...(dialogProps || defaultDialogProps)}
    >
      {(title || title === '') && (
        <StyledDialogTitle data-testid={`${dataTestId}-title`} id="responsive-dialog-title" {...dialogTitleProps}>
          <StyledWrapperTitle>
            {typeof title === 'string' ? (
              <StyledTitle variant="heading-xlarge-700" textTransform="uppercase" textAlign="center" {...titleProps}>
                {title}
              </StyledTitle>
            ) : (
              title
            )}
            {showCloseButton && <StyledCloseIcon variant="close" onClick={handleClose} size={3} />}
          </StyledWrapperTitle>
        </StyledDialogTitle>
      )}
      <StyledDialogContent className="Modal-content" dividers={showDivider} {...contentProps}>
        {children}
      </StyledDialogContent>
      {action && <StyledDialogAction {...dialogActionsProps}>{action}</StyledDialogAction>}
    </StyledDialog>
  );
};
