import Stack from '@mui/material/Stack';
import { Meta, StoryObj } from '@storybook/react';
import Modal, { TModalProps } from '.';

export default {
  title: 'Atoms/Modal',
  component: Modal,
  tags: ['autodocs'],
} satisfies Meta<TModalProps>;

type Story = StoryObj<typeof Modal>;

export const Default: Story = {
  args: { isOpen: true, title: 'title dialog' },
  render: args => (
    <Modal {...args}>
      <Stack>draw 1</Stack>
      <Stack>draw 2</Stack>
      <Stack>draw 3</Stack>
      <Stack>draw 4</Stack>
    </Modal>
  ),
};
