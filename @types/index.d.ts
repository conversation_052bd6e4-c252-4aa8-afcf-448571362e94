import { Theme as MTheme, PaletteColorOptions, Color } from '@mui/material/styles';
import '@emotion/react';

type TColorKey =
  | 'amber'
  | 'blue'
  | 'blueGrey'
  | 'brown'
  | 'common'
  | 'cyan'
  | 'deepOrange'
  | 'deepPurple'
  | 'green'
  | 'grey'
  | 'indigo'
  | 'lightBlue'
  | 'lightGreen'
  | 'lime'
  | 'orange'
  | 'pink'
  | 'purple'
  | 'red'
  | 'teal'
  | 'yellow';

type TColor = Record<Partial<TColorKey> | SimplePaletteColorOptions, Color>;

declare module '@mui/styles/defaultTheme' {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface DefaultTheme extends MTheme {}
}
declare module '@emotion/react' {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  export interface Theme extends MTheme {}
}

declare module '@mui/material/styles' {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface PaletteColor extends PaletteColorOptions {}
  interface TypographyVariants extends CustomTypographyVariants {}

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface Palette extends TColor {
    neutrals: {
      N10: PaletteColor;
      N20: PaletteColor;
      N30: PaletteColor;
      N40: PaletteColor;
      N50: PaletteColor;
      N60: PaletteColor;
      N70: PaletteColor;
      N80: PaletteColor;
      N90: PaletteColor;
      N100: PaletteColor;
      N110: PaletteColor;
      N120: PaletteColor;
      N130: PaletteColor;
      N140: PaletteColor;
      N150: PaletteColor;
      N160: PaletteColor;
      N170: PaletteColor;
      N180: PaletteColor;
      N190: PaletteColor;
      N200: PaletteColor;
      N210: PaletteColor;
      N220: PaletteColor;
      N230: PaletteColor;
      N240: PaletteColor;
      N250: PaletteColor;
      N260: PaletteColor;
      N270: PaletteColor;
      N280: PaletteColor;
      N290: PaletteColor;
      N300: PaletteColor;
      N310: PaletteColor;
      N320: PaletteColor;
      N330: PaletteColor;
      N340: PaletteColor;
      N350: PaletteColor;
      N360: PaletteColor;
      N370: PaletteColor;
      N380: PaletteColor;
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface PaletteOptions extends TColor {}
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    fontWeightSemibold: true;
    display_extraLarge: true;

    'heading-xxxlarge-700': true;
    'heading-xxlarge-700': true;
    'heading-xlarge-700': true;
    'heading-large-700': true;
    'heading-medium-700': true;
    'heading-small-700': true;
    'heading-xsmall-700': true;
    'heading-xxsmall-700': true;
    'heading-xxxsmall-700': true;

    'body-xxxlarge-400': true;
    'body-xxlarge-400': true;
    'body-xlarge-400': true;
    'body-large-400': true;
    'body-medium-400': true;
    'body-small-400': true;
    'body-extra-large-400': true;
    'heading-xmedium-700': true;
    'label-small-400': true;
  }
}
