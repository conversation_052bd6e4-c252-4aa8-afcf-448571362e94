// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { TBranch } from '@/lib/types/entities/branch';
import type { TRoles, TResource } from '@/lib/types/entities/resource';
import 'next-auth';
import 'next-auth/core/types';
import 'next-auth/jwt';

declare module 'next-auth' {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      id: string;
      fullname: string;
      displayName: string;
      phone: string;
      username: string;
      email: string;
      avatar: string;
      accessToken?: string;
      role: {
        id: string;
        name: TRoles;
      };
      isLogin?: boolean;
      resource: TResource[];
      branchChosen: TBranch;
    };
  }
}

declare module 'next-auth/jwt' {
  // eslint-disable-next-line no-shadow
  interface JWT {
    /** This is an example. You can find me in types/next-auth.d.ts */
    accessToken?: string;
    refreshToken?: string;
    provider?: string;
    image?: string;
    isLogin?: boolean;
    branchChosen: TBranch;
  }
}

declare module 'next-auth/core/types' {
  // eslint-disable-next-line no-shadow
  interface User {
    /** This is an example. You can find me in types/next-auth.d.ts */
    accessToken?: string;
    refreshToken?: string;
    provider?: string;
    image?: string;
    isLogin?: boolean;
    branchChosen: TBranch;
  }
}
