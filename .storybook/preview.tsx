import type { Preview } from '@storybook/react';
import React from 'react';
import { CustomProvider } from '../lib/provider/CustomProvider';
import ThemeRegistry from '../theme/ThemeRegistry';

const preview: Preview = {
  parameters: {
    nextjs: {
      appDirectory: true,
    },
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },
  decorators: [
    (Story, options) => { 

      return (
        // allow MUI theme to be used in storybook components
        <CustomProvider session={null}>
        <ThemeRegistry> 
          <div
            style={{
              width: '100%',
              minHeight:'100vh',
              height: '100%',
              display: 'flex', 
              backgroundColor: '#B4A093',
              padding: '8px'
            }}
          >
            <Story />
          </div>
        </ThemeRegistry>
        </CustomProvider>
      )
    },
  ],
}

export default preview
